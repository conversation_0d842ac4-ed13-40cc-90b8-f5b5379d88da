/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    AAvatar: typeof import('@arco-design/web-vue')['Avatar']
    ABadge: typeof import('@arco-design/web-vue')['Badge']
    ABreadcrumb: typeof import('@arco-design/web-vue')['Breadcrumb']
    ABreadcrumbItem: typeof import('@arco-design/web-vue')['BreadcrumbItem']
    AButton: typeof import('@arco-design/web-vue')['Button']
    ACard: typeof import('@arco-design/web-vue')['Card']
    ACheckbox: typeof import('@arco-design/web-vue')['Checkbox']
    ACol: typeof import('@arco-design/web-vue')['Col']
    ADatePicker: typeof import('@arco-design/web-vue')['DatePicker']
    ADescriptions: typeof import('@arco-design/web-vue')['Descriptions']
    ADescriptionsItem: typeof import('@arco-design/web-vue')['DescriptionsItem']
    ADivider: typeof import('@arco-design/web-vue')['Divider']
    ADoption: typeof import('@arco-design/web-vue')['Doption']
    ADrawer: typeof import('@arco-design/web-vue')['Drawer']
    ADropdown: typeof import('@arco-design/web-vue')['Dropdown']
    AEmpty: typeof import('@arco-design/web-vue')['Empty']
    AForm: typeof import('@arco-design/web-vue')['Form']
    AFormItem: typeof import('@arco-design/web-vue')['FormItem']
    AGrid: typeof import('@arco-design/web-vue')['Grid']
    AGridItem: typeof import('@arco-design/web-vue')['GridItem']
    AImage: typeof import('@arco-design/web-vue')['Image']
    AInput: typeof import('@arco-design/web-vue')['Input']
    AInputNumber: typeof import('@arco-design/web-vue')['InputNumber']
    AInputPassword: typeof import('@arco-design/web-vue')['InputPassword']
    AInputSearch: typeof import('@arco-design/web-vue')['InputSearch']
    ALayout: typeof import('@arco-design/web-vue')['Layout']
    ALayoutContent: typeof import('@arco-design/web-vue')['LayoutContent']
    ALayoutFooter: typeof import('@arco-design/web-vue')['LayoutFooter']
    ALayoutSider: typeof import('@arco-design/web-vue')['LayoutSider']
    ALink: typeof import('@arco-design/web-vue')['Link']
    AList: typeof import('@arco-design/web-vue')['List']
    AListItem: typeof import('@arco-design/web-vue')['ListItem']
    AListItemMeta: typeof import('@arco-design/web-vue')['ListItemMeta']
    AMenu: typeof import('@arco-design/web-vue')['Menu']
    AMenuItem: typeof import('@arco-design/web-vue')['MenuItem']
    AModal: typeof import('@arco-design/web-vue')['Modal']
    APagination: typeof import('@arco-design/web-vue')['Pagination']
    ARadio: typeof import('@arco-design/web-vue')['Radio']
    ARadioGroup: typeof import('@arco-design/web-vue')['RadioGroup']
    ARangePicker: typeof import('@arco-design/web-vue')['RangePicker']
    ARow: typeof import('@arco-design/web-vue')['Row']
    AScrollbar: typeof import('@arco-design/web-vue')['Scrollbar']
    ASelect: typeof import('@arco-design/web-vue')['Select']
    ASpace: typeof import('@arco-design/web-vue')['Space']
    ASpin: typeof import('@arco-design/web-vue')['Spin']
    AStatistic: typeof import('@arco-design/web-vue')['Statistic']
    ASubMenu: typeof import('@arco-design/web-vue')['SubMenu']
    ASwitch: typeof import('@arco-design/web-vue')['Switch']
    ATable: typeof import('@arco-design/web-vue')['Table']
    ATableColumn: typeof import('@arco-design/web-vue')['TableColumn']
    ATabPane: typeof import('@arco-design/web-vue')['TabPane']
    ATabs: typeof import('@arco-design/web-vue')['Tabs']
    ATag: typeof import('@arco-design/web-vue')['Tag']
    ATextarea: typeof import('@arco-design/web-vue')['Textarea']
    ATooltip: typeof import('@arco-design/web-vue')['Tooltip']
    ATree: typeof import('@arco-design/web-vue')['Tree']
    ATrigger: typeof import('@arco-design/web-vue')['Trigger']
    ATypographyTitle: typeof import('@arco-design/web-vue')['TypographyTitle']
    AUpload: typeof import('@arco-design/web-vue')['Upload']
    Chart: typeof import('./src/components/chart/index.vue')['default']
    DocPreview: typeof import('./src/components/docPreview/index.vue')['default']
    ExcelPreview: typeof import('./src/components/excelPreview/index.vue')['default']
    Icon: typeof import('./src/components/icon/index.vue')['default']
    PdfPreview: typeof import('./src/components/pdfPreview/index.vue')['default']
    RichTextEditor: typeof import('./src/components/richTextEditor/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    UploadCard: typeof import('./src/components/uploadCard/index.vue')['default']
    UploadMultiple: typeof import('./src/components/uploadMultiple/index.vue')['default']
  }
}

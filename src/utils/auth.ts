import { IRoutes } from '@/types/global'
import { RouteRecordRaw } from 'vue-router'

const TOKEN_KEY = 'token'

export const isLogin = (): boolean => !!localStorage.getItem(TOKEN_KEY)

export const getToken = () => localStorage.getItem(TOKEN_KEY)

export const setToken = (token: string) => localStorage.setItem(TOKEN_KEY, token)

export const clearToken = () => localStorage.removeItem(TOKEN_KEY)
// 首先把你需要动态路由的组件地址全部获取
export const modules = import.meta.glob('../pages/**/*.vue')
export const filterAsyncRouter = (asyncRouterMap: IRoutes[], routes: RouteRecordRaw[] = []): RouteRecordRaw[] => {
  asyncRouterMap.filter((route: IRoutes) => {
    if (!route.hidden) {
      if (!!route.redirect) {
        // 目录
        routes.push(filterRoute(route, true))
      } else {
        if (!!route.children) {
          // 不是目录且有children
          filterAsyncRouter(route.children, routes)
        } else {
          routes.push(filterRoute(route, false))
        }
      }
    }
    return true
  })
  return routes
}
/**
 *
 * @param route
 * @param isM 是不是目录
 * @returns
 */
const filterRoute = (route: IRoutes, isM: boolean): RouteRecordRaw => {
  const obj: any = {
    name: route.name,
    path: route.path,
    meta: {
      title: route.meta?.title ?? '',
      icon: route.meta?.icon === '#' ? '' : route.meta?.icon,
      noCache: route.meta?.noCache
    }
  }
  if (isM) {
    const children: any[] = []
    route.children.forEach((item) => {
      if (!item.hidden) children.push(filterRoute(item, false))
    })
    obj.children = children
  } else {
    obj.component = getViews(route.component)
  }
  return obj
}
/**
 * 树转数组
 * @param list
 * @param attr 获取的属性
 * @returns
 */
export const treeToArray = <K, T>(list: K[], attr?: T): T[] => {
  const results: T[] = []
  const fn = (list: any[]) => {
    list.map((item) => {
      if (!!item.children) {
        if (!!attr) {
          results.push(item[attr])
        } else {
          results.push(item)
        }
        fn(item.children)
      } else {
        if (!!attr) {
          results.push(item[attr])
        } else {
          results.push(item)
        }
      }
    })
  }
  fn(list)
  return results
}
/**
 * 筛选树
 * @param routes
 * @param filterNames
 * @returns
 */
export const treeFilter = (routes: any[], filterNames: string[]): any[] => {
  let results: any[] = []
  routes.map((item) => {
    if (filterNames.includes(item.name)) {
      let obj: any = {
        path: item.path,
        name: item.name,
        meta: item.meta
      }
      if (item.component) {
        obj.component = item.component
      }
      if (item.children) {
        obj.children = treeFilter(item.children, filterNames)
      }
      results.push(obj)
    }
  })
  return results
}
const getViews = (path: string) => {
  // 然后动态路由的时候这样来取
  return modules[`../pages/${path}.vue`]
}

export const setCookie = (key: string, value: string) => {
  const expdate = new Date()
  // expdate.setTime(expdate.getTime() + 60 * 60 * 1000)
  document.cookie = key + '=' + value + ';path=/'
}
export const getCookie = (key: string) => {
  const cookies = document.cookie.split(';')
  for (const c of cookies) {
    const cs = c.trim().split('=')
    if (cs[0] === key) return cs[1]
  }
  return ''
}
export const delCookie = (key: string) => {
  document.cookie = key + '=;expires=' + new Date(0).toString() + ';path=/'
}

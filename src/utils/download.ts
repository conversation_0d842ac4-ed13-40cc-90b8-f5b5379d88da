import { FileType } from '@/types/global'
import axios from 'axios'
import download from 'downloadjs'
import { getToken, isLogin } from '@/utils/auth'

const downLoad = async (fileName: string) => {
  if (!isLogin()) return
  const res = await axios({
    url: `${import.meta.env.VITE_API_URL}/common/file/download/${fileName}`,
    headers: {
      Authorization: 'Bearer ' + getToken()
    },
    method: 'get',
    responseType: 'blob'
  })
  download(res.data, fileName)
}

export default downLoad

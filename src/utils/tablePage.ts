import { PaginationProps } from '@arco-design/web-vue'

export const tablePage = (opt: any) => {
  let { api, query: queryParams, _query, handleFun, isInit = true } = opt
  // 加载
  let loading = ref<boolean>(false)
  // 单、多选
  let selectedRows = ref<any[]>([])
  // table数据
  let rows = ref<any[]>([])
  // 分页
  let pagination = ref<PaginationProps>({
    pageSize: 20,
    showTotal: true,
    showPageSize: true,
    pageSizeOptions: [10, 20, 50, 100, 500],
    size: 'small',
    total: 0, // 数据总数
    defaultCurrent: 1,
    current: 1 // 当前的页数
  })
  const pageChange = (page: number) => {
    pagination.value.current = page
    getListData()
  }
  const pageSizeChange = (pageSize: number) => {
    pagination.value.pageSize = pageSize
    pagination.value.current = 1
    getListData()
  }
  const getListData = async () => {
    try {
      loading.value = true
      const res = await api({ ...queryParams.value, pageNum: pagination.value.current!, pageSize: pagination.value.pageSize! })
      if (res) {
        rows.value = res.rows || res.data || []
        pagination.value.total = res?.total
        pagination.value.current = queryParams.value?.pageNum || 1
        // 回调处理
        if (handleFun) handleFun(res)
        loading.value = false
      }
    } catch (error) {
      loading.value = false
    }
  }
  // 查询
  const query = () => {
    if (queryParams.value.hasOwnProperty('pageNum')) queryParams.value.pageNum = 1
    getListData()
  }
  // 重置参数  可搭配特殊参数处理再搜索
  const reset = () => {
    resetQuery()
    getListData()
  }
  const resetQuery = () => {
    queryParams.value = { ..._query }
  }
  // 初始化
  if (isInit) {
    onMounted(() => {
      getListData()
    })
  }

  // 多选的id
  let ids = ref<string[]>([])

  return {
    ids,
    rows,
    query,
    reset,
    resetQuery,
    loading,
    pagination,
    selectedRows,
    getListData,
    pageChange,
    pageSizeChange
  }
}

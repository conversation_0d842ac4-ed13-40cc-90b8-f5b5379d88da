import Decimal from 'decimal.js'

type TargetContext = '_self' | '_parent' | '_blank' | '_top'

/**
 * 解析blob响应内容并下载
 * @param {*} res blob响应内容
 * @param {String} mimeType MIME类型
 */
export const resolveBlob = (res: any, mimeType: string, fileName?: string) => {
  const aLink = document.createElement('a')
  const blob = new Blob([res.data], { type: mimeType })
  if (!fileName) {
    //从response的headers中获取filename, 后端response.setHeader("Content-disposition", "attachment; filename=xxxx.docx") 设置的文件名;
    const pattern = new RegExp('filename=([^;]+\\.[^\\.;]+);*')
    const contentDisposition = decodeURI(res.headers['content-disposition'])
    const result = pattern.exec(contentDisposition)
    fileName = result ? result[1] : ''
    fileName = fileName.replace(/\"/g, '')
  }
  aLink.href = URL.createObjectURL(blob)
  aLink.setAttribute('download', fileName) // 设置下载文件名称
  document.body.appendChild(aLink)
  aLink.click()
  document.body.removeChild(aLink)
}
/**
 * file对象转ArrayBuffer
 * @param file
 * @param cb
 */
export const fileToBuffer = (file: File, cb: (buf: ArrayBuffer) => void) => {
  const fr = new FileReader()

  fr.readAsArrayBuffer(file)
  fr.addEventListener(
    'loadend',
    (e) => {
      const buf = e.target!.result
      cb(buf as ArrayBuffer)
    },
    false
  )
}
export const openWindow = (url: string, opts?: { target?: TargetContext; [key: string]: any }) => {
  const { target = '_blank', ...others } = opts || {}
  window.open(
    url,
    target,
    Object.entries(others)
      .reduce((preValue: string[], curValue) => {
        const [key, value] = curValue
        return [...preValue, `${key}=${value}`]
      }, [])
      .join(',')
  )
}

export const regexUrl = new RegExp(
  '^(?!mailto:)(?:(?:http|https|ftp)://)(?:\\S+(?::\\S*)?@)?(?:(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[0-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]+-?)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]+-?)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))|localhost)(?::\\d{2,5})?(?:(/|\\?|#)[^\\s]*)?$',
  'i'
)
/**
 * @desc 格式化手机号码
 * @param number 手机号码
 **/
export const formatNumber = (number?: string) => {
  // return number?.length === 11 ? number?.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2') : number
  return number
}
/**
 * 四位添加空格
 * @param value 需要转换的字符串
 * @param isNumber 是否是纯数字
 * @returns
 */
export const spaceFormat = (value: string, isNumber?: boolean) => {
  return !!isNumber
    ? value
        .replace(/\s/g, '')
        .replace(/[^\d]/g, '')
        .replace(/(\d{4})(?=\d)/g, '$1 ')
    : value.replace(/\s/g, '').replace(/(.{4})(?=.)/g, '$1 ')
}
/**
 * 去空格
 * @param value
 * @returns
 */
export const trim = (value: string) => {
  let result
  result = value.replace(/(^\s+)|(\s+$)/g, '')
  result = result.replace(/\s/g, '')
  return result
}
/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 */

export const handleTree = (data: any[], id: string, parentId?: string, children?: any[]) => {
  let config = {
    id: id || 'id',
    parentId: parentId || 'parentId',
    childrenList: children || 'children'
  }

  var childrenListMap = {}
  var nodeIds = {}
  var tree = []

  for (let d of data) {
    let parentId = d[config.parentId] as keyof typeof childrenListMap
    // @ts-ignore
    if (childrenListMap[parentId] == null) {
      // @ts-ignore
      childrenListMap[parentId] = []
    }
    // @ts-ignore
    nodeIds[d[config.id]] = d
    // @ts-ignore
    childrenListMap[parentId].push(d)
  }

  for (let d of data) {
    let parentId = d[config.parentId]
    // @ts-ignore
    if (nodeIds[parentId] == null) {
      tree.push(d)
    }
  }

  for (let t of tree) {
    adaptToChildrenList(t)
  }
  // @ts-ignore
  function adaptToChildrenList(o) {
    // @ts-ignore
    if (childrenListMap[o[config.id]] !== null) {
      // @ts-ignore
      o[config.childrenList] = childrenListMap[o[config.id]]
    }
    // @ts-ignore
    if (o[config.childrenList]) {
      // @ts-ignore
      for (let c of o[config.childrenList]) {
        adaptToChildrenList(c)
      }
    }
  }
  return tree
}
/**
 * 秒转xx:xx:xx
 * @param durationSeconds
 * @returns
 */
export const formatDuration = (durationSeconds?: number): string => {
  if (!durationSeconds) return '00:00:00'
  const hours = Math.floor(durationSeconds / 60 / 60)
  const minutes = Math.floor((durationSeconds - hours * 60 * 60) / 60)
  const seconds = durationSeconds - hours * 60 * 60 - minutes * 60
  const formatResult = `${hours < 10 ? '0' + hours : hours}:${minutes < 10 ? '0' + minutes : minutes}:${seconds < 10 ? '0' + seconds : seconds}`
  return formatResult
}
/** 计算时间差
 * @param {string} d1
 * @param {string} d2
 * @returns
 */
export const timeDifference = (d1: string, d2: string) => {
  //如果时间格式是正确的，那下面这一步转化时间格式就可以不用了
  var dateBegin = new Date(d1.replace(/-/g, '/')) //将-转化为/，使用new Date
  var dateEnd = new Date(d2.replace(/-/g, '/')) //将-转化为/，使用new Date
  var dateDiff = dateEnd.getTime() - dateBegin.getTime() //时间差的毫秒数
  var dayDiff = Math.floor(dateDiff / (24 * 3600 * 1000)) //计算出相差天数
  var leave1 = dateDiff % (24 * 3600 * 1000) //计算天数后剩余的毫秒数
  var hours = Math.floor(leave1 / (3600 * 1000)) //计算出小时数
  //计算相差分钟数
  var leave2 = leave1 % (3600 * 1000) //计算小时数后剩余的毫秒数
  var minutes = Math.floor(leave2 / (60 * 1000)) //计算相差分钟数
  //计算相差秒数
  var leave3 = leave2 % (60 * 1000) //计算分钟数后剩余的毫秒数
  var seconds = Math.round(leave3 / 1000)
  // console.log(' 相差 ' + dayDiff + '天 ' + hours + '小时 ' + minutes + ' 分钟' + seconds + ' 秒')
  // console.log(dateDiff + '时间差的毫秒数', dayDiff + '计算出相差天数', leave1 + '计算天数后剩余的毫秒数', hours + '计算出小时数', minutes + '计算相差分钟数', seconds + '计算相差秒数')
  let str = ''
  if (dayDiff > 0) str += dayDiff < 10 ? `0${dayDiff}天` : `${dayDiff}天`
  str += hours < 10 ? `0${hours}小时` : `${hours}小时`
  str += minutes < 10 ? `0${minutes}分钟` : `${minutes}分钟`
  return str
}

/**
 * 数字相加
 * @param {*} addend1
 * @param {*} addend2
 * @returns
 */
export const accAdd = (addend1: number, addend2: number) => {
  return new Decimal(addend1).plus(addend2).toNumber()
}

/**
 * 数字相减
 * @param {*} minuend
 * @param {*} subtrahend
 * @returns
 */
export const accSub = (minuend: number, subtrahend: number) => {
  return new Decimal(minuend).minus(subtrahend).toNumber()
}

/**
 * 数字相乘
 * @param {*} multiplicand
 * @param {*} multiplier
 * @returns
 */
export const accMul = (multiplicand: number, multiplier: number) => {
  return new Decimal(multiplicand).mul(multiplier).toNumber()
}

/**
 * 数字相除
 * @param {*} dividend
 * @param {*} divisor
 * @returns
 */
export const accDiv = (dividend: number, divisor: number) => {
  return new Decimal(dividend).div(divisor).toNumber()
}

const useDate = () => {
  const now = new Date() //当前日期
  const nowDayOfWeek = now.getDay() //今天是本周的第几天
  const nowDay = now.getDate() //当前日
  const nowMonth = now.getMonth() //当前月
  let nowYear = now.getFullYear() //当前年
  nowYear += nowYear < 2000 ? 1900 : 0 //
  let lastMonthDate = new Date() //上月日期
  lastMonthDate.setDate(1)
  lastMonthDate.setMonth(lastMonthDate.getMonth() - 1)
  const lastYear = lastMonthDate.getFullYear()
  const lastMonth = lastMonthDate.getMonth()
  //获得某月的天数
  const getMonthDays = (myMonth: number) => {
    const monthStartDate = new Date(nowYear, myMonth, 1).getTime()
    const monthEndDate = new Date(nowYear, myMonth + 1, 1).getTime()
    const days = (monthEndDate - monthStartDate) / (1000 * 60 * 60 * 24)
    return days
  }
  //获得本季度的开始月份
  const getQuarterStartMonth = () => {
    var quarterStartMonth = 0
    if (nowMonth < 3) {
      quarterStartMonth = 0
    }
    if (2 < nowMonth && nowMonth < 6) {
      quarterStartMonth = 3
    }
    if (5 < nowMonth && nowMonth < 9) {
      quarterStartMonth = 6
    }
    if (nowMonth > 8) {
      quarterStartMonth = 9
    }
    return quarterStartMonth
  }
  //获得本周的开始日期
  const weekStartDate = new Date(nowYear, nowMonth, nowDay - nowDayOfWeek)
  //获得本周的结束日期
  const weekEndDate = new Date(nowYear, nowMonth, nowDay + (6 - nowDayOfWeek))
  //获得上周的开始日期
  const lastWeekStartDate = new Date(nowYear, nowMonth, nowDay - nowDayOfWeek - 7)
  //获得上周的结束日期
  const lastWeekEndDate = new Date(nowYear, nowMonth, nowDay - nowDayOfWeek - 1)
  //获得本月的开始日期
  const monthStartDate = new Date(nowYear, nowMonth, 1)
  //获得本月的结束日期
  const monthEndDate = new Date(nowYear, nowMonth, getMonthDays(nowMonth))
  //获得上月开始时间
  const lastMonthStartDate = new Date(lastYear, lastMonth, 1)
  //获得上月结束时间
  const lastMonthEndDate = new Date(lastYear, lastMonth, getMonthDays(lastMonth))
  //获得本季度的开始日期
  const quarterStartDate = new Date(nowYear, getQuarterStartMonth(), 1)
  //或的本季度的结束日期
  const quarterEndDate = new Date(nowYear, getQuarterStartMonth() + 2, getMonthDays(getQuarterStartMonth() + 2))
  return {
    weekStartDate,
    weekEndDate,
    lastWeekStartDate,
    lastWeekEndDate,
    monthStartDate,
    monthEndDate,
    lastMonthStartDate,
    lastMonthEndDate,
    quarterStartDate,
    quarterEndDate
  }
}

export default useDate

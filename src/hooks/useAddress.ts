import cityPicker from '@/config/pickerCity'
import { Options } from '@/types/global'

const useAddress = () => {
  const provinces = ref<Options[]>(
    cityPicker.map((item: any) => {
      return {
        label: item.text as string,
        value: item.value as number
      }
    })
  )
  const cities = ref<Options[]>([])
  const setCities = (provinceCode: number) => {
    cities.value =
      cityPicker
        .find((item) => item.value === provinceCode)
        ?.children.map((item: any) => {
          return {
            label: item.text as string,
            value: item.value as number
          }
        }) ?? []
  }
  const areas = ref<Options[]>([])
  const setAreas = (provinceCode: number, cityCode: number) => {
    areas.value =
      cityPicker
        .find((item) => item.value === provinceCode)
        ?.children.find((item) => item.value === cityCode)
        ?.children.map((item: any) => {
          return {
            label: item.text as string,
            value: item.value as number
          }
        }) ?? []
  }
  return {
    provinces,
    cities,
    setCities,
    areas,
    setAreas
  }
}

export default useAddress

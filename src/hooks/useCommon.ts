import { Options } from '@/types/global'
import { reqGetOptions, reqGetAddressTips } from '@/api/apiCommon'

const useCommon = () => {
  // 审核状态
  const approveStateOptions: Options[] = [
    {
      label: '审核中',
      value: 0,
      color: 'blue'
    },
    {
      label: '审核通过',
      value: 1,
      color: 'green'
    },
    {
      label: '审核拒绝',
      value: -1,
      color: 'red'
    }
  ]
  // 上架状态
  const saleStateOptions: Options[] = [
    {
      label: '已上架',
      value: 1,
      color: 'green'
    },
    {
      label: '未上架',
      value: -1,
      color: 'red'
    }
  ]
  // 删除状态
  const delStateOptions: Options[] = [
    {
      label: '未删除',
      value: 1,
      color: 'blue'
    },
    {
      label: '已删除',
      value: -1,
      color: 'red'
    }
  ]
  // 用户状态
  const userStateOptions: Options[] = [
    {
      label: '启用',
      value: 1,
      color: 'blue'
    },
    {
      label: '禁用',
      value: -1,
      color: 'red'
    }
  ]
  // 订单状态
  const orderStateOptions: { label: string; value: number; color: string }[] = [
    {
      label: '待付款',
      value: 0,
      color: 'gray'
    },
    {
      label: '待发货',
      value: 1,
      color: 'orange'
    },
    {
      label: '待收货/待核销',
      value: 2,
      color: 'blue'
    },
    {
      label: '已收货/已核销',
      value: 3,
      color: 'green'
    },
    {
      label: '退款/售后',
      value: 4,
      color: 'green'
    },
    {
      label: '已取消',
      value: -1,
      color: 'red'
    }
  ]
  // 订单配送方式
  const orderDeliveryTypeOptions: Options[] = [
    {
      label: '快递发货',
      value: 1,
      color: 'blue'
    },
    {
      label: '线下核销',
      value: 2,
      color: 'green'
    }
  ]
  // 订单付款方式
  const orderPayTypeOptions: Options[] = [
    {
      label: '微信支付',
      value: 1,
      color: 'blue'
    }
  ]
  // 店铺状态
  const storeBusinessStateOptions: Options[] = [
    {
      label: '正常营业',
      value: 1,
      color: 'blue'
    },
    {
      label: '暂停营业',
      value: -1,
      color: 'red'
    }
  ]
  // 店铺公告状态
  const announcementStateOptions: Options[] = [
    {
      label: '已读',
      value: 1,
      color: 'blue'
    },
    {
      label: '未读',
      value: -1,
      color: 'red'
    }
  ]
  // banner链接类型
  const bannerLinkTypeOptions: Options[] = [
    {
      label: '店铺分类',
      value: 1,
      color: 'cyan'
    },
    {
      label: '店铺详情',
      value: 2,
      color: 'blue'
    },
    {
      label: '外部链接',
      value: 3,
      color: 'gray'
    },
    {
      label: '营销活动',
      value: 4,
      color: 'purple'
    }
    // {
    //   label: '自定义内容',
    //   value: 5,
    //   color: 'purple'
    // }
  ]
  // 店铺
  const storeOptions = ref<Options[]>([])
  const initStoreOptions = async ({ storeCategoryId, storeSubCategoryId }: { storeCategoryId?: string; storeSubCategoryId?: string } = { storeCategoryId: undefined, storeSubCategoryId: undefined }) => {
    try {
      const { data } = await reqGetOptions('store', { storeCategoryId, storeSubCategoryId })
      storeOptions.value = data as Options[]
    } catch (error) {}
  }
  // 所有店铺分类
  const allStoreCategoryOptions = ref<Options[]>([])
  const initAllStoreCategoryOptions = async () => {
    try {
      const { data } = await reqGetOptions('allStoreCategory')
      allStoreCategoryOptions.value = data as Options[]
    } catch (error) {}
  }
  // 店铺所属行业
  const storeMccOptions = ref<Options[]>([])
  const initStoreMccOptions = async () => {
    try {
      const { data } = await reqGetOptions('storeMccCodes')
      storeMccOptions.value = data as Options[]
    } catch (error) {}
  }
  // 店铺分类
  const storeCategoryOptions = ref<Options[]>([])
  const initStoreCategoryOptions = async () => {
    try {
      const { data } = await reqGetOptions('storeCategory')
      storeCategoryOptions.value = data as Options[]
    } catch (error) {}
  }
  const storeSubCategoryOptions = ref<Options[]>([])
  const initStoreSubCategoryOptions = async (parentId: string | undefined) => {
    try {
      storeSubCategoryOptions.value = []
      if (parentId) {
        const { data } = await reqGetOptions('storeSubCategory', { parentId })
        storeSubCategoryOptions.value = data as Options[]
      }
    } catch (error) {}
  }
  // 商品类型
  const commodityTypeOptions: Options[] = [
    {
      label: '实物商品',
      value: 1,
      color: 'blue'
    },
    {
      label: '电子卡券',
      value: 2,
      color: 'green'
    }
  ]
  // 商品分类
  const commodityCategoryOptions = ref<Options[]>([])
  const initCommodityCategoryOptions = async () => {
    try {
      const { data } = await reqGetOptions('commodityCategory')
      commodityCategoryOptions.value = data as Options[]
    } catch (error) {}
  }
  // 活动
  const activityOptions = ref<Options[]>([])
  const initActivityOptions = async (storeId: string) => {
    try {
      activityOptions.value = []
      if (!storeId) return
      const { data } = await reqGetOptions('activity', { storeId })
      activityOptions.value = data as Options[]
    } catch (error) {}
  }
  const addressTipsLoading = ref<boolean>(false)
  const addressTips = ref<{ label: string; value: string; address: string; longitude: number; latitude: number }[]>([])
  const getAddressTips = async (data: any) => {
    try {
      addressTips.value = []
      addressTipsLoading.value = true
      const { data: resData } = await reqGetAddressTips(data)
      addressTips.value = resData as any[]
      addressTipsLoading.value = false
    } catch (error) {
      addressTipsLoading.value = false
    }
  }
  const roleOptions = ref<Options[]>([])
  const initRoleOptions = async () => {
    try {
      const { data } = await reqGetOptions('platformAccountRole')
      roleOptions.value = data as Options[]
    } catch (error) {}
  }
  const bankCodeOptions = ref<Options[]>([])
  const initBankCodeOptions = async () => {
    try {
      const { data } = await reqGetOptions('bankCodes')
      bankCodeOptions.value = data as Options[]
    } catch (error) {}
  }
  const bankBranchIdOptions = ref<Options[]>([])
  const initBankBranchIdOptions = async ({ bankCode, bankBranchName }: { bankCode: string; bankBranchName?: string }) => {
    try {
      bankBranchIdOptions.value = []
      const { data } = await reqGetOptions('bankBranchIds', { bankCode, bankBranchName })
      bankBranchIdOptions.value = data as Options[]
    } catch (error) {}
  }
  return {
    approveStateOptions,
    saleStateOptions,
    delStateOptions,
    userStateOptions,
    orderStateOptions,
    orderDeliveryTypeOptions,
    orderPayTypeOptions,
    storeBusinessStateOptions,
    announcementStateOptions,
    bannerLinkTypeOptions,
    storeOptions,
    initStoreOptions,
    allStoreCategoryOptions,
    initAllStoreCategoryOptions,
    storeMccOptions,
    initStoreMccOptions,
    storeCategoryOptions,
    initStoreCategoryOptions,
    storeSubCategoryOptions,
    initStoreSubCategoryOptions,
    commodityTypeOptions,
    commodityCategoryOptions,
    initCommodityCategoryOptions,
    activityOptions,
    initActivityOptions,
    addressTipsLoading,
    addressTips,
    getAddressTips,
    roleOptions,
    initRoleOptions,
    bankCodeOptions,
    initBankCodeOptions,
    bankBranchIdOptions,
    initBankBranchIdOptions
  }
}

export default useCommon

<script lang="ts" setup>
import useCommon from '@/hooks/useCommon'

const ListCom = defineAsyncComponent(() => import('./list.vue'))

const { orderStateOptions } = useCommon()
</script>

<template>
  <div class="page-container">
    <a-tabs type="rounded" lazy-load destroy-on-hide>
      <a-tab-pane title="全部">
        <list-com></list-com>
      </a-tab-pane>
      <a-tab-pane v-for="state in orderStateOptions" :key="state.value" :title="state.label">
        <list-com :type="state.value"></list-com>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<style lang="scss" scoped>
:deep(.arco-tabs) {
  height: 100%;
}
:deep(.arco-tabs-nav) {
  background-color: var(--color-bg-2);
  padding: 16px 10px 10px;
}
:deep(.arco-tabs-content) {
  padding-top: 0;
  height: calc(100% - 58px);
  .arco-tabs-content-list {
    height: 100%;
    .arco-tabs-pane {
      height: 100%;
    }
  }
}
</style>

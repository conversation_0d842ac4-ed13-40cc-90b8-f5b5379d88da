<script lang="ts" setup>
import { useTemplateRef } from 'vue'
import { formatNumber } from '@/utils'
import useCommon from '@/hooks/useCommon'
import useHooks from './hooks'
import { Modal, Notification, TableData } from '@arco-design/web-vue'
import dayjs from 'dayjs'

const props = defineProps<{
  type?: number
}>()

const { orderStateOptions, orderDeliveryTypeOptions, orderPayTypeOptions } = useCommon()
const { loading, queryParams, pagination, rows, selectedId, selectedRow, selectedIds, selectAll, rowSelect, rowClick, query, reset, pageChange, pageSizeChange, exports } = useHooks({
  type: props.type
})
const handleExport = () => {
  try {
    Modal.warning({
      title: '提示',
      content: () => h('div', { class: 'text-center' }, `确定导出所有订单记录？`),
      maskClosable: false,
      escToClose: false,
      hideCancel: false,
      cancelButtonProps: { type: 'outline' },
      onBeforeOk: async (done: (closed: boolean) => void): Promise<void | boolean> => {
        try {
          await exports()
          Notification.success({
            title: '操作提示',
            content: `已导出所有订单记录`,
            duration: 1500
          })
          done(true)
        } catch (error) {
          done(false)
        }
      }
    })
  } catch (error) {}
}
onMounted(() => {
  pageChange(1)
})
</script>

<template>
  <div class="pt-[18px] h-full">
    <div class="h-full flex flex-col gap-[18px]">
      <a-card :bordered="false">
        <a-form :model="queryParams" auto-label-width>
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item show-colon label="订单编号" field="orderNo">
                <a-input v-model="queryParams.orderNo" :placeholder="`${$inputPlaceholder}订单编号`" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="配送方式" field="deliveryType">
                <a-select v-model="queryParams.deliveryType" :options="orderDeliveryTypeOptions" :placeholder="`${$selectPlaceholder}配送方式`" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="付款方式" field="payType">
                <a-select v-model="queryParams.payType" :options="orderPayTypeOptions" :placeholder="`${$selectPlaceholder}付款方式`" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="商品名称" field="commodityName">
                <a-input v-model="queryParams.commodityName" :placeholder="`${$inputPlaceholder}商品名称`" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="店铺编号" field="storeNo">
                <a-input v-model="queryParams.storeNo" :placeholder="`${$inputPlaceholder}店铺编号`" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="店铺名称" field="storeName">
                <a-input v-model="queryParams.storeName" :placeholder="`${$inputPlaceholder}店铺名称`" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="用户编号" field="userNo">
                <a-input v-model="queryParams.userNo" :placeholder="`${$inputPlaceholder}用户编号`" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="用户昵称" field="nickname">
                <a-input v-model="queryParams.nickname" :placeholder="`${$inputPlaceholder}用户昵称`" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="手机号" field="mobile">
                <a-input-number hide-button v-model="queryParams.mobile" :placeholder="`${$inputPlaceholder}手机号`" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="付款金额">
                <a-row align="center" class="w-full">
                  <a-col :span="11">
                    <a-form-item no-style field="minAmount">
                      <a-input-number hide-button v-model="queryParams.minAmount" :placeholder="`${$inputPlaceholder}最小金额`" allow-clear>
                        <template #suffix>元</template>
                      </a-input-number>
                    </a-form-item>
                  </a-col>
                  <a-col :span="2" class="text-center">-</a-col>
                  <a-col :span="11">
                    <a-form-item no-style field="maxAmount">
                      <a-input-number hide-button v-model="queryParams.maxAmount" :placeholder="`${$inputPlaceholder}最大金额`" allow-clear>
                        <template #suffix>元</template>
                      </a-input-number>
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="下单时间" field="createTime">
                <a-range-picker v-model="queryParams.createTime" :placeholder="[`${$selectPlaceholder}开始日期`, `${$selectPlaceholder}结束日期`]" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item hide-label>
                <a-space :size="18">
                  <a-button type="primary" @click="pageChange(1)">
                    <template #icon>
                      <icon-search />
                    </template>
                    查询
                  </a-button>
                  <a-button type="outline" @click="reset">
                    <template #icon>
                      <icon-refresh />
                    </template>
                    重置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <a-card :bordered="false" class="flex-1 overflow-y-hidden" :body-style="{ height: '100%' }">
        <a-row class="mb-[12px]">
          <a-col :span="16">
            <a-space>
              <a-button :disabled="!rows.length" type="primary" @click="handleExport">
                <template #icon>
                  <icon-export />
                </template>
                导出
              </a-button>
            </a-space>
          </a-col>
        </a-row>
        <!-- :selected-keys="selectedIds"
          :row-selection="{ type: 'checkbox', showCheckedAll: true, onlyCurrent: true }"
          @select-all="selectAll"
          @select="rowSelect"
          @row-click="rowClick" -->
        <a-table size="large" row-key="id" :loading="loading" :pagination="false" :data="rows" :bordered="{ cell: true }" :scroll="{ y: 'calc(100% - 96px)' }">
          <template #columns>
            <a-table-column align="center" title="序号" :width="80">
              <template #cell="{ rowIndex }">
                {{ pagination.pageSize! * (pagination.current! - 1) + rowIndex + 1 }}
              </template>
            </a-table-column>
            <a-table-column align="center" title="订单信息" :width="300" ellipsis tooltip>
              <template #cell="{ record }">
                <a-space>
                  <div class="text-left">
                    <p>订单编号：{{ record.orderNo }}</p>
                    <p class="pt-1">下单时间：{{ record.createTime }}</p>
                    <p class="pt-1">备注信息：{{ record.remark || '-' }}</p>
                  </div>
                </a-space>
              </template>
            </a-table-column>
            <a-table-column align="center" title="商品信息" :width="300">
              <template #cell="{ record }">
                <div>
                  <div v-for="(commodity, index) in record.commodities" :key="index">
                    <a-space>
                      <a-avatar shape="square" :size="32">
                        <img :src="commodity.cover" />
                      </a-avatar>
                      <div class="text-left">
                        <p>商品名称：{{ commodity.name }}</p>
                        <p class="pt-1">商品数量：{{ commodity.count }} {{ commodity.unit }}</p>
                      </div>
                    </a-space>
                  </div>
                </div>
              </template>
            </a-table-column>
            <a-table-column align="center" title="用户信息" :width="300" ellipsis tooltip>
              <template #cell="{ record }">
                <a-space>
                  <a-avatar :size="32">
                    <img :src="record.user.avatar" />
                  </a-avatar>
                  <div class="text-left">
                    <p>用户编号：{{ record.user.no }}</p>
                    <p class="pt-1">用户昵称：{{ record.user.nickname }}</p>
                    <p class="pt-1">用户手机：{{ formatNumber(record.user.mobile) }}</p>
                  </div>
                </a-space>
              </template>
            </a-table-column>
            <a-table-column align="center" title="店铺信息" :width="300" ellipsis tooltip>
              <template #cell="{ record }">
                <a-space>
                  <a-avatar :size="32">
                    <img :src="record.store.logo" />
                  </a-avatar>
                  <div class="text-left">
                    <p>店铺编号：{{ record.store.no }}</p>
                    <p class="pt-1 w-[210px] truncate">店铺名称：{{ record.store.name }}</p>
                    <p class="pt-1">联系人：{{ record.store.contactName }}</p>
                    <p class="pt-1">联系电话：{{ formatNumber(record.store.contactNumber) }}</p>
                  </div>
                </a-space>
              </template>
            </a-table-column>
            <a-table-column align="center" title="支付信息" :width="300">
              <template #cell="{ record }">
                <a-space>
                  <div class="text-left">
                    <p>商品金额：{{ record.commodityAmount.toFixed(2) }} 元</p>
                    <p class="pt-1">优惠金额：{{ record.reduceAmount.toFixed(2) }} 元</p>
                    <p class="pt-1">付款金额：{{ record.payAmount.toFixed(2) }} 元</p>
                    <p class="pt-1">付款方式：{{ orderPayTypeOptions.find((item) => item.value === record.payType)?.label ?? '' }}</p>
                    <p class="pt-1">支付状态：{{ record.payState === 0 ? '待付款' : record.payState === -1 ? '支付失败' : record.payState === 1 ? '支付成功' : '' }}</p>
                    <p class="pt-1 w-[210px]">付款时间：{{ record.payTime }}</p>
                  </div>
                </a-space>
              </template>
            </a-table-column>
            <a-table-column align="center" title="收货信息" :width="300">
              <template #cell="{ record }">
                <a-space>
                  <div class="text-left">
                    <p>
                      配送方式：
                      <a-tag :color="orderDeliveryTypeOptions.find((item) => item.value === record.deliveryType)?.color ?? ''">
                        {{ orderDeliveryTypeOptions.find((item) => item.value === record.deliveryType)?.label ?? '' }}
                      </a-tag>
                    </p>
                    <p class="pt-1">收货人：{{ record.deliveryType === 1 ? record.delivery?.userName : '-' }}</p>
                    <p class="pt-1">手机号码：{{ record.deliveryType === 1 ? record.delivery?.contactNumber : '-' }}</p>
                    <p class="pt-1 w-[210px] truncate">收货地址：{{ record.deliveryType === 1 ? record.delivery?.address : '-' }}</p>
                  </div>
                </a-space>
              </template>
            </a-table-column>
            <a-table-column align="center" title="订单状态" :width="250">
              <template #cell="{ record }">
                <a-tag v-if="record.state === -1" color="gray">已取消</a-tag>
                <a-tag v-if="record.state === -2" color="gray">已关闭（已退款）</a-tag>
                <a-tag v-if="record.state === -3" color="gray">售后中</a-tag>
                <a-tag v-if="record.state === 0" color="red">待付款</a-tag>
                <a-tag v-if="record.state === 1" color="orange">
                  {{ record.afterSaleState === -1 ? '待发货' : '待发货（售后申请被商家驳回）' }}
                </a-tag>
                <a-tag v-if="record.state === 2" color="orange">
                  {{ record.afterSaleState === -1 ? (record.deliveryType === 1 ? '待收货' : '待核销') : record.deliveryType === 1 ? '待收货（售后申请被商家驳回）' : '待核销（售后申请被商家驳回）' }}
                </a-tag>
                <a-tag v-if="record.state === 3" color="blue">{{ record.commentState === -1 ? '待评价' : '已完成' }}</a-tag>
              </template>
            </a-table-column>
            <a-table-column align="center" title="售后" :width="400">
              <template #cell="{ record }">
                <a-space v-if="record.afterSaleState === 1">
                  <div class="text-left">
                    <p class="w-[300px] truncate">售后原因：{{ record.afterSale?.reason }}</p>
                    <p class="pt-1">审核状态：{{ record.afterSale?.approveState === 1 ? '审核通过' : record.afterSale?.approveState === -1 ? '审核拒绝' : '待审核' }}</p>
                    <p v-if="record.afterSale?.approveState === -1" class="pt-1">拒绝原因：{{ record.afterSale?.approveReason }}</p>
                    <p v-if="record.afterSale?.approveState !== 0" class="pt-1">审核时间：{{ dayjs(record.afterSale?.approveTime).format('YYYY-MM-DD HH:mm:ss') }}</p>
                    <template v-if="record.afterSale?.approveState === 1">
                      <p class="pt-1">退款金额：{{ record.afterSale?.refundAmount?.toFixed(2) }} 元</p>
                      <p class="pt-1">退款状态：{{ record.afterSale?.refundState === -1 ? '未退款' : record.afterSale?.refundState === 0 ? '退款中' : '已退款' }}</p>
                      <p v-if="record.refundState === 1" class="pt-1">退款时间：{{ dayjs(record.afterSale?.refundTime).format('YYYY-MM-DD HH:mm:ss') }}</p>
                    </template>
                  </div>
                </a-space>
                <span v-else>-</span>
              </template>
            </a-table-column>
          </template>
        </a-table>
        <template #actions>
          <a-pagination
            v-if="!!pagination.total"
            :current="pagination.current"
            :page-size="pagination.pageSize"
            :show-total="pagination.showTotal"
            :show-page-size="pagination.showPageSize"
            :page-size-options="pagination.pageSizeOptions"
            :total="pagination.total!"
            @change="pageChange"
            @page-size-change="pageSizeChange" />
        </template>
      </a-card>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>

d
<script lang="ts" setup>
import AMapLoader from '@amap/amap-jsapi-loader'
import locationIcon from '@/assets/images/location.png'
import locationSelectedIcon from '@/assets/images/location_s.png'
let AMap: any
const map = shallowRef<any>(null)
const globalMarker = shallowRef<any>(null)
const autoComplete = shallowRef<any>(null)
const placeSearch = shallowRef<any>(null)
const infoWindow = shallowRef<any>(null)

const initMap = async () => {
  return new Promise(async (resolve) => {
    AMap = await AMapLoader.load({
      // 申请好的Web端开发者Key，首次调用 load 时必填
      key: import.meta.env.VITE_AMAP_KEY,
      // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
      version: '2.0',
      // AMapUI: {
      //   plugins: ['misc/PathSimplifier']
      // }
      plugins: ['AMap.Geocoder', 'AMap.PlaceSearch', 'AMap.AutoComplete', 'AMap.Marker', 'Amap.InfoWindow', 'AMap.Pixel']
    })
    resolve(true)
  })
}
const createMap = () => {
  return new Promise((resolve, reject) => {
    map.value = new AMap.Map('aMapContainer', {
      viewMode: '2D',
      resizeEnable: true,
      rotateEnable: true,
      pitchEnable: true,
      zoom: 16,
      zooms: [2, 22]
    }).on('complete', () => {
      console.log('地图加载完成')
      resolve(true)
    })
  })
}
const keywords = ref<string>('')
const info = reactive<any>({})
onMounted(async () => {
  await initMap()
  await createMap()
  // 地图点击事件
  globalMarker.value = new AMap.Marker({
    icon: locationSelectedIcon,
    offset: new AMap.Pixel(-16, -26)
  })
  const geocoder = new AMap.Geocoder({
    city: '全国'
  })
  map.value.on('click', (e: any) => {
    const lnglat = [e.lnglat.lng, e.lnglat.lat]
    geocoder.getAddress(lnglat, function (status: string, result: any) {
      if (status === 'complete' && result.regeocode) {
        const detailAddress =
          // result.regeocode.addressComponent.province +
          // result.regeocode.addressComponent.city +
          // result.regeocode.addressComponent.district +
          result.regeocode.addressComponent.street + result.regeocode.addressComponent.township + result.regeocode.addressComponent.street + result.regeocode.addressComponent.streetNumber
        globalMarker.value.setPosition(lnglat)
        map.value.add(globalMarker.value)
        setInfoWindow(
          globalMarker.value,
          {
            name: result.regeocode.formattedAddress,
            address: detailAddress
          },
          new AMap.Pixel(0, -26)
        )
        Object.assign(info, {
          detailAddress,
          longitude: e.lnglat.lng,
          latitude: e.lnglat.lat
        })
      } else {
        console.log('object :>> ', '根据经纬度查询地址失败')
      }
    })
  })
  autoComplete.value = new AMap.AutoComplete({
    city: '全国'
  })

  placeSearch.value = new AMap.PlaceSearch({
    city: '全国'
  })
})

const suggests = ref<any[]>([])
const loading = ref<boolean>(false)
const getSuggests = () => {
  suggests.value = []
  autoComplete.value?.search(keywords.value, (status: string, result: any) => {
    if (status === 'complete' && result.info === 'OK') {
      suggests.value = result.tips.filter((item: any) => typeof item.address === 'string' && !!item.address)
    }
  })
}
const selectSuggest = (item: any) => {
  placeSearch.value?.setCity(item.adcode)
  placeSearchMethod(item.name)
}
const search = () => {
  if (!keywords.value) return
  placeSearchMethod(keywords.value)
}

const placeSearchMethod = (value: string) => {
  loading.value = true
  placeSearch.value?.search(value, (status: string, result: any) => {
    loading.value = false
    if (status === 'complete') {
      map.value.clearMap()
      const pois = result.poiList.pois
      const positions: any[] = []
      for (var i = 0; i < pois.length; i++) {
        const poi = pois[i]
        positions.push({
          marker: new AMap.Marker({
            icon: locationIcon,
            position: poi.location
          }),
          data: {
            name: poi.name,
            address: poi.address,
            longitude: poi.location.lng,
            latitude: poi.location.lat
          }
        })
      }
      if (!!positions.length) {
        for (let index = 0; index < positions.length; index++) {
          const marker = positions[index].marker
          const data = positions[index].data
          map.value.add(marker)
          marker.on('click', () => {
            map.value.remove(globalMarker.value)
            resetMarkerIcon(positions)
            marker.setIcon(locationSelectedIcon)
            setInfoWindow(marker, data, new AMap.Pixel(16, 0))
            Object.assign(info, {
              detailAddress: data.address,
              longitude: data.longitude,
              latitude: data.latitude
            })
          })
        }
        map.value.setFitView()
      }
    }
  })
}
const resetMarkerIcon = (positions: any[]) => {
  for (let index = 0; index < positions.length; index++) {
    const marker = positions[index].marker
    marker.setIcon(locationIcon)
  }
}
const setInfoWindow = (marker: any, data: any, offset: any) => {
  infoWindow.value = new AMap.InfoWindow({ offset })
  infoWindow.value.setContent(
    `
      <div class="py-[12px] px-[8px]">
        <div class="font-semibold">${data.name}</div>
        <div class="pt-[8px] text-gray-400">${data.address}</div>
      </div>
    `
  )
  infoWindow.value.open(map.value, marker.getPosition())
}
defineExpose({
  info
})
</script>

<template>
  <a-spin :loading="loading">
    <div class="h-[700px] mt-[16px]" id="aMapContainer">
      <div class="absolute top-[16px] right-[16px] z-10 bg-white">
        <a-trigger trigger="focus" :blur-to-close="false">
          <a-input-search v-model="keywords" :style="{ width: '520px' }" placeholder="输入地址搜索" search-button :loading="loading" @focus="getSuggests" @input="getSuggests" @search="search" />
          <template #content>
            <div class="mt-[2px] w-[520px] bg-white px-[8px] border max-h-[400px] overflow-y-scroll no-scrollbar">
              <a-list v-if="!!suggests.length" :bordered="false" size="small">
                <a-list-item v-for="(item, index) in suggests" :key="index" class="cursor-pointer hover:bg-gray-100" @click="selectSuggest(item)">
                  <a-list-item-meta :title="item.name" :description="`${item.district} - ${item.address}`"></a-list-item-meta>
                </a-list-item>
              </a-list>
              <a-empty v-else></a-empty>
            </div>
          </template>
        </a-trigger>
      </div>
    </div>
  </a-spin>
</template>

<style lang="scss" scoped></style>

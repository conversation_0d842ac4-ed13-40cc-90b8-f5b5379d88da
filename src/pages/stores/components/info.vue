<script lang="ts" setup>
import { FormInstance, Notification } from '@arco-design/web-vue'
import useCommon from '@/hooks/useCommon'
import useAddress from '@/hooks/useAddress'
import useHooks from '../hooks'
import { useTemplateRef } from 'vue'

const isPayMentVersion = Number(import.meta.env.VITE_IS_PAYMENT_VERSION)

const props = defineProps<{
  id?: string
}>()

const MapCom = defineAsyncComponent(() => import('./map.vue'))

const {
  storeBusinessStateOptions,
  storeMccOptions,
  initStoreMccOptions,
  storeCategoryOptions,
  initStoreCategoryOptions,
  storeSubCategoryOptions,
  initStoreSubCategoryOptions,
  bankCodeOptions,
  initBankCodeOptions,
  bankBranchIdOptions,
  initBankBranchIdOptions
} = useCommon()
const { provinces, cities, setCities, areas, setAreas } = useAddress()
const { form, detail } = useHooks(1)

onMounted(async () => {
  initStoreMccOptions()
  initStoreCategoryOptions()
  initBankCodeOptions()
  if (!!props.id) {
    await detail(props.id)
    initStoreSubCategoryOptions(form.storeCategoryId!)
    if (!!form.bankCode) {
      initBankBranchIdOptions({
        bankCode: form.bankCode!,
        bankBranchName: form.bankBranchName
      })
    }
    setCities(form.provinceCode!)
    setAreas(form.provinceCode!, form.cityCode!)
  }
})
const formRef = useTemplateRef<FormInstance>('formRef')

const mapRef = useTemplateRef<InstanceType<typeof MapCom>>('mapRef')
const showMap = ref<boolean>(false)
const submitChooseAddress = () => {
  const info = toRaw(mapRef.value?.info)
  if (!info.detailAddress) {
    Notification.warning({
      title: '提示',
      content: `请选择地址`,
      duration: 1500
    })
    return
  }
  form.detailAddress = info.detailAddress
  form.longitude = info.longitude
  form.latitude = info.latitude
  showMap.value = false
}
defineExpose({
  formRef,
  form
})
</script>

<template>
  <div class="h-[700px] overflow-y-scroll no-scrollbar">
    <a-modal
      :visible="showMap"
      :width="1200"
      title-align="start"
      :title="`地图选址`"
      :cancel-button-props="{ type: 'outline' }"
      unmount-on-close
      :mask-closable="false"
      :esc-to-close="false"
      @ok="submitChooseAddress"
      @cancel="showMap = false"
      body-style="background-color: var(--color-fill-2)">
      <map-com ref="mapRef"></map-com>
    </a-modal>
    <a-form ref="formRef" :model="form" auto-label-width>
      <div class="h-full flex flex-col gap-[18px]">
        <a-card :bordered="false" title="装修信息">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item show-colon label="店铺LOGO" field="logo" :rules="[{ required: true, message: `${$uploadPlaceholder}店铺LOGO` }]">
                <template #extra>
                  <div>请上传200px*200px的.png图片</div>
                </template>
                <upload-card v-model="form.logo" :disabled="!!id" accept="image/*" title="上传店铺LOGO"></upload-card>
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item show-colon label="店铺背景图片" field="storeBackgroundPictures" :rules="[{ required: true, message: `${$uploadPlaceholder}店铺背景图片` }]">
            <upload-multiple v-model="form.storeBackgroundPictures" accept="image/*" :limit="20" title="上传店铺背景图片"></upload-multiple>
          </a-form-item>
          <a-form-item show-colon label="店铺宣传图片" field="storePropagandaPictures" :rules="[{ required: true, message: `${$uploadPlaceholder}店铺宣传图片` }]">
            <upload-multiple v-model="form.storePropagandaPictures" accept="image/*" :limit="20" title="上传店铺宣传图片"></upload-multiple>
          </a-form-item>
          <a-form-item show-colon label="店铺介绍" field="storeIntroduce" :rules="[{ required: true, message: `${$inputPlaceholder}店铺介绍` }]">
            <rich-text-editor v-model="form.storeIntroduce" :placeholder="`${$inputPlaceholder}店铺介绍`" />
          </a-form-item>
        </a-card>
        <a-card :bordered="false" title="基本信息">
          <a-row :gutter="16">
            <a-col v-if="!!id" :span="12">
              <a-form-item show-colon label="店铺编号" field="storeNo" :rules="[{ required: true, message: `${$inputPlaceholder}店铺编号` }]">
                <a-input v-model="form.storeNo" disabled :placeholder="`${$inputPlaceholder}店铺编号`" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item show-colon label="店铺名称" field="name" :rules="[{ required: true, message: `${$inputPlaceholder}店铺名称` }]">
                <a-input v-model="form.name" :placeholder="`${$inputPlaceholder}店铺名称`" />
              </a-form-item>
            </a-col>

            <a-col v-if="!!isPayMentVersion && !!id" :span="12">
              <a-form-item show-colon label="平台佣金比例" field="platformCommissionRate" :rules="[{ required: true, message: `${$inputPlaceholder}平台佣金比例` }]">
                <a-input-number hide-button v-model="form.platformCommissionRate" :placeholder="`${$inputPlaceholder}平台佣金比例`">
                  <template #suffix>%</template>
                </a-input-number>
              </a-form-item>
            </a-col>
          </a-row>
          <!-- <a-col :span="12">
              <a-form-item show-colon label="店铺状态" field="businessState" :rules="[{ required: true, message: `${$inputPlaceholder}店铺状态` }]">
                <a-select v-model="form.businessState" :disabled="!!id" :options="storeBusinessStateOptions" :placeholder="`${$selectPlaceholder}店铺状态`" />
              </a-form-item>
            </a-col> -->
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item show-colon label="一级分类" field="storeCategoryId" :rules="[{ required: true, message: `${$selectPlaceholder}一级分类` }]">
                <a-select
                  v-model="form.storeCategoryId"
                  :options="storeCategoryOptions"
                  :placeholder="`${$selectPlaceholder}一级分类`"
                  @change="(e:any) =>{
                    form.storeSubCategoryId = undefined
                    storeSubCategoryOptions = []
                    initStoreSubCategoryOptions(e as string)
                  }"
                  allow-search />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item show-colon label="二级分类" field="storeSubCategoryId" :rules="[{ required: true, message: `${$selectPlaceholder}二级分类` }]">
                <a-select v-model="form.storeSubCategoryId" :options="storeSubCategoryOptions" :placeholder="`${$selectPlaceholder}二级分类`" allow-search />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item show-colon label="联系人姓名" field="contactName" :rules="[{ required: true, message: `${$inputPlaceholder}联系人姓名` }]">
                <a-input v-model="form.contactName" :placeholder="`${$inputPlaceholder}联系人姓名`" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item show-colon label="联系电话1" field="contactNumber" :rules="[{ required: true, message: `${$inputPlaceholder}联系电话1` }]">
                <a-input-number hide-button v-model="form.contactNumber" :placeholder="`${$inputPlaceholder}联系电话1`" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item show-colon label="联系电话2" field="contactNumber2">
                <a-input-number hide-button v-model="form.contactNumber2" :placeholder="`${$inputPlaceholder}联系电话2`" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item show-colon label="嘉联商户号" field="merchNo">
                <a-input v-model="form.merchNo" :placeholder="`${$inputPlaceholder}嘉联商户号`" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item show-colon label="店铺地址" required style="margin-bottom: 0">
            <div class="w-full flex gap-[12px] justify-between">
              <a-form-item hide-label field="provinceCode" :rules="[{ required: true, message: `${$selectPlaceholder}省` }]">
                <a-select
                  v-model="form.provinceCode"
                  :options="provinces"
                  :placeholder="`${$selectPlaceholder}省`"
                  allow-search
                  @change="(e:any) => {
                    form.provinceName = provinces.find(item => item.value === e)?.label
                    form.cityCode = undefined
                    form.cityName = undefined
                    form.areaCode = undefined
                    form.areaName = undefined
                    cities = []
                    areas = []
                    setCities(e as number)
                  }" />
              </a-form-item>
              <a-form-item hide-label field="cityCode" :rules="[{ required: true, message: `${$selectPlaceholder}市` }]">
                <a-select
                  v-model="form.cityCode"
                  :options="cities"
                  :placeholder="`${$selectPlaceholder}市`"
                  allow-search
                  @change="(e:any) =>{
                    form.cityName = cities.find(item => item.value === e)?.label
                    form.areaCode = undefined
                    form.areaName = undefined
                    areas = []
                    setAreas(form.provinceCode!,e as number)
                  }" />
              </a-form-item>
              <a-form-item hide-label field="areaCode" :rules="[{ required: true, message: `${$selectPlaceholder}区` }]">
                <a-select
                  v-model="form.areaCode"
                  :options="areas"
                  :placeholder="`${$selectPlaceholder}区`"
                  allow-search
                  @change="(e:any) =>{
                    form.areaName = areas.find(item => item.value === e)?.label
                  }" />
              </a-form-item>
            </div>
          </a-form-item>
          <a-form-item show-colon label="详细地址" field="detailAddress" :rules="[{ required: true, message: `${$inputPlaceholder}详细地址` }]">
            <div class="w-full flex items-center gap-x-3">
              <a-input v-model="form.detailAddress" :readonly="!form.longitude && !form.latitude" :placeholder="`${$inputPlaceholder}详细地址`" />
              <a-button type="primary" @click="showMap = true">
                <template #icon>
                  <icon-location />
                </template>
                地图选址
              </a-button>
            </div>
          </a-form-item>
        </a-card>
        <a-card :bordered="false" title="企业信息">
          <a-form-item show-colon label="营业执照" field="license">
            <upload-card v-model="form.license" accept="image/*" title="上传营业执照"></upload-card>
          </a-form-item>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item show-colon label="法人身份证人像面" field="legalPersonIdCardFront">
                <upload-card v-model="form.legalPersonIdCardFront" accept="image/*" title="上传身份证人像面"></upload-card>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item show-colon label="法人身份证国徽面" field="legalPersonIdCardBack">
                <upload-card v-model="form.legalPersonIdCardBack" accept="image/*" title="上传身份证国徽面"></upload-card>
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item show-colon label="企业性质" field="enterpriseNature">
            <a-radio-group v-model="form.enterpriseNature">
              <a-radio :value="1">个体工商户</a-radio>
              <a-radio :value="2">企业</a-radio>
              <a-radio :value="3">政府机关</a-radio>
              <a-radio :value="4">事业单位</a-radio>
              <a-radio :value="5">其他组织</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item show-colon label="促进会职务" field="enterpriseName">
                <a-input v-model="form.enterpriseName" :placeholder="`${$inputPlaceholder}促进会职务`" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item show-colon label="企业法人" field="legalPerson">
                <a-input v-model="form.legalPerson" :placeholder="`${$inputPlaceholder}企业法人`" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item show-colon label="法人身份证号" field="legalPersonIdentityCard">
                <a-input v-model="form.legalPersonIdentityCard" :placeholder="`${$inputPlaceholder}法人身份证号`" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item show-colon label="法人手机号" field="legalPersonPhone">
                <a-input-number hide-button v-model="form.legalPersonPhone" :placeholder="`${$inputPlaceholder}法人手机号`" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item show-colon label="统一社会信用代码" field="unifiedSocialCreditCode">
                <a-input v-model="form.unifiedSocialCreditCode" :placeholder="`${$inputPlaceholder}统一社会信用代码`" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item show-colon label="注册地址" field="registeredAddress">
                <a-input v-model="form.registeredAddress" :placeholder="`${$inputPlaceholder}注册地址`" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item show-colon label="经营范围" field="businessScope">
            <a-textarea v-model="form.businessScope" :auto-size="{ minRows: 5 }" :max-length="300" show-word-limit :placeholder="`${$inputPlaceholder}经营范围`" />
          </a-form-item>
        </a-card>
        <a-card :bordered="false" title="开票信息">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item show-colon label="发票抬头" field="invoiceTitle">
                <a-input v-model="form.invoiceTitle" :placeholder="`${$inputPlaceholder}发票抬头`" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item show-colon label="税号" field="invoiceTaxNumber">
                <a-input v-model="form.invoiceTaxNumber" :placeholder="`${$inputPlaceholder}税号`" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item show-colon label="开户行" field="bankCode">
                <a-select
                  v-model="form.bankCode"
                  :options="bankCodeOptions"
                  :placeholder="`${$selectPlaceholder}开户行`"
                  @change="(e:any) =>{
                    form.bankName = bankCodeOptions.find(item => item.value === e)?.label
                    form.bankBranchId= undefined
                    form.bankBranchName = undefined
                    form.bankProvinceName = undefined
                    form.bankCityName = undefined
                    bankBranchIdOptions=[]
                  }"
                  allow-search />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item show-colon label="开户行支行名称" field="bankBranchId">
                <a-select
                  v-model="form.bankBranchId"
                  :disabled="!form.bankCode"
                  :options="bankBranchIdOptions"
                  :placeholder="`${$selectPlaceholder}开户行支行名称`"
                  @change="(e:any) =>{
                    form.bankBranchName = bankBranchIdOptions.find(item => item.value === e)?.label
                    form.bankProvinceName = bankBranchIdOptions.find(item => item.value === e)?.extra?.bankProvinceName
                    form.bankCityName = bankBranchIdOptions.find(item => item.value === e)?.extra?.bankCityName
                  }"
                  @search="(value: any) => {
                    initBankBranchIdOptions({
                      bankCode: form.bankCode!,
                      bankBranchName: value
                    })
                  }"
                  :filter-option="false"
                  allow-search />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item show-colon label="开户账号" field="bankAccount">
                <a-input v-model="form.bankAccount" :placeholder="`${$inputPlaceholder}开户账号`" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item show-colon label="银行预留手机号" field="bankReservePhone">
                <a-input-number hide-button v-model="form.bankReservePhone" :placeholder="`${$inputPlaceholder}银行预留手机号`" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>
        <a-card v-if="!id" :bordered="false" title="管理员信息">
          <a-col :span="12">
            <a-form-item
              show-colon
              label="管理员账号（手机号）"
              field="mobile"
              :rules="[
                { required: true, message: `${$inputPlaceholder}管理员账号（手机号）` },
                { match: /^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[1589]))\d{8}$/, message: `${$inputPlaceholder}正确的管理员账号（手机号）` }
              ]">
              <a-input-number hide-button v-model="form.mobile" :placeholder="`${$inputPlaceholder}管理员账号（手机号）`" />
              <template #extra>
                <div>管理员登陆密码默认为账号后6位</div>
              </template>
            </a-form-item>
          </a-col>
        </a-card>
      </div>
    </a-form>
  </div>
</template>

<style lang="scss" scoped></style>

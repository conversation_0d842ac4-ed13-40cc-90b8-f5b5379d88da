<script lang="ts" setup>
import { formatNumber } from '@/utils'
import { FormInstance } from '@arco-design/web-vue'
import useCommon from '@/hooks/useCommon'
import useAddress from '@/hooks/useAddress'
import useHooks from '../hooks'
import { useTemplateRef } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
// console.log('route.name :>> ', route.name)
const isPayMentVersion = Number(import.meta.env.VITE_IS_PAYMENT_VERSION)

const props = defineProps<{
  id: string
}>()
const { storeBusinessStateOptions } = useCommon()
const { provinces, cities, setCities, areas, setAreas } = useAddress()
const { form, detail } = useHooks(1)

onMounted(async () => {
  await detail(props.id)
  setCities(form.provinceCode!)
  setAreas(form.provinceCode!, form.cityCode!)
})
const formRef = useTemplateRef<FormInstance>('formRef')
defineExpose({
  formRef,
  form
})
</script>

<template>
  <div class="h-[700px] overflow-y-scroll no-scrollbar">
    <div class="h-full flex flex-col gap-[18px]">
      <a-card :bordered="false" title="装修信息">
        <a-descriptions bordered size="large" :column="1">
          <a-descriptions-item label="店铺LOGO">
            <upload-card :modelValue="form.logo" disabled></upload-card>
          </a-descriptions-item>
          <a-descriptions-item label="店铺背景图片">
            <upload-multiple v-if="form.storeBackgroundPictures?.length" :modelValue="form.storeBackgroundPictures" disabled></upload-multiple>
            <span v-else>-</span>
          </a-descriptions-item>
          <a-descriptions-item label="店铺宣传图片">
            <upload-multiple v-if="form.storePropagandaPictures?.length" :modelValue="form.storePropagandaPictures" disabled></upload-multiple>
            <span v-else>-</span>
          </a-descriptions-item>
          <a-descriptions-item label="店铺介绍">
            <div v-if="!!form.storeIntroduce" v-html="form.storeIntroduce"></div>
            <span v-else>-</span>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
      <a-card :bordered="false" title="基本信息">
        <a-descriptions bordered size="large" :column="2">
          <a-descriptions-item label="店铺编号">{{ form.storeNo }}</a-descriptions-item>
          <a-descriptions-item label="店铺名称">{{ form.name }}</a-descriptions-item>
          <a-descriptions-item label="联系人姓名">{{ form.contactName }}</a-descriptions-item>
          <a-descriptions-item label="联系电话">{{ formatNumber(form.contactNumber?.toString()) }}</a-descriptions-item>
          <a-descriptions-item label="联系电话2">{{ formatNumber(form.contactNumber2?.toString() ?? '-') }}</a-descriptions-item>
          <a-descriptions-item label="店铺状态">
            <template v-if="route.name === 'StoreApprove'">-</template>
            <template v-if="route.name === 'StoreList'">
              <a-tag :color="storeBusinessStateOptions.find((item) => item.value === form.businessState)?.color ?? ''">
                {{ storeBusinessStateOptions.find((item) => item.value === form.businessState)?.label ?? '' }}
              </a-tag>
            </template>
          </a-descriptions-item>
          <a-descriptions-item v-if="!!isPayMentVersion" label="平台佣金比例">{{ form.platformCommissionRate }} %</a-descriptions-item>
          <a-descriptions-item label="店铺分类" :span="2">{{ form.storeCategoryName }} - {{ form.storeSubCategoryName }}</a-descriptions-item>
          <a-descriptions-item label="店铺地址" :span="2">{{ form.provinceName }} - {{ form.cityName }} - {{ form.areaName }} - {{ form.detailAddress }}</a-descriptions-item>
        </a-descriptions>
      </a-card>
      <a-card :bordered="false" title="企业信息">
        <a-descriptions bordered size="large" :column="2">
          <a-descriptions-item label="营业执照">
            <upload-card v-if="!!form.license" :modelValue="form.license" disabled></upload-card>
            <span v-else>-</span>
          </a-descriptions-item>
          <a-descriptions-item label="法人身份证人像面">
            <upload-card v-if="!!form.legalPersonIdCardFront" :modelValue="form.legalPersonIdCardFront" disabled></upload-card>
            <span v-else>-</span>
          </a-descriptions-item>
          <a-descriptions-item label="法人身份证国徽面">
            <upload-card v-if="!!form.legalPersonIdCardBack" :modelValue="form.legalPersonIdCardBack" disabled></upload-card>
            <span v-else>-</span>
          </a-descriptions-item>
          <a-descriptions-item label="企业性质">
            {{
              form.enterpriseNature === 1
                ? '个体工商户'
                : form.enterpriseNature === 2
                ? '企业'
                : form.enterpriseNature === 3
                ? '政府机关'
                : form.enterpriseNature === 4
                ? '事业单位'
                : form.enterpriseNature === 5
                ? '其他组织'
                : '-'
            }}
          </a-descriptions-item>
          <a-descriptions-item label="促进会职务">{{ form.enterpriseName || '-' }}</a-descriptions-item>
          <a-descriptions-item label="企业法人">{{ form.legalPerson || '-' }}</a-descriptions-item>
          <a-descriptions-item label="企业法人身份证号">{{ form.legalPersonIdentityCard || '-' }}</a-descriptions-item>
          <a-descriptions-item label="企业法人手机号">{{ form.legalPersonPhone || '-' }}</a-descriptions-item>
          <a-descriptions-item label="统一社会信用代码">{{ form.unifiedSocialCreditCode || '-' }}</a-descriptions-item>
          <a-descriptions-item label="注册地址">{{ form.registeredAddress || '-' }}</a-descriptions-item>
          <a-descriptions-item label="经营范围" :span="2">{{ form.businessScope || '-' }}</a-descriptions-item>
        </a-descriptions>
      </a-card>
      <a-card :bordered="false" title="开票信息">
        <a-descriptions bordered size="large" :column="2">
          <a-descriptions-item label="发票抬头">{{ form.invoiceTitle || '-' }}</a-descriptions-item>
          <a-descriptions-item label="税号">{{ form.invoiceTaxNumber || '-' }}</a-descriptions-item>
          <a-descriptions-item label="开户行">{{ form.bankName || '-' }}</a-descriptions-item>
          <a-descriptions-item label="开户行支行名称">{{ form.bankBranchName || '-' }}</a-descriptions-item>
          <a-descriptions-item label="开户账号">{{ form.bankAccount || '-' }}</a-descriptions-item>
          <a-descriptions-item label="银行预留手机号">{{ form.bankReservePhone || '-' }}</a-descriptions-item>
        </a-descriptions>
      </a-card>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>

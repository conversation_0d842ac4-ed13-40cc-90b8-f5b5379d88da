<script lang="ts" setup>
const ListCom = defineAsyncComponent(() => import('./list.vue'))
</script>

<template>
  <div class="page-container">
    <a-tabs type="rounded" lazy-load destroy-on-hide>
      <a-tab-pane
        v-for="delState in [
          { label: '未注销', value: -1 },
          { label: '已注销', value: 1 }
        ]"
        :key="delState.value"
        :title="delState.label">
        <list-com :delState="delState.value"></list-com>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<style lang="scss" scoped>
:deep(.arco-tabs) {
  height: 100%;
}
:deep(.arco-tabs-nav) {
  background-color: var(--color-bg-2);
  padding: 16px 10px 10px;
}
:deep(.arco-tabs-content) {
  padding-top: 0;
  height: calc(100% - 58px);
  .arco-tabs-content-list {
    height: 100%;
    .arco-tabs-pane {
      height: 100%;
    }
  }
}
</style>

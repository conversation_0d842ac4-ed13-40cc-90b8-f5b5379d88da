import useLoading from '@/hooks/useLoading'
import usePagination from '@/hooks/usePagination'
import { IForm, IQueryParams } from './types'
import { reqGetStores, reqAddStore, reqEditStore, reqGetStoreDetail, reqDelStore, reqApproveStore, reqExportStore } from '@/api/apiStore'
import dayjs from 'dayjs'
import { TableData } from '@arco-design/web-vue'
import { accMul, accDiv } from '@/utils'

const useHooks = (args: any) => {
  const { loading, setLoading } = useLoading()
  const { pagination } = usePagination()
  const initQueryParams = (): IQueryParams => {
    return {
      name: undefined,
      businessState: undefined,
      contactName: undefined,
      contactNumber: undefined,
      storeCategoryId: undefined,
      storeSubCategoryId: undefined,
      provinceCode: undefined,
      cityCode: undefined,
      areaCode: undefined,
      createTime: undefined
    }
  }
  const queryParams = reactive<IQueryParams>(initQueryParams())
  const selectedId = ref<string | undefined>(undefined)
  const selectedRow = computed(() => {
    return !selectedId.value ? null : rows.value.find((item) => item.id === selectedId.value)
  })
  const selectedIds = ref<string[]>([])
  const selectedRows = computed(() => {
    return !selectedIds.value.length ? [] : rows.value.filter((item) => selectedIds.value.includes(item.id))
  })
  // 单选
  // const rowSelect = (rowKeys: (string | number)[], rowKey: string | number, record: TableData): void => {
  //   selectedId.value = selectedId.value === record.id ? 0 : record.id
  // }
  // const rowClick = (record: TableData): void => {
  //   selectedId.value = selectedId.value === record.id ? 0 : record.id
  // }
  // 多选
  const selectAll = (checked: boolean): void => {
    selectedIds.value = checked ? rows.value.map((item) => item.id) : []
  }
  const rowSelect = (rowKeys: (string | number)[], rowKey: string | number, record: TableData): void => {
    selectedIds.value.includes(record.id) ? selectedIds.value.splice(selectedIds.value.indexOf(record.id), 1) : selectedIds.value.push(record.id)
  }
  const rowClick = (record: TableData): void => {
    selectedIds.value.includes(record.id) ? selectedIds.value.splice(selectedIds.value.indexOf(record.id), 1) : selectedIds.value.push(record.id)
  }
  const rows = ref<any[]>([])
  const query = async () => {
    setLoading(true)
    selectedId.value = undefined
    selectedIds.value = []
    try {
      const { data } = await reqGetStores({ ...queryParams, ...args, contactNumber: queryParams.contactNumber?.toString(), pageNum: pagination.current!, pageSize: pagination.pageSize! })
      rows.value = (data.rows as any[]).map((item) => {
        item.createTime = dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss')
        item.delTime = item.delTime ? dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') : ''
        return item
      })
      pagination.total = data.total as number
      setLoading(false)
    } catch (error) {
      rows.value = []
      pagination.total = 0
      setLoading(false)
    }
  }
  const reset = () => {
    pagination.current = 1
    Object.assign(queryParams, initQueryParams())
    query()
  }
  const pageChange = async (current: number) => {
    pagination.current = current
    query()
  }
  const pageSizeChange = async (pageSize: number) => {
    pagination.current = 1
    pagination.pageSize = pageSize
    query()
  }
  const initForm = (): IForm => {
    return {
      logo: undefined,
      storeBackgroundPictures: [],
      storePropagandaPictures: [],
      storeIntroduce: undefined,

      storeNo: undefined,
      name: undefined,
      businessState: 1,
      storeCategoryId: undefined,
      storeCategoryName: undefined,
      storeSubCategoryId: undefined,
      storeSubCategoryName: undefined,
      provinceCode: undefined,
      provinceName: undefined,
      cityCode: undefined,
      cityName: undefined,
      areaCode: undefined,
      areaName: undefined,
      detailAddress: undefined,
      longitude: undefined,
      latitude: undefined,

      invoiceTitle: undefined,
      invoiceTaxNumber: undefined,
      bankName: undefined,
      bankBranchName: undefined,
      bankAccount: undefined,
      bankReservePhone: undefined,
      bankCode: undefined,
      bankBranchId: undefined,
      bankProvinceName: undefined,
      bankCityName: undefined,

      license: undefined,
      enterpriseName: undefined,
      enterpriseNature: undefined,
      legalPerson: undefined,
      legalPersonIdentityCard: undefined,
      legalPersonPhone: undefined,
      legalPersonIdCardFront: undefined,
      legalPersonIdCardBack: undefined,
      unifiedSocialCreditCode: undefined,
      registeredAddress: undefined,
      contactName: undefined,
      contactNumber: undefined,
      contactNumber2: undefined,
      merchNo: undefined,

      businessScope: undefined,

      platformCommissionRate: undefined,

      mobile: undefined
    }
  }
  const form = reactive<IForm>(initForm())
  const add = async (data: IForm) => {
    try {
      const { storeCategoryName, storeSubCategoryName, ...d } = data
      await reqAddStore({ ...d })
    } catch (error) {
      throw error
    }
  }
  const detail = async (id: string) => {
    try {
      const { data } = await reqGetStoreDetail(id)
      const {
        logo,
        storeBackgroundPictures,
        storePropagandaPictures,
        storeIntroduce,

        storeNo,
        name,
        storeMccCode,
        businessState,
        storeCategoryId,
        storeCategoryName,
        storeSubCategoryId,
        storeSubCategoryName,
        provinceCode,
        provinceName,
        cityCode,
        cityName,
        areaCode,
        areaName,
        detailAddress,
        longitude,
        latitude,

        invoiceTitle,
        invoiceTaxNumber,
        bankName,
        bankBranchName,
        bankAccount,
        bankReservePhone,
        bankCode,
        bankBranchId,
        bankProvinceName,
        bankCityName,

        license,
        enterpriseName,
        enterpriseNature,
        legalPerson,
        legalPersonIdentityCard,
        legalPersonPhone,
        legalPersonIdCardFront,
        legalPersonIdCardBack,
        unifiedSocialCreditCode,
        registeredAddress,
        contactName,
        contactNumber,
        contactNumber2,
        merchNo,

        businessScope,
        platformCommissionRate
      } = data
      Object.assign(form, {
        logo,
        storeBackgroundPictures,
        storePropagandaPictures,
        storeIntroduce,

        storeNo,
        name,
        storeMccCode,
        businessState,
        storeCategoryId,
        storeCategoryName,
        storeSubCategoryId,
        storeSubCategoryName,
        provinceCode,
        provinceName,
        cityCode,
        cityName,
        areaCode,
        areaName,
        detailAddress,
        longitude,
        latitude,

        invoiceTitle,
        invoiceTaxNumber,
        bankName,
        bankBranchName,
        bankAccount,
        bankReservePhone: Number(bankReservePhone) || undefined,
        bankCode,
        bankBranchId,
        bankProvinceName,
        bankCityName,

        license,
        enterpriseName,
        enterpriseNature,
        legalPerson,
        legalPersonIdentityCard,
        legalPersonPhone: Number(legalPersonPhone) || undefined,
        legalPersonIdCardFront,
        legalPersonIdCardBack,
        unifiedSocialCreditCode,
        registeredAddress,
        contactName,
        contactNumber: Number(contactNumber) || undefined,
        contactNumber2: Number(contactNumber2) || undefined,
        merchNo,

        businessScope,
        platformCommissionRate: accMul(platformCommissionRate, 100)
      })
    } catch (error) {
      throw error
    }
  }
  const edit = async (id: string, data: any) => {
    try {
      const { storeCategoryName, storeSubCategoryName, platformCommissionRate, ...d } = data
      await reqEditStore(id, { ...d, platformCommissionRate: accDiv(platformCommissionRate, 100) })
    } catch (error) {
      throw error
    }
  }
  const del = async (id: string) => {
    try {
      await reqDelStore(id)
    } catch (error) {
      throw error
    }
  }
  const approve = async (id: string, data: { approveState: number; approveReason?: string }) => {
    try {
      await reqApproveStore(id, data)
    } catch (error) {
      throw error
    }
  }
  const exports = async () => {
    try {
      await reqExportStore()
    } catch (error) {
      throw error
    }
  }
  return {
    loading,
    queryParams,
    pagination,
    rows,
    selectedId,
    selectedRow,
    selectedIds,
    selectedRows,
    selectAll,
    rowSelect,
    rowClick,
    query,
    reset,
    pageChange,
    pageSizeChange,
    form,
    add,
    detail,
    edit,
    del,
    approve,
    exports
  }
}
export default useHooks

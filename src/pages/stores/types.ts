export type IQueryParams = {
  name?: string
  businessState?: number
  contactName?: string
  contactNumber?: number
  storeCategoryId?: string
  storeSubCategoryId?: string
  provinceCode?: number
  cityCode?: number
  areaCode?: number
  createTime?: string[]
}
export type IForm = {
  logo?: string
  storeBackgroundPictures: string[]
  storePropagandaPictures: string[]
  storeIntroduce?: string

  storeNo?: string
  name?: string
  businessState?: number
  contactName?: string
  contactNumber?: number
  contactNumber2?: number
  merchNo?: string
  storeCategoryId?: string
  storeCategoryName?: string
  storeSubCategoryId?: string
  storeSubCategoryName?: string
  provinceCode?: number
  provinceName?: string
  cityCode?: number
  cityName?: string
  areaCode?: number
  areaName?: string
  detailAddress?: string
  longitude?: number
  latitude?: number

  invoiceTitle?: string
  invoiceTaxNumber?: string
  bankName?: string
  bankBranchName?: string
  bankAccount?: string
  bankReservePhone?: number
  bankCode?: string
  bankBranchId?: string
  bankProvinceName?: string
  bankCityName?: string

  license?: string
  enterpriseName?: string
  enterpriseNature?: number
  legalPerson?: string
  legalPersonIdentityCard?: string
  legalPersonIdCardFront?: string
  legalPersonIdCardBack?: string
  legalPersonPhone?: number
  unifiedSocialCreditCode?: string
  registeredAddress?: string
  businessScope?: string

  platformCommissionRate?: number

  mobile?: number
}

<script lang="ts" setup>
import { useTemplateRef } from 'vue'
import useCommon from '@/hooks/useCommon'
import useAddress from '@/hooks/useAddress'
import useHooks from './hooks'
import { Modal, Notification, TableData } from '@arco-design/web-vue'
import { accMul, formatNumber } from '@/utils'

const props = defineProps<{
  delState: number
}>()

const isPayMentVersion = Number(import.meta.env.VITE_IS_PAYMENT_VERSION)

const InfoCom = defineAsyncComponent(() => import('./components/info.vue'))
const DetailCom = defineAsyncComponent(() => import('./components/detail.vue'))

const { storeBusinessStateOptions, storeCategoryOptions, initStoreCategoryOptions, storeSubCategoryOptions, initStoreSubCategoryOptions } = useCommon()
const { provinces, cities, setCities, areas, setAreas } = useAddress()
const { loading, queryParams, pagination, rows, selectedId, selectedIds, selectAll, rowSelect, rowClick, query, reset, pageChange, pageSizeChange, add, edit, del, exports } = useHooks({
  approveState: props.delState === -1 ? 1 : undefined,
  delState: props.delState
})
const addRef = useTemplateRef<InstanceType<typeof InfoCom>>('addRef')
const showAdd = ref<boolean>(false)
const submitAdd = async (done: (closed: boolean) => void): Promise<void | boolean> => {
  try {
    const errors = await addRef.value?.formRef?.validate()
    if (!!errors) throw new Error('校验失败')
    await add(toRaw(addRef.value!.form))
    Notification.success({
      title: '成功提示',
      content: `已添加店铺`,
      duration: 1500
    })
    done(true)
    query()
  } catch (error) {
    done(false)
  }
}
const editRef = useTemplateRef<InstanceType<typeof InfoCom>>('editRef')
const showEdit = ref<boolean>(false)
const submitEdit = async (done: (closed: boolean) => void): Promise<void | boolean> => {
  try {
    const errors = await editRef.value?.formRef?.validate()
    if (!!errors) throw new Error('校验失败')
    await edit(selectedId.value!, toRaw(editRef.value!.form))
    Notification.success({
      title: '成功提示',
      content: `已修改店铺`,
      duration: 1500
    })
    done(true)
    query()
  } catch (error) {
    done(false)
  }
}
const handleDel = (record: TableData) => {
  try {
    Modal.warning({
      title: '提示',
      content: () => h('div', { class: 'text-center' }, `确定注销【${record.name}】？`),
      maskClosable: false,
      escToClose: false,
      hideCancel: false,
      cancelButtonProps: { type: 'outline' },
      onBeforeOk: async (done: (closed: boolean) => void): Promise<void | boolean> => {
        try {
          await del(record.id)
          Notification.success({
            title: '成功提示',
            content: `已注销店铺`,
            duration: 1500
          })
          done(true)
          query()
        } catch (error) {
          done(false)
        }
      }
    })
  } catch (error) {}
}
const showDetail = ref<boolean>(false)

const handleExport = () => {
  try {
    Modal.warning({
      title: '提示',
      content: () => h('div', { class: 'text-center' }, `确定导出所有店铺信息？`),
      maskClosable: false,
      escToClose: false,
      hideCancel: false,
      cancelButtonProps: { type: 'outline' },
      onBeforeOk: async (done: (closed: boolean) => void): Promise<void | boolean> => {
        try {
          await exports()
          Notification.success({
            title: '操作提示',
            content: `已导出所有店铺信息`,
            duration: 1500
          })
          done(true)
        } catch (error) {
          done(false)
        }
      }
    })
  } catch (error) {}
}
onMounted(() => {
  initStoreCategoryOptions()
  pageChange(1)
})
</script>

<template>
  <div class="pt-[18px] h-full">
    <a-modal
      v-model:visible="showAdd"
      :width="1000"
      title-align="start"
      :title="`店铺入驻`"
      :cancel-button-props="{ type: 'outline' }"
      unmount-on-close
      :mask-closable="false"
      :esc-to-close="false"
      :on-before-ok="submitAdd"
      @cancel="showAdd = false"
      body-style="background-color: var(--color-fill-2)">
      <info-com ref="addRef"></info-com>
    </a-modal>
    <a-modal
      v-model:visible="showEdit"
      :width="1000"
      title-align="start"
      :title="`修改店铺`"
      :cancel-button-props="{ type: 'outline' }"
      unmount-on-close
      :mask-closable="false"
      :esc-to-close="false"
      :on-before-ok="submitEdit"
      @cancel="showEdit = false"
      body-style="background-color: var(--color-fill-2)">
      <info-com ref="editRef" :id="selectedId"></info-com>
    </a-modal>
    <a-modal
      v-model:visible="showDetail"
      :width="1000"
      title-align="start"
      :title="`店铺详情`"
      :cancel-button-props="{ type: 'outline' }"
      unmount-on-close
      :mask-closable="false"
      :esc-to-close="false"
      @cancel="showDetail = false"
      :footer="false"
      body-style="background-color: var(--color-fill-2)">
      <detail-com :id="selectedId!"></detail-com>
    </a-modal>

    <div class="h-full flex flex-col gap-[18px]">
      <a-card :bordered="false">
        <a-form :model="queryParams" auto-label-width>
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item show-colon label="店铺名称" field="name">
                <a-input v-model="queryParams.name" :placeholder="`${$inputPlaceholder}店铺名称`" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="店铺状态" field="businessState">
                <a-select v-model="queryParams.businessState" :options="storeBusinessStateOptions" :placeholder="`${$selectPlaceholder}店铺状态`" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="联系人姓名" field="contactName">
                <a-input v-model="queryParams.contactName" :placeholder="`${$inputPlaceholder}联系人姓名`" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="联系电话" field="contactNumber">
                <a-input-number hide-button v-model="queryParams.contactNumber" :placeholder="`${$inputPlaceholder}联系电话`" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="店铺分类">
                <a-row align="center" class="w-full">
                  <a-col :span="11">
                    <a-form-item no-style field="storeCategoryId">
                      <a-select
                        v-model="queryParams.storeCategoryId"
                        :options="storeCategoryOptions"
                        :placeholder="`${$selectPlaceholder}一级分类`"
                        @change="(e:any) =>{
                          queryParams.storeSubCategoryId = undefined
                          storeSubCategoryOptions = []
                          initStoreSubCategoryOptions(e as string)
                        }"
                        allow-clear />
                    </a-form-item>
                  </a-col>
                  <a-col :span="2" class="text-center">-</a-col>
                  <a-col :span="11">
                    <a-form-item no-style field="storeSubCategoryId">
                      <a-select v-model="queryParams.storeSubCategoryId" :options="storeSubCategoryOptions" :placeholder="`${$selectPlaceholder}二级分类`" allow-clear />
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="店铺地址">
                <a-row align="center" class="w-full">
                  <a-col :span="7">
                    <a-form-item no-style field="provinceCode">
                      <a-select
                        v-model="queryParams.provinceCode"
                        :options="provinces"
                        :placeholder="`${$selectPlaceholder}省`"
                        @change="(e:any) => {
                          queryParams.cityCode = undefined
                          queryParams.areaCode = undefined
                          cities = []
                          areas = []
                          setCities(e as number)
                        }"
                        allow-clear />
                    </a-form-item>
                  </a-col>
                  <a-col :span="1" class="text-center">-</a-col>
                  <a-col :span="7">
                    <a-form-item no-style field="cityCode">
                      <a-select
                        v-model="queryParams.cityCode"
                        :options="cities"
                        :placeholder="`${$selectPlaceholder}市`"
                        @change="(e:any) => {
                          queryParams.areaCode = undefined
                          areas = []
                          setAreas(queryParams.provinceCode!,e as number)
                        }"
                        allow-clear />
                    </a-form-item>
                  </a-col>
                  <a-col :span="1" class="text-center">-</a-col>
                  <a-col :span="8">
                    <a-form-item no-style field="areaCode">
                      <a-select v-model="queryParams.areaCode" :options="areas" :placeholder="`${$selectPlaceholder}区`" allow-clear />
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="入驻时间" field="createTime">
                <a-range-picker v-model="queryParams.createTime" :placeholder="[`${$selectPlaceholder}开始日期`, `${$selectPlaceholder}结束日期`]" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item hide-label>
                <a-space :size="18">
                  <a-button type="primary" @click="pageChange(1)">
                    <template #icon>
                      <icon-search />
                    </template>
                    查询
                  </a-button>
                  <a-button type="outline" @click="reset">
                    <template #icon>
                      <icon-refresh />
                    </template>
                    重置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <a-card :bordered="false" class="flex-1 overflow-y-hidden" :body-style="{ height: '100%' }">
        <a-row class="mb-[12px]">
          <a-col :span="16">
            <a-space>
              <a-button
                type="primary"
                @click="
                  () => {
                    selectedId = undefined
                    showAdd = true
                  }
                ">
                <template #icon>
                  <icon-plus />
                </template>
                店铺入驻
              </a-button>
              <a-button :disabled="!rows.length || delState === 1" type="primary" @click="handleExport">
                <template #icon>
                  <icon-export />
                </template>
                导出
              </a-button>
            </a-space>
          </a-col>
        </a-row>
        <!-- :selected-keys="selectedIds"
          :row-selection="{ type: 'checkbox', showCheckedAll: true, onlyCurrent: true }"
          @select-all="selectAll"
          @select="rowSelect"
          @row-click="rowClick" -->
        <a-table size="large" row-key="id" :loading="loading" :pagination="false" :data="rows" :bordered="{ cell: true }" :scroll="{ y: 'calc(100% - 96px)' }">
          <template #columns>
            <a-table-column align="center" title="序号" :width="80">
              <template #cell="{ rowIndex }">
                {{ pagination.pageSize! * (pagination.current! - 1) + rowIndex + 1 }}
              </template>
            </a-table-column>
            <a-table-column align="center" title="店铺信息" :width="300" ellipsis tooltip>
              <template #cell="{ record }">
                <a-space>
                  <a-avatar :size="32">
                    <img :src="record.logo" />
                  </a-avatar>
                  <div class="text-left">
                    <p>店铺编号：{{ record.storeNo }}</p>
                    <p class="pt-1 w-[210px] truncate">店铺名称：{{ record.name }}</p>
                    <p class="pt-1">联系人：{{ record.contactName }}</p>
                    <p class="pt-1">联系电话：{{ formatNumber(record.contactNumber) }}</p>
                  </div>
                </a-space>
              </template>
            </a-table-column>
            <a-table-column align="center" title="店铺状态" :width="150" ellipsis tooltip>
              <template #cell="{ record }">
                <a-tag v-if="record.delState === 1" color="red">已注销</a-tag>
                <a-tag v-else-if="!!record.businessState" :color="storeBusinessStateOptions.find((item) => item.value === record.businessState)?.color ?? ''">
                  {{ storeBusinessStateOptions.find((item) => item.value === record.businessState)?.label ?? '' }}
                </a-tag>
              </template>
            </a-table-column>
            <template v-if="delState === -1">
              <a-table-column align="center" title="企业信息" :width="400" ellipsis tooltip>
                <template #cell="{ record }">
                  <a-space v-if="!!record.unifiedSocialCreditCode">
                    <a-avatar shape="square" :size="32">
                      <img :src="record.license" />
                    </a-avatar>
                    <div class="text-left">
                      <p>统一社会信用代码：{{ record.unifiedSocialCreditCode }}</p>
                      <p class="pt-1 w-[280px] truncate">促进会职务：{{ record.enterpriseName }}</p>
                      <p class="pt-1">企业法人：{{ record.legalPerson }}</p>
                    </div>
                  </a-space>
                  <span v-else>-</span>
                </template>
              </a-table-column>
              <a-table-column align="center" title="店铺分类" :width="300" ellipsis tooltip>
                <template #cell="{ record }">{{ record.storeCategoryName }} - {{ record.storeSubCategoryName }}</template>
              </a-table-column>
              <a-table-column align="center" title="店铺地址" :width="300" ellipsis tooltip>
                <template #cell="{ record }">{{ record.provinceName }} - {{ record.cityName }} - {{ record.areaName }} - {{ record.detailAddress }}</template>
              </a-table-column>
              <a-table-column align="center" title="平台佣金比例" :width="180">
                <template #cell="{ record }">{{ accMul(record.platformCommissionRate, 100) }} %</template>
              </a-table-column>
              <a-table-column align="center" title="在售商品数量" :width="150" ellipsis tooltip>
                <template #cell="{ record }">{{ record.saleCommodityCount }} 个</template>
              </a-table-column>
              <a-table-column align="center" title="入驻时间" :width="180" data-index="createTime" />
              <a-table-column v-if="delState === -1" align="center" title="操作" :width="300" fixed="right">
                <template #cell="{ record }">
                  <a-space>
                    <template #split>
                      <a-divider direction="vertical" />
                    </template>
                    <a-link
                      @click.stop="
                        () => {
                          selectedId = record.id
                          showDetail = true
                        }
                      ">
                      详情
                    </a-link>
                    <a-link
                      @click.stop="
                        () => {
                          selectedId = record.id
                          showEdit = true
                        }
                      ">
                      编辑
                    </a-link>
                    <a-link status="danger" @click.stop="handleDel(record)">注销</a-link>
                  </a-space>
                </template>
              </a-table-column>
            </template>
            <template v-else>
              <a-table-column align="center" title="注销时间" :width="180" data-index="delTime" />
            </template>
          </template>
        </a-table>
        <template #actions>
          <a-pagination
            v-if="!!pagination.total"
            :current="pagination.current"
            :page-size="pagination.pageSize"
            :show-total="pagination.showTotal"
            :show-page-size="pagination.showPageSize"
            :page-size-options="pagination.pageSizeOptions"
            :total="pagination.total!"
            @change="pageChange"
            @page-size-change="pageSizeChange" />
        </template>
      </a-card>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>

<script lang="ts" setup>
import { formatNumber } from '@/utils'
import useHooks from '../hooks'
import dayjs from 'dayjs'

const props = defineProps<{
  id: string
}>()

const { form, detail } = useHooks({
  delState: -1
})

onMounted(() => {
  detail(props.id)
})
</script>

<template>
  <div class="overflow-y-scroll no-scrollbar">
    <a-descriptions bordered size="large" :column="2">
      <a-descriptions-item label="用户编号">{{ form.userNo }}</a-descriptions-item>
      <a-descriptions-item label="用户昵称">
        <div class="flex items-center gap-x-[12px]">
          <a-avatar :size="32">
            <img :src="form.avatar" />
          </a-avatar>
          <span>{{ form.nickname }}</span>
        </div>
      </a-descriptions-item>
      <a-descriptions-item label="手机号码">{{ formatNumber(form.mobile) }}</a-descriptions-item>
      <a-descriptions-item label="生日">{{ form.birthday ? form.birthday : '-' }}</a-descriptions-item>
      <a-descriptions-item label="性别">{{ form.gender === 1 ? '男' : form.gender === 2 ? '女' : '-' }}</a-descriptions-item>
      <a-descriptions-item label="所在城市">{{ !!form.areaCode ? `${form.provinceName} - ${form.cityName} - ${form.areaName}` : '-' }}</a-descriptions-item>
      <a-descriptions-item label="注册时间" :span="2">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="下级用户" :span="2">
        <a-table row-key="id" :pagination="false" :data="form.inviteUsers" :bordered="{ cell: true }" :scroll="{ y: '500px' }">
          <template #columns>
            <a-table-column align="center" title="序号">
              <template #cell="{ rowIndex }">
                {{ rowIndex + 1 }}
              </template>
            </a-table-column>
            <a-table-column align="center" title="用户信息" :width="350" ellipsis tooltip>
              <template #cell="{ record }">
                <a-space>
                  <a-avatar :size="32">
                    <img :src="record.avatar" />
                  </a-avatar>
                  <div class="text-left">
                    <p>用户编号：{{ record.userNo }}</p>
                    <p class="pt-1">用户昵称：{{ record.nickname }}</p>
                    <p class="pt-1">用户手机：{{ formatNumber(record.mobile) }}</p>
                  </div>
                </a-space>
              </template>
            </a-table-column>
            <a-table-column align="center" title="注册时间" ellipsis tooltip>
              <template #cell="{ record }">
                {{ dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss') }}
              </template>
            </a-table-column>
          </template>
        </a-table>
      </a-descriptions-item>
    </a-descriptions>
  </div>
</template>

<style lang="scss" scoped></style>

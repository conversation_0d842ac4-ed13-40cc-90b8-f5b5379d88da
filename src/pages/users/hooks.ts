import useLoading from '@/hooks/useLoading'
import usePagination from '@/hooks/usePagination'
import { IForm, IQueryParams } from './types'
import { reqGetUsers, reqAddUser, reqGetUserDetail, reqEditUser, reqChangeUserState, reqDelUser, reqRecoverUser, reqExportUser } from '@/api/apiUser'
import dayjs from 'dayjs'
import { TableData, Notification } from '@arco-design/web-vue'

const useHooks = (args: any) => {
  const { loading, setLoading } = useLoading()
  const { pagination } = usePagination()
  const initQueryParams = (): IQueryParams => {
    return {
      userNo: undefined,
      nickname: undefined,
      mobile: undefined,
      state: undefined,
      createTime: undefined
    }
  }
  const queryParams = reactive<IQueryParams>(initQueryParams())
  const selectedId = ref<string | undefined>(undefined)
  const selectedRow = computed(() => {
    return !selectedId.value ? null : rows.value.find((item) => item.id === selectedId.value)
  })
  const selectedIds = ref<string[]>([])
  const selectedRows = computed(() => {
    return !selectedIds.value.length ? [] : rows.value.filter((item) => selectedIds.value.includes(item.id))
  })
  // 单选
  // const rowSelect = (rowKeys: (string | number)[], rowKey: string | number, record: TableData): void => {
  //   selectedId.value = selectedId.value === record.id ? 0 : record.id
  // }
  // const rowClick = (record: TableData): void => {
  //   selectedId.value = selectedId.value === record.id ? 0 : record.id
  // }
  // 多选
  const selectAll = (checked: boolean): void => {
    selectedIds.value = checked ? rows.value.map((item) => item.id) : []
  }
  const rowSelect = (rowKeys: (string | number)[], rowKey: string | number, record: TableData): void => {
    selectedIds.value.includes(record.id) ? selectedIds.value.splice(selectedIds.value.indexOf(record.id), 1) : selectedIds.value.push(record.id)
  }
  const rowClick = (record: TableData): void => {
    selectedIds.value.includes(record.id) ? selectedIds.value.splice(selectedIds.value.indexOf(record.id), 1) : selectedIds.value.push(record.id)
  }
  const rows = ref<any[]>([])
  const query = async () => {
    setLoading(true)
    selectedId.value = undefined
    selectedIds.value = []
    try {
      const { data } = await reqGetUsers({ ...queryParams, ...args, mobile: queryParams.mobile?.toString(), pageNum: pagination.current!, pageSize: pagination.pageSize! })
      rows.value = (data.rows as any[]).map((item) => {
        item.createTime = dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss')
        item.delTime = item.delTime ? dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') : ''
        return item
      })
      pagination.total = data.total as number
      setLoading(false)
    } catch (error) {
      rows.value = []
      pagination.total = 0
      setLoading(false)
    }
  }
  const reset = () => {
    pagination.current = 1
    Object.assign(queryParams, initQueryParams())
    query()
  }
  const pageChange = async (current: number) => {
    pagination.current = current
    query()
  }
  const pageSizeChange = async (pageSize: number) => {
    pagination.current = 1
    pagination.pageSize = pageSize
    query()
  }
  const initForm = (): IForm => {
    return {
      avatar: undefined,
      userNo: undefined,
      nickname: undefined,
      mobile: undefined,
      birthday: undefined,
      gender: undefined,
      state: undefined,
      provinceCode: undefined,
      provinceName: undefined,
      cityCode: undefined,
      cityName: undefined,
      areaCode: undefined,
      areaName: undefined,
      createTime: undefined,
      inviteUsers: []
    }
  }
  const form = reactive<IForm>(initForm())
  const add = async (data: IForm) => {
    try {
      await reqAddUser(data)
    } catch (error) {
      throw error
    }
  }
  const detail = async (id: string) => {
    try {
      const { data } = await reqGetUserDetail(id)
      const { avatar, userNo, nickname, mobile, birthday, gender, state, provinceCode, provinceName, cityCode, cityName, areaCode, areaName, createTime, inviteUsers } = data
      Object.assign(form, {
        avatar,
        userNo,
        nickname,
        mobile,
        birthday,
        gender,
        state,
        provinceCode,
        provinceName,
        cityCode,
        cityName,
        areaCode,
        areaName,
        createTime: dayjs(createTime).format('YYYY-MM-DD HH:mm:ss'),
        inviteUsers
      })
    } catch (error) {
      throw error
    }
  }
  const edit = async (id: string, data: any) => {
    try {
      await reqEditUser(id, data)
    } catch (error) {
      throw error
    }
  }
  const del = async (id: string) => {
    try {
      await reqDelUser(id)
    } catch (error) {
      throw error
    }
  }
  const recover = async (id: string) => {
    try {
      await reqRecoverUser(id)
    } catch (error) {
      throw error
    }
  }
  const changeState = async (id: string, state: number) => {
    try {
      await reqChangeUserState(id, state)
    } catch (error) {
      throw error
    }
  }
  const exports = async () => {
    try {
      await reqExportUser()
    } catch (error) {
      throw error
    }
  }
  return {
    loading,
    queryParams,
    pagination,
    rows,
    selectedId,
    selectedRow,
    selectedIds,
    selectedRows,
    selectAll,
    rowSelect,
    rowClick,
    query,
    reset,
    pageChange,
    pageSizeChange,
    form,
    add,
    detail,
    edit,
    del,
    recover,
    changeState,
    exports
  }
}
export default useHooks

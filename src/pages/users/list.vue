<script lang="ts" setup>
import useCommon from '@/hooks/useCommon'
import useHooks from './hooks'
import { Modal, Notification, TableData } from '@arco-design/web-vue'
import { accMul, formatNumber } from '@/utils'

const props = defineProps<{
  delState: number
}>()

const DetailCom = defineAsyncComponent(() => import('./components/detail.vue'))

const { userStateOptions } = useCommon()
const { loading, queryParams, pagination, rows, selectedId, selectedIds, selectAll, rowSelect, rowClick, query, reset, pageChange, pageSizeChange, changeState, add, del, recover, exports } = useHooks({
  delState: props.delState
})

const handleChangeState = (record: TableData) => {
  Modal.warning({
    title: '提示',
    content: () => h('div', { class: 'text-center' }, `确定${record.state === -1 ? '启用' : '禁用'}【${record.nickname}】？`),
    maskClosable: false,
    escToClose: false,
    hideCancel: false,
    cancelButtonProps: { type: 'outline' },
    onBeforeOk: async (done: (closed: boolean) => void): Promise<void | boolean> => {
      try {
        await changeState(record.id, record.state === -1 ? 1 : -1)
        done(true)
        query()
      } catch (error) {
        done(false)
      }
    }
  })
}
const showDetail = ref<boolean>(false)
const handleDel = (record: TableData) => {
  Modal.warning({
    title: '提示',
    content: () => h('div', { class: 'text-center' }, `确定注销【${record.nickname}】？`),
    maskClosable: false,
    escToClose: false,
    hideCancel: false,
    cancelButtonProps: { type: 'outline' },
    onBeforeOk: async (done: (closed: boolean) => void): Promise<void | boolean> => {
      try {
        await del(record.id)
        Notification.success({
          title: '成功提示',
          content: `用户已注销`,
          duration: 1500
        })
        done(true)
        query()
      } catch (error) {
        done(false)
      }
    }
  })
}
const handleRecover = (record: TableData) => {
  Modal.confirm({
    title: '提示',
    content: () => h('div', { class: 'text-center' }, `确定恢复【${record.nickname}】？`),
    maskClosable: false,
    escToClose: false,
    hideCancel: false,
    cancelButtonProps: { type: 'outline' },
    onBeforeOk: async (done: (closed: boolean) => void): Promise<void | boolean> => {
      try {
        await recover(record.id)
        Notification.success({
          title: '成功提示',
          content: `用户已恢复`,
          duration: 1500
        })
        done(true)
        query()
      } catch (error) {
        done(false)
      }
    }
  })
}
const handleExport = () => {
  try {
    Modal.warning({
      title: '提示',
      content: () => h('div', { class: 'text-center' }, `确定导出所有用户信息？`),
      maskClosable: false,
      escToClose: false,
      hideCancel: false,
      cancelButtonProps: { type: 'outline' },
      onBeforeOk: async (done: (closed: boolean) => void): Promise<void | boolean> => {
        try {
          await exports()
          Notification.success({
            title: '操作提示',
            content: `已导出所有用户信息`,
            duration: 1500
          })
          done(true)
        } catch (error) {
          done(false)
        }
      }
    })
  } catch (error) {}
}
onMounted(() => {
  pageChange(1)
})
</script>

<template>
  <div class="pt-[18px] h-full">
    <a-modal
      v-model:visible="showDetail"
      :width="1000"
      title-align="start"
      :title="`用户详情`"
      :cancel-button-props="{ type: 'outline' }"
      unmount-on-close
      :mask-closable="false"
      :esc-to-close="false"
      @cancel="showDetail = false"
      :footer="false">
      <detail-com :id="selectedId!"></detail-com>
    </a-modal>
    <div class="h-full flex flex-col gap-[18px]">
      <a-card :bordered="false">
        <a-form :model="queryParams" auto-label-width>
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item show-colon label="用户编号" field="userNo">
                <a-input v-model="queryParams.userNo" :placeholder="`${$inputPlaceholder}用户编号`" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="用户昵称" field="nickname">
                <a-input v-model="queryParams.nickname" :placeholder="`${$inputPlaceholder}用户昵称`" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="手机号" field="mobile">
                <a-input-number hide-button v-model="queryParams.mobile" :placeholder="`${$inputPlaceholder}手机号`" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="用户状态" field="state">
                <a-select v-model="queryParams.state" :options="userStateOptions" :placeholder="`${$selectPlaceholder}用户状态`" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="注册时间" field="createTime">
                <a-range-picker v-model="queryParams.createTime" :placeholder="[`${$selectPlaceholder}开始日期`, `${$selectPlaceholder}结束日期`]" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item hide-label>
                <a-space :size="18">
                  <a-button type="primary" @click="pageChange(1)">
                    <template #icon>
                      <icon-search />
                    </template>
                    查询
                  </a-button>
                  <a-button type="outline" @click="reset">
                    <template #icon>
                      <icon-refresh />
                    </template>
                    重置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <a-card :bordered="false" class="flex-1 overflow-y-hidden" :body-style="{ height: '100%' }">
        <a-row class="mb-[12px]">
          <a-col :span="16">
            <a-space>
              <a-button :disabled="!rows.length || delState === 1" type="primary" @click="handleExport">
                <template #icon>
                  <icon-export />
                </template>
                导出
              </a-button>
            </a-space>
          </a-col>
        </a-row>
        <!-- :selected-keys="selectedIds"
          :row-selection="{ type: 'checkbox', showCheckedAll: true, onlyCurrent: true }"
          @select-all="selectAll"
          @select="rowSelect"
          @row-click="rowClick" -->
        <a-table size="large" row-key="id" :loading="loading" :pagination="false" :data="rows" :bordered="{ cell: true }" :scroll="{ y: 'calc(100% - 96px)' }">
          <template #columns>
            <a-table-column align="center" title="序号" :width="80">
              <template #cell="{ rowIndex }">
                {{ pagination.pageSize! * (pagination.current! - 1) + rowIndex + 1 }}
              </template>
            </a-table-column>
            <a-table-column align="center" title="用户信息" :width="350" ellipsis tooltip>
              <template #cell="{ record }">
                <a-space>
                  <a-avatar :size="32">
                    <img :src="record.avatar" />
                  </a-avatar>
                  <div class="text-left">
                    <p>用户编号：{{ record.userNo }}</p>
                    <p class="pt-1">用户昵称：{{ record.nickname }}</p>
                    <p class="pt-1">用户手机：{{ formatNumber(record.mobile) }}</p>
                  </div>
                </a-space>
              </template>
            </a-table-column>
            <a-table-column align="center" title="用户状态" :width="100">
              <template #cell="{ record }">
                <a-tag v-if="record.delState === 1" color="red">已注销</a-tag>
                <a-tag v-else :color="userStateOptions.find((item) => item.value === record.state)?.color ?? ''">{{ userStateOptions.find((item) => item.value === record.state)?.label ?? '' }}</a-tag>
              </template>
            </a-table-column>
            <template v-if="delState === -1">
              <a-table-column align="center" title="佣金信息" :width="300" ellipsis tooltip>
                <template #cell="{ record }">
                  <a-space>
                    <div class="text-left">
                      <p class="pt-1">可提现金额：{{ record.activityAmount?.toFixed(2) }} 元</p>
                      <p class="pt-1">不可提现金额：{{ record.freezingAmount?.toFixed(2) }} 元</p>
                    </div>
                  </a-space>
                </template>
              </a-table-column>
              <a-table-column align="center" title="消费金额" :width="150" ellipsis tooltip>
                <template #cell="{ record }">{{ record.consumeAmount?.toFixed(2) }} 元</template>
              </a-table-column>
              <a-table-column align="center" title="上级推荐人" :width="300" ellipsis tooltip>
                <template #cell="{ record }">
                  <a-space v-if="record.parentUser?.id">
                    <a-avatar :size="32">
                      <img :src="record.parentUser.avatar" />
                    </a-avatar>
                    <div class="text-left">
                      <p>用户编号：{{ record.parentUser.userNo }}</p>
                      <p class="pt-1">用户昵称：{{ record.parentUser.nickname }}</p>
                      <p class="pt-1">用户手机：{{ formatNumber(record.parentUser.mobile) }}</p>
                    </div>
                  </a-space>
                  <span v-else>系统</span>
                </template>
              </a-table-column>
              <a-table-column align="center" title="推广下级" :width="150" ellipsis tooltip>
                <template #cell="{ record }">{{ record.inviteUserCount }} 人</template>
              </a-table-column>
              <a-table-column align="center" title="向下级收取佣金比例" :width="180">
                <template #cell="{ record }">{{ accMul(record.commissionRate, 100) }} %</template>
              </a-table-column>
              <a-table-column align="center" title="注册时间" :width="180" data-index="createTime" />
              <a-table-column align="center" title="操作" :width="300" fixed="right">
                <template #cell="{ record }">
                  <a-space>
                    <template #split>
                      <a-divider direction="vertical" />
                    </template>
                    <a-link
                      @click.stop="
                        () => {
                          selectedId = record.id
                          showDetail = true
                        }
                      ">
                      详情
                    </a-link>
                    <a-link status="warning" @click.stop="handleChangeState(record)">
                      {{ record.state === -1 ? '启用' : '禁用' }}
                    </a-link>
                    <a-link status="danger" @click.stop="handleDel(record)">注销</a-link>
                  </a-space>
                </template>
              </a-table-column>
            </template>
            <template v-else>
              <a-table-column align="center" title="注销时间" :width="180" data-index="delTime" />
              <a-table-column align="center" title="操作" :width="100" fixed="right">
                <template #cell="{ record }">
                  <a-space>
                    <template #split>
                      <a-divider direction="vertical" />
                    </template>
                    <a-link @click.stop="handleRecover(record)">恢复</a-link>
                  </a-space>
                </template>
              </a-table-column>
            </template>
          </template>
        </a-table>
        <template #actions>
          <a-pagination
            v-if="!!pagination.total"
            :current="pagination.current"
            :page-size="pagination.pageSize"
            :show-total="pagination.showTotal"
            :show-page-size="pagination.showPageSize"
            :page-size-options="pagination.pageSizeOptions"
            :total="pagination.total!"
            @change="pageChange"
            @page-size-change="pageSizeChange" />
        </template>
      </a-card>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>

<script lang="ts" setup>
import { FormInstance } from '@arco-design/web-vue'
import useCommon from '@/hooks/useCommon'
import useHooks from '../hooks'
import { useTemplateRef } from 'vue'

const props = defineProps<{
  type: 'add' | 'edit'
  id?: string
}>()
const { allStoreCategoryOptions, initAllStoreCategoryOptions } = useCommon()
const { form, detail } = useHooks()

const formRef = useTemplateRef<FormInstance>('formRef')

onMounted(() => {
  initAllStoreCategoryOptions()
  if (props.type === 'edit' && !!props.id) {
    detail(props.id)
  } else if (props.type === 'add' && !!props.id) {
    form.parentId = props.id
  }
})
defineExpose({
  formRef,
  form
})
</script>

<template>
  <div class="overflow-y-scroll no-scrollbar">
    <a-form ref="formRef" :model="form" auto-label-width>
      <a-form-item v-if="!!form.parentId" :disabled="!!form.parentId" label="所属分类" field="parentId" :rules="[{ required: true, message: `${$selectPlaceholder}所属分类` }]">
        <a-select v-model="form.parentId" :options="allStoreCategoryOptions" :placeholder="$selectPlaceholder" />
      </a-form-item>
      <a-form-item show-colon label="分类图标" field="icon" :rules="[{ required: true, message: `${$uploadPlaceholder}分类图标` }]">
        <template #extra>
          <div>请上传200px*200px的.png图片</div>
        </template>
        <upload-card v-model="form.icon" accept="image/*" title="上传分类图标"></upload-card>
      </a-form-item>
      <a-form-item label="分类名称" field="name" :rules="[{ required: true, message: `${$inputPlaceholder}分类名称` }]">
        <a-input v-model="form.name" :placeholder="$inputPlaceholder" />
      </a-form-item>
    </a-form>
  </div>
</template>

<style lang="scss" scoped></style>

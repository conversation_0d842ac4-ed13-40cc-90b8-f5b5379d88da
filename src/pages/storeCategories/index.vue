<script lang="ts" setup>
import { useTemplateRef } from 'vue'
import useCommon from '@/hooks/useCommon'
import useHooks from './hooks'
import { Modal, Notification, TableData } from '@arco-design/web-vue'

const InfoCom = defineAsyncComponent(() => import('./components/info.vue'))
// const DetailCom = defineAsyncComponent(() => import('./components/detail.vue'))
const { storeCategoryOptions, initStoreCategoryOptions, storeSubCategoryOptions, initStoreSubCategoryOptions } = useCommon()
const { loading, queryParams, pagination, rows, selectedId, selectedIds, selectAll, rowSelect, rowClick, query, reset, pageChange, pageSizeChange, add, edit, del, setSortIndex } = useHooks()
const addRef = useTemplateRef<InstanceType<typeof InfoCom>>('addRef')
const showAdd = ref<boolean>(false)
const submitAdd = async (done: (closed: boolean) => void): Promise<void | boolean> => {
  try {
    const errors = await addRef.value?.formRef?.validate()
    if (!!errors) throw new Error('校验失败')
    await add(toRaw(addRef.value!.form))
    Notification.success({
      title: '成功提示',
      content: `已添加店铺分类`,
      duration: 1500
    })
    done(true)
    query()
  } catch (error) {
    done(false)
  }
}
const editRef = useTemplateRef<InstanceType<typeof InfoCom>>('editRef')
const showEdit = ref<boolean>(false)
const submitEdit = async (done: (closed: boolean) => void): Promise<void | boolean> => {
  try {
    const errors = await editRef.value?.formRef?.validate()
    if (!!errors) throw new Error('校验失败')
    await edit(selectedId.value!, toRaw(editRef.value!.form))
    Notification.success({
      title: '成功提示',
      content: `已修改店铺分类`,
      duration: 1500
    })
    done(true)
    query()
  } catch (error) {
    done(false)
  }
}
const submitSetSortIndex = async (record: TableData) => {
  try {
    await setSortIndex(record.id, record.sortIndex)
    Notification.success({
      title: '成功提示',
      content: `已设置店铺分类顺序`,
      duration: 1500
    })
    query()
  } catch (error) {}
}
const handleDel = (record: TableData) => {
  try {
    Modal.warning({
      title: '提示',
      content: () => h('div', { class: 'text-center' }, `确定删除【${record.name}】？`),
      maskClosable: false,
      escToClose: false,
      hideCancel: false,
      cancelButtonProps: { type: 'outline' },
      onBeforeOk: async (done: (closed: boolean) => void): Promise<void | boolean> => {
        try {
          await del(record.id)
          Notification.success({
            title: '成功提示',
            content: `已删除店铺分类`,
            duration: 1500
          })
          done(true)
          query()
        } catch (error) {
          done(false)
        }
      }
    })
  } catch (error) {}
}
onMounted(() => {
  initStoreCategoryOptions()
  pageChange(1)
})
</script>

<template>
  <div class="page-container">
    <a-modal
      v-model:visible="showAdd"
      title-align="start"
      :title="`添加店铺分类`"
      :cancel-button-props="{ type: 'outline' }"
      unmount-on-close
      :mask-closable="false"
      :esc-to-close="false"
      :on-before-ok="submitAdd"
      @cancel="showAdd = false">
      <info-com ref="addRef" type="add" :id="selectedId"></info-com>
    </a-modal>
    <a-modal
      v-model:visible="showEdit"
      title-align="start"
      :title="`修改店铺分类`"
      :cancel-button-props="{ type: 'outline' }"
      unmount-on-close
      :mask-closable="false"
      :esc-to-close="false"
      :on-before-ok="submitEdit"
      @cancel="showEdit = false">
      <info-com ref="editRef" type="edit" :id="selectedId"></info-com>
    </a-modal>
    <div class="h-full flex flex-col gap-[18px]">
      <a-card :bordered="false">
        <a-form :model="queryParams" auto-label-width>
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item show-colon label="店铺分类">
                <a-row align="center" class="w-full">
                  <a-col :span="11">
                    <a-form-item no-style field="parentId">
                      <a-select
                        v-model="queryParams.parentId"
                        :options="storeCategoryOptions"
                        :placeholder="`${$selectPlaceholder}一级分类`"
                        @change="(e:any) =>{
                          queryParams.id = undefined
                          storeSubCategoryOptions = []
                          initStoreSubCategoryOptions(e as string)
                        }"
                        allow-clear />
                    </a-form-item>
                  </a-col>
                  <a-col :span="2" class="text-center">-</a-col>
                  <a-col :span="11">
                    <a-form-item no-style field="id">
                      <a-select v-model="queryParams.id" :options="storeSubCategoryOptions" :placeholder="`${$selectPlaceholder}二级分类`" allow-clear />
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item hide-label>
                <a-space :size="18">
                  <a-button type="primary" @click="pageChange(1)">
                    <template #icon>
                      <icon-search />
                    </template>
                    查询
                  </a-button>
                  <a-button type="outline" @click="reset">
                    <template #icon>
                      <icon-refresh />
                    </template>
                    重置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <a-card :bordered="false" class="flex-1 overflow-y-hidden" :body-style="{ height: '100%' }">
        <a-row class="mb-[12px]">
          <a-col :span="16">
            <a-space>
              <a-button
                type="primary"
                @click="
                  () => {
                    selectedId = undefined
                    showAdd = true
                  }
                ">
                <template #icon>
                  <icon-plus />
                </template>
                添加一级分类
              </a-button>
            </a-space>
          </a-col>
        </a-row>
        <!-- :selected-keys="selectedIds"
          :row-selection="{ type: 'checkbox', showCheckedAll: true, onlyCurrent: true }"
          @select-all="selectAll"
          @select="rowSelect"
          @row-click="rowClick" -->
        <a-table size="large" row-key="id" :loading="loading" :pagination="false" :data="rows" :bordered="{ cell: true }" :scroll="{ y: 'calc(100% - 96px)' }">
          <template #columns>
            <a-table-column align="center" title="序号" :width="80">
              <template #cell="{ record, rowIndex }">
                <span v-if="!!record.children">
                  {{ pagination.pageSize! * (pagination.current! - 1) + rowIndex + 1 }}
                </span>
                <span v-else>{{ rowIndex + 1 }}</span>
              </template>
            </a-table-column>
            <a-table-column align="center" title="店铺分类名称" :width="200" ellipsis tooltip>
              <template #cell="{ record }">
                <a-space>
                  <a-avatar :size="32">
                    <img :src="record.icon" />
                  </a-avatar>
                  <span>{{ record.name }}</span>
                </a-space>
              </template>
            </a-table-column>
            <a-table-column align="center" title="排序" :width="160">
              <template #cell="{ record }">
                <div>
                  <a-space v-if="record.showSetSortIndex">
                    <a-input-number v-model="record.sortIndex" hide-button :min="1" :max="9999" :disabled="!record.showSetSortIndex" />
                    <a-button type="primary" @click.stop="submitSetSortIndex(record)">
                      <template #icon>
                        <icon-check />
                      </template>
                    </a-button>
                    <a-button type="primary" status="warning" @click.stop="query">
                      <template #icon>
                        <icon-close />
                      </template>
                    </a-button>
                  </a-space>
                  <span v-else>{{ record.sortIndex }}</span>
                </div>
              </template>
            </a-table-column>
            <a-table-column align="center" title="创建时间" :width="180" data-index="createTime" />
            <a-table-column align="center" title="操作" :width="400" fixed="right">
              <template #cell="{ record }">
                <a-space>
                  <template #split>
                    <a-divider direction="vertical" />
                  </template>
                  <a-link
                    v-if="!!record.children"
                    @click.stop="
                      () => {
                        selectedId = record.id
                        showAdd = true
                      }
                    ">
                    添加二级分类
                  </a-link>
                  <a-link
                    @click.stop="
                      () => {
                        selectedId = record.id
                        showEdit = true
                      }
                    ">
                    编辑
                  </a-link>
                  <a-link
                    :disabled="rows.some((item) => item.showSetSortIndex)"
                    @click.stop="
                      () => {
                        record.showSetSortIndex = true
                      }
                    ">
                    排序
                  </a-link>
                  <a-link status="danger" @click.stop="handleDel(record)">删除</a-link>
                </a-space>
              </template>
            </a-table-column>
          </template>
        </a-table>
        <template #actions>
          <a-pagination
            v-if="!!pagination.total"
            :current="pagination.current"
            :page-size="pagination.pageSize"
            :show-total="pagination.showTotal"
            :show-page-size="pagination.showPageSize"
            :page-size-options="pagination.pageSizeOptions"
            :total="pagination.total!"
            @change="pageChange"
            @page-size-change="pageSizeChange" />
        </template>
      </a-card>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>

<script lang="ts" setup>
import { useTemplateRef } from 'vue'
import { formatNumber } from '@/utils'
import useHooks from './hooks'
import { useRoute } from 'vue-router'
import { Modal, Notification, TableData } from '@arco-design/web-vue'

const route = useRoute()
const DetailCom = defineAsyncComponent(() => import('./components/detail.vue'))
const CardCom = defineAsyncComponent(() => import('./components/card.vue'))

const { loading, queryParams, pagination, rows, selectedId, selectedIds, selectAll, rowSelect, rowClick, query, reset, pageChange, pageSizeChange, approve, del, exports } = useHooks({
  approveState: route.name === 'VipApprove' ? 0 : 1
})
const showApprove = ref<boolean>(false)
const passLoading = ref<boolean>(false)
const submitPass = async () => {
  try {
    passLoading.value = true
    await approve(selectedId.value!, { approveState: 1 })
    passLoading.value = false
    showApprove.value = false
    Notification.success({
      title: '成功提示',
      content: `该会员已审核通过`,
      duration: 1500
    })
    query()
  } catch (error) {
    passLoading.value = false
  }
}
const showRefuse = ref<boolean>(false)
const approveReason = ref<string | undefined>(undefined)
const submitRefuse = async (done: (closed: boolean) => void): Promise<void | boolean> => {
  try {
    if (!approveReason.value) {
      Notification.warning({
        title: '提示',
        content: `请填写拒绝理由`,
        duration: 1500
      })
      throw new Error('校验失败')
    }
    await approve(selectedId.value!, { approveState: -1, approveReason: approveReason.value })
    Notification.success({
      title: '成功提示',
      content: `该会员已审核拒绝`,
      duration: 1500
    })
    showApprove.value = false
    done(true)
    query()
  } catch (error) {
    done(false)
  }
}
const showDetail = ref<boolean>(false)
const showCard = ref<boolean>(false)

const handleDel = (record: TableData) => {
  try {
    Modal.warning({
      title: '提示',
      content: () => h('div', { class: 'text-center' }, `确定注销会员【${record.info.name}】？`),
      maskClosable: false,
      escToClose: false,
      hideCancel: false,
      cancelButtonProps: { type: 'outline' },
      onBeforeOk: async (done: (closed: boolean) => void): Promise<void | boolean> => {
        try {
          await del(record.id)
          Notification.success({
            title: '成功提示',
            content: `已注销会员`,
            duration: 1500
          })
          done(true)
          query()
        } catch (error) {
          done(false)
        }
      }
    })
  } catch (error) {}
}

const handleExport = () => {
  try {
    Modal.warning({
      title: '提示',
      content: () => h('div', { class: 'text-center' }, `确定导出所有会员信息？`),
      maskClosable: false,
      escToClose: false,
      hideCancel: false,
      cancelButtonProps: { type: 'outline' },
      onBeforeOk: async (done: (closed: boolean) => void): Promise<void | boolean> => {
        try {
          await exports()
          Notification.success({
            title: '操作提示',
            content: `已导出所有会员信息`,
            duration: 1500
          })
          done(true)
        } catch (error) {
          done(false)
        }
      }
    })
  } catch (error) {}
}
onMounted(() => {
  pageChange(1)
})
</script>

<template>
  <div class="page-container">
    <a-modal
      :visible="showApprove"
      :width="1000"
      title-align="start"
      :title="`审核详情`"
      :cancel-button-props="{ type: 'outline' }"
      cancel-text="拒绝"
      ok-text="通过"
      unmount-on-close
      :mask-closable="false"
      :esc-to-close="false"
      @cancel="showApprove = false"
      body-style="background-color: var(--color-fill-2)">
      <template #footer>
        <a-space>
          <a-button
            type="outline"
            @click="
              () => {
                approveReason = undefined
                showRefuse = true
              }
            ">
            审核拒绝
          </a-button>
          <a-button type="primary" :loading="passLoading" @click="submitPass">审核通过</a-button>
        </a-space>
      </template>
      <detail-com :id="selectedId!"></detail-com>
    </a-modal>
    <a-modal
      v-model:visible="showRefuse"
      title-align="start"
      :title="`拒绝理由`"
      :cancel-button-props="{ type: 'outline' }"
      ok-text="提交"
      unmount-on-close
      :mask-closable="false"
      :esc-to-close="false"
      :on-before-ok="submitRefuse">
      <a-input v-model="approveReason" :placeholder="`${$inputPlaceholder}拒绝理由`" allow-clear />
    </a-modal>
    <a-modal
      v-model:visible="showDetail"
      :width="1000"
      title-align="start"
      :title="`会员详情`"
      :cancel-button-props="{ type: 'outline' }"
      unmount-on-close
      :mask-closable="false"
      :esc-to-close="false"
      @cancel="showDetail = false"
      :footer="false"
      body-style="background-color: var(--color-fill-2)">
      <detail-com :id="selectedId!"></detail-com>
    </a-modal>
    <a-modal
      v-model:visible="showCard"
      :width="1000"
      title-align="start"
      :title="`会员证`"
      :cancel-button-props="{ type: 'outline' }"
      unmount-on-close
      :mask-closable="false"
      :esc-to-close="false"
      @cancel="showCard = false"
      :footer="false"
      body-style="background-color: var(--color-fill-2)">
      <card-com :id="selectedId!"></card-com>
    </a-modal>
    <div class="h-full flex flex-col gap-[18px]">
      <a-card :bordered="false">
        <a-form :model="queryParams" auto-label-width>
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item show-colon label="申请时间" field="createTime">
                <a-range-picker v-model="queryParams.createTime" :placeholder="[`${$selectPlaceholder}开始日期`, `${$selectPlaceholder}结束日期`]" />
              </a-form-item>
            </a-col>
            <a-col :span="18">
              <a-form-item hide-label>
                <a-space :size="18">
                  <a-button type="primary" @click="pageChange(1)">
                    <template #icon>
                      <icon-search />
                    </template>
                    查询
                  </a-button>
                  <a-button type="outline" @click="reset">
                    <template #icon>
                      <icon-refresh />
                    </template>
                    重置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <a-card :bordered="false" class="flex-1 overflow-y-hidden" :body-style="{ height: '100%' }">
        <a-row v-if="route.name === 'VipList'" class="mb-[12px]">
          <a-col :span="16">
            <a-space>
              <a-button :disabled="!rows.length" type="primary" @click="handleExport">
                <template #icon>
                  <icon-export />
                </template>
                导出
              </a-button>
            </a-space>
          </a-col>
        </a-row>
        <!-- :selected-keys="selectedIds"
          :row-selection="{ type: 'checkbox', showCheckedAll: true, onlyCurrent: true }"
          @select-all="selectAll"
          @select="rowSelect"
          @row-click="rowClick" -->
        <a-table size="large" row-key="id" :loading="loading" :pagination="false" :data="rows" :bordered="{ cell: true }" :scroll="{ y: route.name === 'VipList' ? 'calc(100% - 96px)' : 'calc(100% - 52px)' }">
          <template #columns>
            <a-table-column align="center" title="序号" :width="80">
              <template #cell="{ rowIndex }">
                {{ pagination.pageSize! * (pagination.current! - 1) + rowIndex + 1 }}
              </template>
            </a-table-column>
            <a-table-column align="center" title="会员信息" :width="350" ellipsis tooltip>
              <template #cell="{ record }">
                <a-space>
                  <a-image width="30" height="40" fit="cover" :preview="false" :src="record.photo" />
                  <div class="text-left">
                    <p v-if="route.name === 'VipList'">会员编号：{{ record.vipNo }}</p>
                    <p class="pt-1 w-[210px] truncate">姓名：{{ record.info?.name }}</p>
                    <p class="pt-1">身份证号：{{ formatNumber(record.info?.identityNumber) }}</p>
                  </div>
                </a-space>
              </template>
            </a-table-column>
            <a-table-column align="center" title="申请用户" :width="350" ellipsis tooltip>
              <template #cell="{ record }">
                <a-space>
                  <a-avatar :size="32">
                    <img :src="record.applyUser?.avatar" />
                  </a-avatar>
                  <div class="text-left">
                    <p>用户编号：{{ record.applyUser?.userNo }}</p>
                    <p class="pt-1">用户昵称：{{ record.applyUser?.nickname }}</p>
                    <p class="pt-1">用户手机：{{ formatNumber(record.applyUser?.mobile) }}</p>
                  </div>
                </a-space>
              </template>
            </a-table-column>
            <a-table-column align="center" title="会员类型" :width="150" ellipsis tooltip>
              <template #cell="{ record }">{{ record.type === 1 ? '个人' : '单位' }}</template>
            </a-table-column>
            <a-table-column align="center" title="申请时间" :width="180" data-index="createTime" />
            <a-table-column align="center" title="操作" :width="300" fixed="right">
              <template #cell="{ record }">
                <a-space>
                  <template #split>
                    <a-divider direction="vertical" />
                  </template>
                  <a-link
                    v-if="route.name === 'VipApprove'"
                    @click.stop="
                      () => {
                        selectedId = record.id
                        showApprove = true
                      }
                    ">
                    审核
                  </a-link>
                  <template v-if="route.name === 'VipList'">
                    <a-link
                      @click.stop="
                        () => {
                          selectedId = record.id
                          showDetail = true
                        }
                      ">
                      详情
                    </a-link>
                    <a-link
                      @click.stop="
                        () => {
                          selectedId = record.id
                          showCard = true
                        }
                      ">
                      会员证
                    </a-link>
                    <a-link status="danger" @click.stop="handleDel(record)">注销</a-link>
                  </template>
                </a-space>
              </template>
            </a-table-column>
          </template>
        </a-table>
        <template #actions>
          <a-pagination
            v-if="!!pagination.total"
            :current="pagination.current"
            :page-size="pagination.pageSize"
            :show-total="pagination.showTotal"
            :show-page-size="pagination.showPageSize"
            :page-size-options="pagination.pageSizeOptions"
            :total="pagination.total!"
            @change="pageChange"
            @page-size-change="pageSizeChange" />
        </template>
      </a-card>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>

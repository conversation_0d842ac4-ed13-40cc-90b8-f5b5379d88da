<script lang="ts" setup>
import { formatNumber } from '@/utils'
import { FormInstance } from '@arco-design/web-vue'
import useHooks from '../hooks'
import { useTemplateRef } from 'vue'

const props = defineProps<{
  id: string
}>()
const { form, detail } = useHooks(1)

onMounted(() => {
  detail(props.id)
})
const formRef = useTemplateRef<FormInstance>('formRef')
defineExpose({
  formRef,
  form
})
</script>

<template>
  <div class="overflow-y-scroll no-scrollbar">
    <a-card :bordered="false">
      <div class="relative">
        <img src="@/assets/images/vip_bg.jpg" alt="" class="w-full" />
        <div class="absolute z-10 left-0 top-0 w-full h-full text-[#f3ed95] text-[38px] my-text">
          <div class="absolute left-[260px] top-[145px]">{{ form.info.name }}</div>
          <div class="absolute left-[260px] top-[227px]">{{ form.info.birth }}</div>
          <div class="absolute left-[260px] top-[311px]">{{ form.vipNo }}</div>
          <img :src="form.photo" class="w-[245px] h-[322px] absolute left-[614px] top-[144px]" />
        </div>
      </div>
    </a-card>
  </div>
</template>

<style lang="scss" scoped>
.my-text {
  font-family: fangsong, Georgia, 'Times New Roman', serif;
}
</style>

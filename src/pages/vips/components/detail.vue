<script lang="ts" setup>
import { FormInstance } from '@arco-design/web-vue'
import useHooks from '../hooks'
import { useTemplateRef } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const props = defineProps<{
  id: string
}>()
const { form, detail } = useHooks(1)

onMounted(() => {
  detail(props.id)
})
const formRef = useTemplateRef<FormInstance>('formRef')
defineExpose({
  formRef,
  form
})
</script>

<template>
  <div class="h-[700px] overflow-y-scroll no-scrollbar">
    <div class="h-full flex flex-col gap-[18px]">
      <a-card :bordered="false">
        <a-descriptions bordered size="large" :column="1">
          <a-descriptions-item label="申请主体">{{ form.type === 1 ? '个人' : '单位' }}</a-descriptions-item>
          <a-descriptions-item label="个人证件照">
            <upload-card :modelValue="form.photo" disabled></upload-card>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
      <a-card :bordered="false" title="基本信息">
        <a-descriptions bordered size="large" :column="2">
          <a-descriptions-item v-if="route.name === 'VipList'" label="会员编号" :span="2">{{ form.vipNo }}</a-descriptions-item>
          <template v-if="form.type === 1">
            <a-descriptions-item label="姓名">{{ form.info.name || '-' }}</a-descriptions-item>
            <a-descriptions-item label="身份证号码">{{ form.info.identityNumber || '-' }}</a-descriptions-item>
            <a-descriptions-item label="民族">{{ form.info.ethnicGroup || '-' }}</a-descriptions-item>
            <a-descriptions-item label="性别">{{ form.info.gender || '-' }}</a-descriptions-item>
            <a-descriptions-item label="出生年月">{{ form.info.birth || '-' }}</a-descriptions-item>
            <a-descriptions-item label="政治面貌">{{ form.info.politicalOutlook || '-' }}</a-descriptions-item>
            <a-descriptions-item label="文化程度">{{ form.info.education || '-' }}</a-descriptions-item>
            <a-descriptions-item label="工作单位">{{ form.info.workplace || '-' }}</a-descriptions-item>
            <a-descriptions-item label="职务">{{ form.info.office || '-' }}</a-descriptions-item>
            <a-descriptions-item label="办公电话">{{ form.info.workPhone || '-' }}</a-descriptions-item>
            <a-descriptions-item label="家庭住址">{{ form.info.homeAddress || '-' }}</a-descriptions-item>
            <a-descriptions-item label="住宅电话">{{ form.info.homePhone || '-' }}</a-descriptions-item>
            <a-descriptions-item label="入会介绍人">{{ form.info.introducePerson || '-' }}</a-descriptions-item>
            <a-descriptions-item label="介绍人联系方式">{{ form.info.introducePhone || '-' }}</a-descriptions-item>
            <a-descriptions-item label="特（专）长" :span="2">{{ form.info.specialty || '-' }}</a-descriptions-item>
            <a-descriptions-item label="个人简历" :span="2">{{ form.info.resume || '-' }}</a-descriptions-item>
          </template>
          <template v-if="form.type === 2">
            <a-descriptions-item label="姓名">{{ form.info.name || '-' }}</a-descriptions-item>
            <a-descriptions-item label="身份证号码">{{ form.info.identityNumber || '-' }}</a-descriptions-item>
            <a-descriptions-item label="出生年月">{{ form.info.birth || '-' }}</a-descriptions-item>
            <a-descriptions-item label="社团名称">{{ form.info.societyName || '-' }}</a-descriptions-item>
            <a-descriptions-item label="单位名称">{{ form.info.workplaceName || '-' }}</a-descriptions-item>
            <a-descriptions-item label="单位性质">{{ form.info.workplaceNature || '-' }}</a-descriptions-item>
            <a-descriptions-item label="工作内容">{{ form.info.JobDescription || '-' }}</a-descriptions-item>
            <a-descriptions-item label="法定代表人">{{ form.info.legalPerson || '-' }}</a-descriptions-item>
            <a-descriptions-item label="职务">{{ form.info.office || '-' }}</a-descriptions-item>
            <a-descriptions-item label="职称">{{ form.info.jobTitle || '-' }}</a-descriptions-item>
            <a-descriptions-item label="通讯地址">{{ form.info.address || '-' }}</a-descriptions-item>
            <a-descriptions-item label="联系电话">{{ form.info.contactNumber || '-' }}</a-descriptions-item>
            <a-descriptions-item label="入会介绍人">{{ form.info.introducePerson || '-' }}</a-descriptions-item>
            <a-descriptions-item label="介绍人联系方式">{{ form.info.introducePhone || '-' }}</a-descriptions-item>
            <a-descriptions-item label="个人简历" :span="2">{{ form.info.resume || '-' }}</a-descriptions-item>
            <a-descriptions-item label="入会理由" :span="2">{{ form.info.joinInReason || '-' }}</a-descriptions-item>
          </template>
        </a-descriptions>
      </a-card>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.my-text {
  font-family: fangsong, Georgia, 'Times New Roman', serif;
}
</style>

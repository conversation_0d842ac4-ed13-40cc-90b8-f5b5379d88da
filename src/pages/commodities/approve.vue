<script lang="ts" setup>
import { useTemplateRef } from 'vue'
import useCommon from '@/hooks/useCommon'
import useHooks from './hooks'
import { Modal, Notification, TableData } from '@arco-design/web-vue'

const DetailCom = defineAsyncComponent(() => import('./components/detail.vue'))

const { storeOptions, initStoreOptions, saleStateOptions, commodityTypeOptions, commodityCategoryOptions, initCommodityCategoryOptions } = useCommon()
const { loading, queryParams, pagination, rows, selectedId, selectedIds, selectAll, rowSelect, rowClick, query, reset, pageChange, pageSizeChange, approve } = useHooks(0)
const showApprove = ref<boolean>(false)
const passLoading = ref<boolean>(false)
const submitPass = async () => {
  try {
    passLoading.value = true
    await approve(selectedId.value!, { approveState: 1 })
    passLoading.value = false
    showApprove.value = false
    Notification.success({
      title: '成功提示',
      content: `该商品已审核通过`,
      duration: 1500
    })
    query()
  } catch (error) {
    passLoading.value = false
  }
}
const showRefuse = ref<boolean>(false)
const approveReason = ref<string | undefined>(undefined)
const submitRefuse = async (done: (closed: boolean) => void): Promise<void | boolean> => {
  try {
    if (!approveReason.value) {
      Notification.warning({
        title: '提示',
        content: `请填写拒绝理由`,
        duration: 1500
      })
      throw new Error('校验失败')
    }
    await approve(selectedId.value!, { approveState: -1, approveReason: approveReason.value })
    Notification.success({
      title: '成功提示',
      content: `该商品已审核拒绝`,
      duration: 1500
    })
    showApprove.value = false
    done(true)
    query()
  } catch (error) {
    done(false)
  }
}
onMounted(() => {
  initStoreOptions()
  initCommodityCategoryOptions()
  pageChange(1)
})
</script>

<template>
  <div class="page-container">
    <a-modal
      :visible="showApprove"
      :width="1000"
      title-align="start"
      :title="`审核详情`"
      :cancel-button-props="{ type: 'outline' }"
      cancel-text="拒绝"
      ok-text="通过"
      unmount-on-close
      :mask-closable="false"
      :esc-to-close="false"
      @cancel="showApprove = false"
      body-style="background-color: var(--color-fill-2)">
      <template #footer>
        <a-space>
          <a-button
            type="outline"
            @click="
              () => {
                approveReason = undefined
                showRefuse = true
              }
            ">
            审核拒绝
          </a-button>
          <a-button type="primary" :loading="passLoading" @click="submitPass">审核通过</a-button>
        </a-space>
      </template>
      <detail-com :id="selectedId!"></detail-com>
    </a-modal>
    <a-modal
      v-model:visible="showRefuse"
      title-align="start"
      :title="`拒绝理由`"
      :cancel-button-props="{ type: 'outline' }"
      ok-text="提交"
      unmount-on-close
      :mask-closable="false"
      :esc-to-close="false"
      :on-before-ok="submitRefuse">
      <a-input v-model="approveReason" :placeholder="`${$inputPlaceholder}拒绝理由`" allow-clear />
    </a-modal>
    <div class="h-full flex flex-col gap-[18px]">
      <a-card :bordered="false">
        <a-form :model="queryParams" auto-label-width>
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item show-colon label="商品名称" field="name">
                <a-input v-model="queryParams.name" :placeholder="`${$inputPlaceholder}商品名称`" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="店铺名称" field="storeId">
                <a-select v-model="queryParams.storeId" :options="storeOptions" :placeholder="`${$selectPlaceholder}店铺名称`" allow-search allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="商品类型" field="type">
                <a-select v-model="queryParams.type" :options="commodityTypeOptions" :placeholder="`${$selectPlaceholder}商品类型`" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="创建时间" field="createTime">
                <a-range-picker v-model="queryParams.createTime" :placeholder="[`${$selectPlaceholder}开始日期`, `${$selectPlaceholder}结束日期`]" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item hide-label>
                <a-space :size="18">
                  <a-button type="primary" @click="pageChange(1)">
                    <template #icon>
                      <icon-search />
                    </template>
                    查询
                  </a-button>
                  <a-button type="outline" @click="reset">
                    <template #icon>
                      <icon-refresh />
                    </template>
                    重置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <a-card :bordered="false" class="flex-1 overflow-y-hidden" :body-style="{ height: '100%' }">
        <!-- :selected-keys="selectedIds"
          :row-selection="{ type: 'checkbox', showCheckedAll: true, onlyCurrent: true }"
          @select-all="selectAll"
          @select="rowSelect"
          @row-click="rowClick" -->
        <a-table size="large" row-key="id" :loading="loading" :pagination="false" :data="rows" :bordered="{ cell: true }" :scroll="{ y: 'calc(100% - 52px)' }">
          <template #columns>
            <a-table-column align="center" title="序号" :width="80">
              <template #cell="{ rowIndex }">
                {{ rowIndex + 1 }}
              </template>
            </a-table-column>
            <a-table-column align="center" title="商品信息" :width="300" ellipsis tooltip>
              <template #cell="{ record }">
                <a-space>
                  <a-avatar shape="square" :size="32">
                    <img :src="record.cover" />
                  </a-avatar>
                  <div class="text-left">
                    <p>商品名称：{{ record.name }}</p>
                    <p class="pt-1">商品类型：{{ commodityTypeOptions.find((item) => item.value === record.type)?.label }}</p>
                  </div>
                </a-space>
              </template>
            </a-table-column>
            <a-table-column align="center" title="商品分类" :width="150" ellipsis tooltip data-index="commodityCategoryIName" />
            <a-table-column align="center" title="价格信息" :width="200" ellipsis tooltip>
              <template #cell="{ record }">
                <p>零售价：{{ record.retailPrice }} 元</p>
                <p>批发价：{{ record.wholesalePrice }} 元</p>
                <p class="text-gray-400 line-through text-xs">原价：{{ record.originalPrice }} 元</p>
              </template>
            </a-table-column>
            <a-table-column align="center" title="创建时间" :width="180" data-index="createTime" />
            <a-table-column align="center" title="操作" :width="100" fixed="right">
              <template #cell="{ record }">
                <a-space>
                  <template #split>
                    <a-divider direction="vertical" />
                  </template>
                  <a-link
                    @click.stop="
                      () => {
                        selectedId = record.id
                        showApprove = true
                      }
                    ">
                    审核
                  </a-link>
                </a-space>
              </template>
            </a-table-column>
          </template>
        </a-table>
        <template #actions>
          <a-pagination
            v-if="!!pagination.total"
            :current="pagination.current"
            :page-size="pagination.pageSize"
            :show-total="pagination.showTotal"
            :show-page-size="pagination.showPageSize"
            :page-size-options="pagination.pageSizeOptions"
            :total="pagination.total!"
            @change="pageChange"
            @page-size-change="pageSizeChange" />
        </template>
      </a-card>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>

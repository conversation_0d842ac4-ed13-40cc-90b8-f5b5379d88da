<script lang="ts" setup>
import { FormInstance } from '@arco-design/web-vue'
import useCommon from '@/hooks/useCommon'
import useHooks from '../hooks'
import { useTemplateRef } from 'vue'

const props = defineProps<{
  id?: string
}>()
const { commodityCategoryOptions, initCommodityCategoryOptions, commodityTypeOptions } = useCommon()
const { form, detail } = useHooks()

onMounted(() => {
  initCommodityCategoryOptions()
  if (!!props.id) detail(props.id)
})

const formRef = useTemplateRef<FormInstance>('formRef')
defineExpose({
  formRef,
  form
})
</script>

<template>
  <div class="h-[700px] overflow-y-scroll no-scrollbar">
    <div class="h-full flex flex-col gap-[18px]">
      <a-card :bordered="false" title="基本信息">
        <a-descriptions bordered size="large" :column="3">
          <a-descriptions-item label="商品类型" :span="1">
            {{ commodityTypeOptions.find((item) => item.value === form.type)?.label }}
          </a-descriptions-item>
          <a-descriptions-item label="商品名称" :span="1">{{ form.name }}</a-descriptions-item>
          <a-descriptions-item label="是否上新" :span="1">{{ form.newState === 1 ? '是' : '否' }}</a-descriptions-item>
          <a-descriptions-item label="是否热销" :span="1">{{ form.hotState === 1 ? '是' : '否' }}</a-descriptions-item>
          <a-descriptions-item label="是否推荐" :span="1">{{ form.recommendState === 1 ? '是' : '否' }}</a-descriptions-item>
          <a-descriptions-item label="商品原价" :span="1">{{ form.originalPrice?.toFixed(2) }} 元</a-descriptions-item>
          <a-descriptions-item label="商品零售价" :span="1">{{ form.retailPrice?.toFixed(2) }} 元</a-descriptions-item>
          <a-descriptions-item label="商品批发价" :span="1">{{ form.wholesalePrice?.toFixed(2) }} 元</a-descriptions-item>
          <a-descriptions-item label="商品单位" :span="1">{{ form.unit }}</a-descriptions-item>
          <a-descriptions-item label="商品简介" :span="1">{{ form.introduce }}</a-descriptions-item>
        </a-descriptions>
      </a-card>
      <a-card :bordered="false" title="商品规格">
        <a-descriptions bordered size="large" :column="1">
          <a-descriptions-item label="是否多规格商品" :span="1">{{ form.isMultiSpec === 1 ? '是' : '否' }}</a-descriptions-item>
          <a-descriptions-item v-if="form.isMultiSpec === 1" label="规格" :span="1">
            <div v-for="(field, fieldIndex) in form.specs" :key="fieldIndex" :class="{ 'pt-[18px]': fieldIndex !== 0 }">
              <div>规格名：{{ field.label }}</div>
              <div class="pt-[12px] flex items-center gap-[12px]">
                <span>规格值：</span>
                <a-tag v-for="(value, valueIndex) in field.values" :key="valueIndex">{{ value.value }}</a-tag>
              </div>
            </div>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
      <a-card :bordered="false" title="图文信息">
        <a-descriptions bordered size="large" :column="1">
          <a-descriptions-item label="封面图片" :span="1">
            <upload-card :modelValue="form.cover" disabled></upload-card>
          </a-descriptions-item>
          <a-descriptions-item label="商品图片" :span="1">
            <upload-multiple :modelValue="form.pictures" disabled></upload-multiple>
          </a-descriptions-item>
          <a-descriptions-item label="商品介绍" :span="1">
            <div v-html="form.content"></div>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>

<script lang="ts" setup>
import { FormInstance } from '@arco-design/web-vue'
import useCommon from '@/hooks/useCommon'
import useHooks from '../hooks'
import { useTemplateRef } from 'vue'

const { storeCategoryOptions, initStoreCategoryOptions } = useCommon()
const { form } = useHooks()

onMounted(() => {
  initStoreCategoryOptions()
})

const formRef = useTemplateRef<FormInstance>('formRef')
defineExpose({
  formRef,
  form
})
</script>

<template>
  <div class="overflow-y-scroll no-scrollbar">
    <a-form ref="formRef" :model="form" auto-label-width>
      <a-form-item show-colon label="分类名称" field="storeCategoryId" :rules="[{ required: true, message: `${$selectPlaceholder}分类名称` }]">
        <a-select v-model="form.storeCategoryId" :options="storeCategoryOptions" :placeholder="`${$selectPlaceholder}分类名称`" />
      </a-form-item>
    </a-form>
  </div>
</template>

<style lang="scss" scoped></style>

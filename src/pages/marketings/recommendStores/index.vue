<script lang="ts" setup>
import { useTemplateRef } from 'vue'
import useCommon from '@/hooks/useCommon'
import useHooks from './hooks'
import { Modal, Notification, TableData } from '@arco-design/web-vue'

const InfoCom = defineAsyncComponent(() => import('./components/info.vue'))
const { bannerLinkTypeOptions } = useCommon()
const { loading, queryParams, pagination, rows, selectedId, selectedIds, selectAll, rowSelect, rowClick, query, reset, pageChange, pageSizeChange, add, del, setSortIndex } = useHooks()
const addRef = useTemplateRef<InstanceType<typeof InfoCom>>('addRef')
const showAdd = ref<boolean>(false)
const submitAdd = async (done: (closed: boolean) => void): Promise<void | boolean> => {
  try {
    const errors = await addRef.value?.formRef?.validate()
    if (!!errors) throw new Error('校验失败')
    await add(toRaw(addRef.value!.form))
    Notification.success({
      title: '成功提示',
      content: `已添加`,
      duration: 1500
    })
    done(true)
    query()
  } catch (error) {
    done(false)
  }
}
const submitSetSortIndex = async (record: TableData) => {
  try {
    await setSortIndex(record.id, record.recommendSortIndex)
    Notification.success({
      title: '成功提示',
      content: `已设置顺序`,
      duration: 1500
    })
    query()
  } catch (error) {}
}
const handleDel = (record: TableData) => {
  try {
    Modal.warning({
      title: '提示',
      content: () => h('div', { class: 'text-center' }, `确定从金刚区中删除【${record.name}】？`),
      maskClosable: false,
      escToClose: false,
      hideCancel: false,
      cancelButtonProps: { type: 'outline' },
      onBeforeOk: async (done: (closed: boolean) => void): Promise<void | boolean> => {
        try {
          await del(record.id)
          Notification.success({
            title: '成功提示',
            content: `已删除`,
            duration: 1500
          })
          done(true)
          query()
        } catch (error) {
          done(false)
        }
      }
    })
  } catch (error) {}
}
onMounted(() => {
  pageChange(1)
})
</script>

<template>
  <div class="page-container">
    <a-modal
      v-model:visible="showAdd"
      title-align="start"
      :title="`添加`"
      :cancel-button-props="{ type: 'outline' }"
      unmount-on-close
      :mask-closable="false"
      :esc-to-close="false"
      :on-before-ok="submitAdd"
      @cancel="showAdd = false">
      <info-com ref="addRef"></info-com>
    </a-modal>
    <div class="h-full flex flex-col gap-[18px]">
      <a-card :bordered="false" class="flex-1 overflow-y-hidden" :body-style="{ height: '100%' }">
        <a-row class="mb-[12px]">
          <a-col :span="16">
            <a-space>
              <a-button
                type="primary"
                @click="
                  () => {
                    selectedId = undefined
                    showAdd = true
                  }
                ">
                <template #icon>
                  <icon-plus />
                </template>
                添加
              </a-button>
            </a-space>
          </a-col>
        </a-row>
        <!-- :selected-keys="selectedIds"
          :row-selection="{ type: 'checkbox', showCheckedAll: true, onlyCurrent: true }"
          @select-all="selectAll"
          @select="rowSelect"
          @row-click="rowClick" -->
        <a-table size="large" row-key="id" :loading="loading" :pagination="false" :data="rows" :bordered="{ cell: true }" :scroll="{ y: 'calc(100% - 96px)' }">
          <template #columns>
            <a-table-column align="center" title="序号" :width="80">
              <template #cell="{ rowIndex }">
                {{ pagination.pageSize! * (pagination.current! - 1) + rowIndex + 1 }}
              </template>
            </a-table-column>
            <a-table-column align="center" title="店铺名称" :width="200" ellipsis tooltip>
              <template #cell="{ record }">
                <a-space>
                  <a-avatar :size="32">
                    <img :src="record.logo" />
                  </a-avatar>
                  <span>{{ record.name }}</span>
                </a-space>
              </template>
            </a-table-column>
            <a-table-column align="center" title="排序" :width="160">
              <template #cell="{ record }">
                <div>
                  <a-space v-if="record.showSetSortIndex">
                    <a-input-number v-model="record.recommendSortIndex" hide-button :min="1" :max="9999" :disabled="!record.showSetSortIndex" />
                    <a-button type="primary" @click.stop="submitSetSortIndex(record)">
                      <template #icon>
                        <icon-check />
                      </template>
                    </a-button>
                    <a-button type="primary" status="warning" @click.stop="query">
                      <template #icon>
                        <icon-close />
                      </template>
                    </a-button>
                  </a-space>
                  <span v-else>{{ record.recommendSortIndex }}</span>
                </div>
              </template>
            </a-table-column>
            <a-table-column align="center" title="操作" :width="200" fixed="right">
              <template #cell="{ record }">
                <a-space>
                  <template #split>
                    <a-divider direction="vertical" />
                  </template>
                  <a-link
                    :disabled="rows.some((item) => item.showSetSortIndex)"
                    @click.stop="
                      () => {
                        record.showSetSortIndex = true
                      }
                    ">
                    排序
                  </a-link>
                  <a-link status="danger" @click.stop="handleDel(record)">删除</a-link>
                </a-space>
              </template>
            </a-table-column>
          </template>
        </a-table>
        <template #actions>
          <a-pagination
            v-if="!!pagination.total"
            :current="pagination.current"
            :page-size="pagination.pageSize"
            :show-total="pagination.showTotal"
            :show-page-size="pagination.showPageSize"
            :page-size-options="pagination.pageSizeOptions"
            :total="pagination.total!"
            @change="pageChange"
            @page-size-change="pageSizeChange" />
        </template>
      </a-card>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>

<script lang="ts" setup>
import { FormInstance } from '@arco-design/web-vue'
import useCommon from '@/hooks/useCommon'
import useHooks from '../hooks'
import { useTemplateRef } from 'vue'

const { storeOptions, initStoreOptions } = useCommon()
const { form } = useHooks()

onMounted(() => {
  initStoreOptions()
})

const formRef = useTemplateRef<FormInstance>('formRef')
defineExpose({
  formRef,
  form
})
</script>

<template>
  <div class="overflow-y-scroll no-scrollbar">
    <a-form ref="formRef" :model="form" auto-label-width>
      <a-form-item show-colon label="店铺名称" field="storeId" :rules="[{ required: true, message: `${$selectPlaceholder}店铺名称` }]">
        <a-select v-model="form.storeId" :options="storeOptions" :placeholder="`${$selectPlaceholder}店铺名称`" allow-search />
      </a-form-item>
    </a-form>
  </div>
</template>

<style lang="scss" scoped></style>

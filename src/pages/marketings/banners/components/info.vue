<script lang="ts" setup>
import { FormInstance } from '@arco-design/web-vue'
import useCommon from '@/hooks/useCommon'
import useHooks from '../hooks'
import { useTemplateRef } from 'vue'

const props = defineProps<{
  position: string
}>()
const { bannerLinkTypeOptions, storeCategoryOptions, initStoreCategoryOptions, storeSubCategoryOptions, initStoreSubCategoryOptions, storeOptions, initStoreOptions, activityOptions, initActivityOptions } =
  useCommon()
const { form } = useHooks(props.position)

onMounted(() => {
  initStoreCategoryOptions()
  initStoreOptions()
})

const formRef = useTemplateRef<FormInstance>('formRef')
defineExpose({
  formRef,
  form
})
</script>

<template>
  <div class="overflow-y-scroll no-scrollbar">
    <a-form ref="formRef" :model="form" auto-label-width>
      <a-form-item show-colon label="广告图" field="picturePath" :rules="[{ required: true, message: `${$uploadPlaceholder}广告图` }]">
        <upload-card v-model="form.picturePath" accept="image/*" title="上传广告图"></upload-card>
        <template #extra>请上传702px*300px图片</template>
      </a-form-item>
      <a-form-item show-colon label="展示有效期至" field="expirationEndTime" :rules="[{ required: true, message: `${$selectPlaceholder}展示有效期至` }]">
        <a-date-picker v-model="form.expirationEndTime" show-time :placeholder="`${$selectPlaceholder}展示有效期至`" />
      </a-form-item>
      <a-form-item show-colon label="链接类型" field="linkType" :rules="[{ required: true, message: `${$selectPlaceholder}链接类型` }]">
        <a-radio-group
          v-model="form.linkType"
          @change="
            () => {
              form.storeCategoryId = undefined
              form.storeSubCategoryId = undefined
              form.storeId = undefined
              form.activityId = undefined
              form.linkContent = undefined
              form.linkUrl = undefined
            }
          ">
          <a-radio v-for="carType in bannerLinkTypeOptions" :key="carType.value" :value="carType.value">
            {{ carType.label }}
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <a-row v-if="form.linkType === 1" :gutter="16">
        <a-col :span="12">
          <a-form-item show-colon label="一级分类" field="storeCategoryId" :rules="[{ required: true, message: `${$selectPlaceholder}一级分类` }]">
            <a-select
              v-model="form.storeCategoryId"
              :options="storeCategoryOptions"
              :placeholder="`${$selectPlaceholder}一级分类`"
              @change="(e:any) =>{
                  form.storeSubCategoryId = undefined
                  storeSubCategoryOptions = []
                  initStoreSubCategoryOptions(e as string)
                }" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item show-colon label="二级分类" field="storeSubCategoryId" :rules="[{ required: true, message: `${$selectPlaceholder}二级分类` }]">
            <a-select v-model="form.storeSubCategoryId" :options="storeSubCategoryOptions" :placeholder="`${$selectPlaceholder}二级分类`" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-form-item v-if="form.linkType === 2" show-colon label="店铺名称" field="storeId" :rules="[{ required: true, message: `${$selectPlaceholder}店铺名称` }]">
        <a-select v-model="form.storeId" :options="storeOptions" :placeholder="`${$selectPlaceholder}店铺名称`" allow-search allow-clear />
      </a-form-item>
      <a-form-item v-if="form.linkType === 3" show-colon label="链接地址" field="linkUrl" :rules="[{ required: true, message: `${$inputPlaceholder}链接地址` }]">
        <a-input v-model="form.linkUrl" :placeholder="`${$inputPlaceholder}链接地址`" :max-length="50" show-word-limit />
        <template #extra>请填写以https://开头的域名地址，非关联公众号链接请确保域名已添加到小程序后台的业务域名中。</template>
      </a-form-item>
      <a-row v-if="form.linkType === 4" :gutter="16">
        <a-col :span="12">
          <a-form-item show-colon label="店铺名称" field="storeId" :rules="[{ required: true, message: `${$selectPlaceholder}店铺名称` }]">
            <a-select v-model="form.storeId" :options="storeOptions" :placeholder="`${$selectPlaceholder}店铺名称`" allow-search allow-clear @change="initActivityOptions($event as string)" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item show-colon label="关联活动" field="activityId" :rules="[{ required: true, message: `${$selectPlaceholder}关联活动` }]">
            <a-select v-model="form.activityId" :disabled="!form.storeId" :options="activityOptions" :placeholder="`${$selectPlaceholder}关联活动`" allow-search allow-clear />
          </a-form-item>
        </a-col>
      </a-row>
      <!-- <a-form-item v-if="form.linkType === 5" show-colon label="链接内容" field="linkContent" :rules="[{ required: true, message: `${$inputPlaceholder}链接内容` }]">
        <rich-text-editor v-model="form.linkContent" :placeholder="`${$inputPlaceholder}链接内容`" />
      </a-form-item> -->
    </a-form>
  </div>
</template>

<style lang="scss" scoped></style>

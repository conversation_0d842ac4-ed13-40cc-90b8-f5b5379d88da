<script lang="ts" setup>
const ListCom = defineAsyncComponent(() => import('./list.vue'))
</script>

<template>
  <div class="page-container">
    <a-tabs default-active-key="1" type="rounded" lazy-load>
      <a-tab-pane key="1" title="平台首页"><list-com position="home"></list-com></a-tab-pane>
      <a-tab-pane key="2" title="分类页面"><list-com position="storeCategory"></list-com></a-tab-pane>
    </a-tabs>
  </div>
</template>

<style lang="scss" scoped>
:deep(.arco-tabs) {
  height: 100%;
}
:deep(.arco-tabs-nav) {
  background-color: var(--color-bg-2);
  padding: 16px 10px 10px;
}
:deep(.arco-tabs-content) {
  padding-top: 0;
  height: calc(100% - 58px);
  .arco-tabs-content-list {
    height: 100%;
    .arco-tabs-pane {
      height: 100%;
    }
  }
}
</style>

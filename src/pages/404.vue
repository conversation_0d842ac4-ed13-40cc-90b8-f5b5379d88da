<script lang="ts" setup>
import image from '@/assets/images/404.svg'

const toHome = () => {
  location.href = import.meta.env.VITE_BASE_PATH
}
</script>

<template>
  <div class="not-fount-container">
    <a-space :size="30" fill>
      <img :width="400" :src="image" />
      <div class="bullshit">
        <div class="bullshit__oops">404错误！</div>
        <div class="bullshit__headline">找不到网页！</div>
        <div class="bullshit__info">对不起，您正在寻找的页面不存在。尝试检查URL的错误，然后按浏览器上的刷新按钮或尝试在我们的应用程序中找到其他内容。</div>
        <a-button type="primary" @click="toHome">返回首页</a-button>
      </div>
    </a-space>
  </div>
</template>

<style lang="scss" scoped>
.not-fount-container {
  @apply flex justify-center items-center h-screen;
  .bullshit {
    @apply flex flex-col  justify-center w-[300px];
    &__oops {
      font-size: 32px;
      font-weight: bold;
      line-height: 40px;
      color: rgb(var(--primary-6));
      opacity: 0;
      margin-bottom: 20px;
      animation-name: slideUp;
      animation-duration: 0.5s;
      animation-fill-mode: forwards;
    }
    &__headline {
      font-size: 20px;
      line-height: 24px;
      color: var(--color-text-1);
      font-weight: bold;
      opacity: 0;
      margin-bottom: 10px;
      animation-name: slideUp;
      animation-duration: 0.5s;
      animation-delay: 0.1s;
      animation-fill-mode: forwards;
    }
    &__info {
      font-size: 13px;
      line-height: 21px;
      color: var(--color-text-3);
      opacity: 0;
      margin-bottom: 30px;
      animation-name: slideUp;
      animation-duration: 0.5s;
      animation-delay: 0.2s;
      animation-fill-mode: forwards;
    }
    @keyframes slideUp {
      0% {
        transform: translateY(60px);
        opacity: 0;
      }
      100% {
        transform: translateY(0);
        opacity: 1;
      }
    }
  }
}
</style>

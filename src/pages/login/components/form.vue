<script lang="ts" setup>
import { useRouter } from 'vue-router'
import { ValidatedError } from '@arco-design/web-vue'
import { DEFAULT_ROUTE_NAME } from '@/router/constants'
import useLogin from '../useLogin'

const router = useRouter()
const { loading, codeUrl, loginForm, loginConfig, getCode, login } = useLogin()
getCode()

const handleSubmit = async (e: Event | { errors: Record<string, ValidatedError> | undefined; values: Record<string, any> }) => {
  // 如果是DOM事件，阻止默认行为
  if (e instanceof Event && typeof e.preventDefault === 'function') {
    e.preventDefault()
    return
  }
  
  // 处理Arco Design表单验证结果
  const { errors, values } = e as { errors: Record<string, ValidatedError> | undefined; values: Record<string, any> }
  
  if (loading.value) return
  if (!errors) {
    try {
      await login()
      router.replace({
        name: DEFAULT_ROUTE_NAME
      })
    } catch (error) {}
  }
}
</script>

<template>
  <div class="login-form-wrapper">
    <div class="login-form-title">登录系统 · 平台端</div>
    <a-form ref="loginRef" :model="loginForm" class="login-form" layout="vertical" @submit="handleSubmit">
      <a-form-item show-colon field="username" :rules="[{ required: true, message: '账号不能为空' }]" :validate-trigger="['change', 'blur']" hide-label>
        <a-input v-model="loginForm.username" placeholder="请输入账号">
          <template #prefix>
            <icon-user />
          </template>
        </a-input>
      </a-form-item>
      <!--禁用自动填充密码-->
      <input type="text" class="fake-input" />
      <input type="password" class="fake-input" />
      <a-form-item show-colon field="password" :rules="[{ required: true, message: '密码不能为空' }]" :validate-trigger="['change', 'blur']" hide-label>
        <a-input-password v-model="loginForm.password" placeholder="请输入密码" allow-clear>
          <template #prefix>
            <icon-lock />
          </template>
        </a-input-password>
      </a-form-item>
      <a-form-item show-colon field="code" :rules="[{ required: true, message: '验证码不能为空' }]" :validate-trigger="['change', 'blur']" hide-label>
        <a-input-number hide-button v-model="loginForm.code" placeholder="请输入验证码">
          <template #prefix>
            <icon-safe />
          </template>
          <template #append>
            <a-image v-if="!codeUrl" height="30" width="80" :preview="false" :src="`${codeUrl}`">
              <template #error-icon><icon-loading :size="10" /></template>
            </a-image>
            <div v-else v-html="codeUrl" class="cursor-pointer" @click="getCode"></div>
          </template>
        </a-input-number>
      </a-form-item>
      <a-space :size="16" direction="vertical">
        <div class="login-form-password-actions">
          <a-checkbox v-model="loginConfig.rememberUsername">记住账号</a-checkbox>
        </div>
        <a-button type="primary" html-type="submit" long :loading="loading">登录</a-button>
      </a-space>
    </a-form>
  </div>
</template>

<style lang="scss" scoped>
.login-form {
  &-title {
    font-weight: 500;
    font-size: 26px;
    margin-bottom: 20px;
    text-align: center;
    font-weight: 600;
  }

  &-password-actions {
    display: flex;
    justify-content: space-between;
  }

  &-register-btn {
    color: var(--color-text-3) !important;
  }
  :deep(.arco-form-item) {
    margin-bottom: 20px;
    .arco-input-wrapper {
      height: 46px;
      border-radius: 6px;
      background-color: #edf3f7;
      .arco-input {
        font-size: 16px;
      }
    }
    .arco-input-append {
      border-radius: 6px;
      margin-left: 12px;
      border-left-width: 1px !important;
      background-color: #edf3f7;
    }
  }
  .arco-btn-size-medium {
    height: 46px;
    border-radius: 6px;
    font-size: 16px;
  }
  :deep(.arco-image-error) {
    .arco-image-error-icon {
      width: 100%;
      height: 100%;
    }
    .arco-image-error-alt {
      display: none;
    }
  }
}
</style>

import { reqGetCodeImage } from '@/api/apiCommon'
import { useStorage } from '@vueuse/core'
import { useUserStore } from '@/store'
import useLoading from '@/hooks/useLoading'
import { Notification } from '@arco-design/web-vue'

export type LoginForm = {
  username?: string
  password?: string
  code?: number
  uuid: string
}

const userStore = useUserStore()

const useLogin = () => {
  const { loading, setLoading } = useLoading()
  const codeUrl = ref<string>('')

  const loginConfig = useStorage<any>('login-storage', {
    rememberUsername: true,
    username: undefined
    // password: undefined
  })
  const initLoginForm = (): LoginForm => {
    return {
      username: loginConfig.value.username,
      password: undefined,
      // password: loginConfig.value.password,
      code: undefined,
      uuid: ''
    }
  }
  const loginForm = reactive<LoginForm>(initLoginForm())

  const getCode = async () => {
    const { data } = await reqGetCodeImage()
    codeUrl.value = data.svg as string
    loginForm.uuid = data.uuid as string
  }
  const login = async () => {
    setLoading(true)
    try {
      await userStore.login(loginForm)
      loginConfig.value.username = loginConfig.value.rememberUsername ? loginForm.username : undefined
      // loginConfig.value.password = loginConfig.value.rememberUsername ? loginForm.password : ''
    } catch (error) {
      setLoading(false)
      getCode()
      throw error
    }
  }
  return {
    loading,
    codeUrl,
    loginForm,
    loginConfig,

    getCode,
    login
  }
}

export default useLogin

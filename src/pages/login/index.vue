<script lang="ts" setup>
import LoginForm from './components/form.vue'
import Footer from '@/layout/pageFooter/index.vue'
</script>

<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-form">
        <login-form></login-form>
      </div>
    </div>
    <div class="footer">
      <Footer />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.login-page {
  @apply flex justify-center items-center h-screen bg-[#f2f6ff];
  .login-container {
    @apply flex justify-center w-[70%];
    .login-form {
      width: 492px;
      padding: 50px;
      border-radius: 10px;
      background-color: rgba(255, 255, 255, 1);
      box-shadow: 0px 20px 30px 10px rgba(55, 118, 255, 0.1);
    }
  }

  .footer {
    @apply fixed right-0 bottom-0 w-full;
  }
}
</style>

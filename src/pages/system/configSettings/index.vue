<script lang="ts" setup>
import { reqGetConfigs, reqEditConfig } from '@/api/apiConfig'
import dayjs from 'dayjs'
import { Notification } from '@arco-design/web-vue'
import { v4 as uuidv4 } from 'uuid'
import { useTemplateRef } from 'vue'
import { accMul, accDiv } from '@/utils'

const customerSettings = ref<any[]>([])

const initData = async () => {
  try {
    const { data } = await reqGetConfigs({})
    customerSettings.value = (data.filter((item: any) => item.type === 'customerSetting') as any[]).map((item) => {
      const { content, ...d } = item
      return {
        ...d,
        content: d.subType === 'platformCommissionRate' || d.subType === 'userCommissionRate' ? accMul(Number(content), 100) : content
      }
    })
  } catch (error) {
    // console.log('error :>> ', error)
  }
}
onMounted(() => {
  initData()
})
</script>

<template>
  <div class="page-container">
    <div class="h-full flex flex-col gap-[18px]">
      <a-card title="基础配置" :bordered="false">
        <a-table size="large" row-key="id" :pagination="false" :data="customerSettings" :bordered="{ cell: true }">
          <template #columns>
            <a-table-column align="center" title="名称" :width="150">
              <template #cell="{ record }">
                <a-space>
                  <span>{{ record.name }}</span>
                  <a-tooltip v-if="record.remark" :content="record.remark">
                    <icon-question-circle />
                  </a-tooltip>
                </a-space>
              </template>
            </a-table-column>
            <a-table-column align="center" title="内容" :width="300">
              <template #cell="{ record }">
                <a-input v-if="record.subType === 'contactNumber'" v-model="record.content" :placeholder="$inputPlaceholder" />
                <a-input-number v-else-if="record.subType === 'platformCommissionRate' || record.subType === 'userCommissionRate'" v-model="record.content" hide-button :placeholder="$inputPlaceholder">
                  <template #suffix>%</template>
                </a-input-number>
                <upload-card v-else v-model="record.content" accept="image/*" title="上传图片" class="!justify-center"></upload-card>
              </template>
            </a-table-column>
            <a-table-column align="center" title="操作" :width="200" fixed="right">
              <template #cell="{ record }">
                <a-link
                  @click.stop="
                    () => {
                      reqEditConfig(record.id, { content: record.subType === 'platformCommissionRate' || record.subType === 'userCommissionRate' ? accDiv(record.content, 100) : record.content }).then(() => {
                        Notification.success({
                          title: '成功提示',
                          content: `已修改${record.name}`
                        })
                      })
                    }
                  ">
                  提交
                </a-link>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </a-card>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>

<script lang="ts" setup>
import { reqGetConfigs, reqEditConfig } from '@/api/apiConfig'
import dayjs from 'dayjs'
import { Notification } from '@arco-design/web-vue'
import { v4 as uuidv4 } from 'uuid'
import { useTemplateRef } from 'vue'

const submitUserRegistrationAgreementLoading = ref<boolean>(false)
const userRegistrationAgreement = reactive<any>({
  id: undefined,
  content: undefined
})
const submitStoreRegistrationAgreementLoading = ref<boolean>(false)
const storeRegistrationAgreement = reactive<any>({
  id: undefined,
  content: undefined
})
const submitPrivacyPolicyLoading = ref<boolean>(false)
const privacyPolicy = reactive<any>({
  id: undefined,
  content: undefined
})
const submitPlatformIntroduceLoading = ref<boolean>(false)
const platformIntroduce = reactive<any>({
  id: undefined,
  content: undefined
})
const initData = async () => {
  try {
    const { data } = await reqGetConfigs({})
    Object.assign(userRegistrationAgreement, {
      id: data.find((item: any) => item.type === 'doc' && item.subType === 'userRegistrationAgreement')?.id,
      content: data.find((item: any) => item.type === 'doc' && item.subType === 'userRegistrationAgreement')?.content
    })
    Object.assign(storeRegistrationAgreement, {
      id: data.find((item: any) => item.type === 'doc' && item.subType === 'storeRegistrationAgreement')?.id,
      content: data.find((item: any) => item.type === 'doc' && item.subType === 'storeRegistrationAgreement')?.content
    })
    Object.assign(privacyPolicy, {
      id: data.find((item: any) => item.type === 'doc' && item.subType === 'privacyPolicy')?.id,
      content: data.find((item: any) => item.type === 'doc' && item.subType === 'privacyPolicy')?.content
    })
    Object.assign(platformIntroduce, {
      id: data.find((item: any) => item.type === 'doc' && item.subType === 'platformIntroduce')?.id,
      content: data.find((item: any) => item.type === 'doc' && item.subType === 'platformIntroduce')?.content
    })
  } catch (error) {
    // console.log('error :>> ', error)
  }
}
onMounted(() => {
  initData()
})
</script>

<template>
  <div class="page-container">
    <div class="h-full flex flex-col gap-[18px]">
      <a-grid :cols="2" :colGap="16" :rowGap="16">
        <a-grid-item>
          <a-card title="用户注册协议" :bordered="false">
            <rich-text-editor v-model="userRegistrationAgreement.content" :placeholder="$inputPlaceholder" />
            <a-space class="mt-3 flex justify-end" fill>
              <a-button
                :loading="submitUserRegistrationAgreementLoading"
                type="primary"
                @click="
                  () => {
                    submitUserRegistrationAgreementLoading = true
                    reqEditConfig(userRegistrationAgreement.id, { content: userRegistrationAgreement.content }).then(() => {
                      submitUserRegistrationAgreementLoading = false
                      Notification.success({
                        title: '成功',
                        content: '已修改用户注册协议'
                      })
                    })
                  }
                ">
                修改
              </a-button>
            </a-space>
          </a-card>
        </a-grid-item>
        <a-grid-item>
          <a-card title="门店注册协议" :bordered="false">
            <rich-text-editor v-model="storeRegistrationAgreement.content" :placeholder="$inputPlaceholder" />
            <a-space class="mt-3 flex justify-end" fill>
              <a-button
                :loading="submitStoreRegistrationAgreementLoading"
                type="primary"
                @click="
                  () => {
                    submitStoreRegistrationAgreementLoading = true
                    reqEditConfig(storeRegistrationAgreement.id, { content: storeRegistrationAgreement.content }).then(() => {
                      submitStoreRegistrationAgreementLoading = false
                      Notification.success({
                        title: '成功',
                        content: '已修改门店注册协议'
                      })
                    })
                  }
                ">
                修改
              </a-button>
            </a-space>
          </a-card>
        </a-grid-item>
        <a-grid-item>
          <a-card title="隐私政策" :bordered="false">
            <rich-text-editor v-model="privacyPolicy.content" :placeholder="$inputPlaceholder" />
            <a-space class="mt-3 flex justify-end" fill>
              <a-button
                :loading="submitPrivacyPolicyLoading"
                type="primary"
                @click="
                  () => {
                    submitPrivacyPolicyLoading = true
                    reqEditConfig(privacyPolicy.id, { content: privacyPolicy.content }).then(() => {
                      submitPrivacyPolicyLoading = false
                      Notification.success({
                        title: '成功',
                        content: '已修改隐私政策'
                      })
                    })
                  }
                ">
                修改
              </a-button>
            </a-space>
          </a-card>
        </a-grid-item>
        <a-grid-item>
          <a-card title="协会介绍" :bordered="false">
            <rich-text-editor v-model="platformIntroduce.content" :placeholder="$inputPlaceholder" />
            <a-space class="mt-3 flex justify-end" fill>
              <a-button
                :loading="submitPlatformIntroduceLoading"
                type="primary"
                @click="
                  () => {
                    submitPlatformIntroduceLoading = true
                    reqEditConfig(platformIntroduce.id, { content: platformIntroduce.content }).then(() => {
                      submitPlatformIntroduceLoading = false
                      Notification.success({
                        title: '成功',
                        content: '已修改协会介绍'
                      })
                    })
                  }
                ">
                修改
              </a-button>
            </a-space>
          </a-card>
        </a-grid-item>
      </a-grid>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>

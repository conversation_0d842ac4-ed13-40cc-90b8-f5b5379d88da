<script lang="ts" setup>
import { FormInstance } from '@arco-design/web-vue'
import useHooks from '../hooks'
import { useTemplateRef } from 'vue'

const props = defineProps<{
  id?: string
}>()

const { form, detail } = useHooks()

onMounted(() => {
  if (!!props.id) detail(props.id)
})
const formRef = useTemplateRef<FormInstance>('formRef')
defineExpose({
  formRef,
  form
})
</script>

<template>
  <div class="overflow-y-scroll no-scrollbar">
    <a-form ref="formRef" :model="form" auto-label-width>
      <a-form-item show-colon label="角色编号" field="roleNo" :rules="[{ required: true, message: `${$inputPlaceholder}角色编号` }]">
        <a-input v-model="form.roleNo" :placeholder="`${$inputPlaceholder}角色编号`" />
      </a-form-item>
      <a-form-item show-colon label="角色名称" field="roleName" :rules="[{ required: true, message: `${$inputPlaceholder}角色名称` }]">
        <a-input v-model="form.roleName" :placeholder="`${$inputPlaceholder}角色名称`" />
      </a-form-item>
      <a-form-item show-colon label="备注" field="remark">
        <a-textarea v-model="form.remark" :auto-size="{ minRows: 10 }" :max-length="50" show-word-limit :placeholder="`${$inputPlaceholder}备注`" />
      </a-form-item>
    </a-form>
  </div>
</template>

<style lang="scss" scoped></style>

<script lang="ts" setup>
import { DEFAULT_ROUTE_NAME } from '@/router/constants'
import { RouteRecordRaw, useRouter } from 'vue-router'
import { treeToArray } from '@/utils/auth'
import { TreeNodeData } from '@arco-design/web-vue'
import { useSideBarStore } from '@/store'

const props = defineProps<{
  permission?: { checkedKeys: string[]; halfCheckedKeys: string[] }
}>()

const sideBarStore = useSideBarStore()

const cloneTreeData = (list: any[]): any[] => {
  let results: any[] = []
  list.map((item) => {
    results.push({
      key: item.name,
      title: item.meta?.title,
      disabled: !!item.meta?.disabled,
      children: item.children ? cloneTreeData(item.children) : []
    })
  })
  return results
}
const router = useRouter()
// const allMenuTree = router.options.routes.find((item) => item.name === DEFAULT_ROUTE_NAME)?.children ?? []
const allMenuTree = computed(() => sideBarStore.getAllMenus)
const treeData: any[] = cloneTreeData(allMenuTree.value)
const allMenuKeys = treeToArray<RouteRecordRaw, string>(allMenuTree.value, 'name')
const checkedKeys = ref<string[]>([])
const halfCheckedKeys = ref<string[]>([])

const handleCheck = (
  keys: (string | number)[],
  data: { checked?: boolean; checkedNodes: TreeNodeData[]; node?: TreeNodeData; e?: Event; halfCheckedKeys: (string | number)[]; halfCheckedNodes: TreeNodeData[] }
) => {
  checkedKeys.value = keys as string[]
  halfCheckedKeys.value = data.halfCheckedKeys as string[]
}
onMounted(() => {
  checkedKeys.value = props.permission?.checkedKeys ?? []
  halfCheckedKeys.value = props.permission?.halfCheckedKeys ?? []
})

defineExpose({
  checkedKeys,
  halfCheckedKeys
})
</script>

<template>
  <!-- <a-checkbox
    :model-value="checkedKeys.length === allMenuKeys.length"
    @change="(e: any) => {
      checkedKeys = !!e ? allMenuKeys : []
    }"
    style="margin-bottom: 16px; margin-left: 18px">
    全部授权
  </a-checkbox> -->
  <a-tree :checked-keys="checkedKeys" :half-checked-keys="halfCheckedKeys" :selected-keys="checkedKeys" checkable :multiple="true" :data="treeData" @check="handleCheck" />
</template>

<style lang="scss" scoped></style>

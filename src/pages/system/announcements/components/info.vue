<script lang="ts" setup>
import { FormInstance } from '@arco-design/web-vue'
import useCommon from '@/hooks/useCommon'
import useHooks from '../hooks'
import { useTemplateRef } from 'vue'

const props = defineProps<{
  id?: string
}>()
const { storeOptions, initStoreOptions, storeCategoryOptions, initStoreCategoryOptions, storeSubCategoryOptions, initStoreSubCategoryOptions } = useCommon()
const { form, detail } = useHooks()

onMounted(async () => {
  initStoreCategoryOptions()
  initStoreOptions()
  if (!!props.id) {
    await detail(props.id)
    initStoreSubCategoryOptions(form.storeCategoryId!)
  }
})
const formRef = useTemplateRef<FormInstance>('formRef')
defineExpose({
  formRef,
  form
})
</script>

<template>
  <div class="overflow-y-scroll no-scrollbar">
    <a-form ref="formRef" :model="form" auto-label-width>
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item show-colon label="一级分类" field="storeCategoryId">
            <a-select
              v-model="form.storeCategoryId"
              :options="storeCategoryOptions"
              :placeholder="`${$selectPlaceholder}一级分类`"
              @change="(e:any) =>{
                form.storeSubCategoryId = undefined
                storeSubCategoryOptions = []
                initStoreSubCategoryOptions(e as string)
                initStoreOptions({storeCategoryId: e as string})
              }" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item show-colon label="二级分类" field="storeSubCategoryId">
            <a-select
              v-model="form.storeSubCategoryId"
              :options="storeSubCategoryOptions"
              :placeholder="`${$selectPlaceholder}二级分类`"
              @change="(e:any) =>{
                initStoreOptions({storeCategoryId: form.storeCategoryId! ,storeSubCategoryId: e as string})
              }" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-form-item show-colon label="店铺名称" field="storeId">
        <a-select v-model="form.storeId" :options="storeOptions" :placeholder="`${$selectPlaceholder}店铺名称`" allow-search allow-clear />
      </a-form-item>
      <a-form-item show-colon label="公告标题" field="title" :rules="[{ required: true, message: `${$inputPlaceholder}公告标题` }]">
        <a-input v-model="form.title" :placeholder="`${$inputPlaceholder}公告标题`" :max-length="50" show-word-limit />
      </a-form-item>
      <a-form-item show-colon label="公告内容" field="content" :rules="[{ required: true, message: `${$inputPlaceholder}公告内容` }]">
        <rich-text-editor v-model="form.content" :placeholder="`${$inputPlaceholder}公告内容`" />
      </a-form-item>
    </a-form>
  </div>
</template>

<style lang="scss" scoped></style>

<script lang="ts" setup>
import useHooks from '../hooks'

const props = defineProps<{
  id: string
}>()

const { form, detail } = useHooks()

onMounted(() => {
  detail(props.id)
})
</script>

<template>
  <div class="max-h-[700px] overflow-y-scroll no-scrollbar">
    <a-descriptions bordered size="large" :column="2">
      <a-descriptions-item label="公告标题">{{ form.title }}</a-descriptions-item>
      <a-descriptions-item label="店铺分类">{{ form.storeCategoryName }} - {{ form.storeSubCategoryName }}</a-descriptions-item>
      <a-descriptions-item label="店铺名称">{{ form.storeName }}</a-descriptions-item>
      <a-descriptions-item label="创建人">{{ form.createBy }}</a-descriptions-item>
      <a-descriptions-item label="创建时间" :span="2">{{ form.createTime }}</a-descriptions-item>
      <a-descriptions-item label="公告内容" :span="2">
        <div v-html="form.content"></div>
      </a-descriptions-item>
    </a-descriptions>
  </div>
</template>

<style lang="scss" scoped></style>

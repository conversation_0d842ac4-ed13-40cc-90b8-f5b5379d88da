<script lang="ts" setup>
import { useTemplateRef } from 'vue'
import useCommon from '@/hooks/useCommon'
import useHooks from './hooks'
import { Modal, Notification, TableData } from '@arco-design/web-vue'

const InfoCom = defineAsyncComponent(() => import('./components/info.vue'))
const DetailCom = defineAsyncComponent(() => import('./components/detail.vue'))

const { announcementStateOptions, storeOptions, initStoreOptions } = useCommon()
const { loading, queryParams, pagination, rows, selectedId, selectedIds, selectAll, rowSelect, rowClick, query, reset, pageChange, pageSizeChange, add, edit, del } = useHooks()
const addRef = useTemplateRef<InstanceType<typeof InfoCom>>('addRef')
const showAdd = ref<boolean>(false)
const submitAdd = async (done: (closed: boolean) => void): Promise<void | boolean> => {
  try {
    const errors = await addRef.value?.formRef?.validate()
    if (!!errors) throw new Error('校验失败')
    await add(toRaw(addRef.value!.form))
    Notification.success({
      title: '成功提示',
      content: `已添加公告`,
      duration: 1500
    })
    done(true)
    query()
  } catch (error) {
    done(false)
  }
}
const editRef = useTemplateRef<InstanceType<typeof InfoCom>>('editRef')
const showEdit = ref<boolean>(false)
const submitEdit = async (done: (closed: boolean) => void): Promise<void | boolean> => {
  try {
    const errors = await editRef.value?.formRef?.validate()
    if (!!errors) throw new Error('校验失败')
    await edit(selectedId.value!, toRaw(editRef.value!.form))
    Notification.success({
      title: '成功提示',
      content: `已修改公告`,
      duration: 1500
    })
    done(true)
    query()
  } catch (error) {
    done(false)
  }
}
const showDetail = ref<boolean>(false)
const handleDel = (record: TableData) => {
  Modal.warning({
    title: '提示',
    content: () => h('div', { class: 'text-center' }, `确定删除【${record.title}】？`),
    maskClosable: false,
    escToClose: false,
    hideCancel: false,
    cancelButtonProps: { type: 'outline' },
    onBeforeOk: async (done: (closed: boolean) => void): Promise<void | boolean> => {
      try {
        await del(record.id)
        Notification.success({
          title: '成功提示',
          content: `已删除公告`,
          duration: 1500
        })
        done(true)
        query()
      } catch (error) {
        done(false)
      }
    }
  })
}
onMounted(() => {
  initStoreOptions()
  pageChange(1)
})
</script>

<template>
  <div class="page-container">
    <a-modal
      v-model:visible="showAdd"
      :width="1000"
      title-align="start"
      :title="`添加公告`"
      :cancel-button-props="{ type: 'outline' }"
      unmount-on-close
      :mask-closable="false"
      :esc-to-close="false"
      :on-before-ok="submitAdd"
      @cancel="showAdd = false">
      <info-com ref="addRef"></info-com>
    </a-modal>
    <a-modal
      v-model:visible="showEdit"
      :width="1000"
      title-align="start"
      :title="`修改公告`"
      :cancel-button-props="{ type: 'outline' }"
      unmount-on-close
      :mask-closable="false"
      :esc-to-close="false"
      :on-before-ok="submitEdit"
      @cancel="showEdit = false">
      <info-com ref="editRef" :id="selectedId"></info-com>
    </a-modal>
    <a-modal
      v-model:visible="showDetail"
      :width="1000"
      title-align="start"
      :title="`公告详情`"
      :cancel-button-props="{ type: 'outline' }"
      unmount-on-close
      :mask-closable="false"
      :esc-to-close="false"
      @cancel="showDetail = false"
      :footer="false">
      <detail-com :id="selectedId!"></detail-com>
    </a-modal>
    <div class="h-full flex flex-col gap-[18px]">
      <a-card :bordered="false">
        <a-form :model="queryParams" auto-label-width>
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item show-colon label="公告标题" field="title">
                <a-input v-model="queryParams.title" :placeholder="`${$inputPlaceholder}公告标题`" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="公告状态" field="state">
                <a-select v-model="queryParams.state" :options="announcementStateOptions" :placeholder="`${$selectPlaceholder}公告状态`" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="店铺名称" field="storeId">
                <a-select v-model="queryParams.storeId" :options="storeOptions" :placeholder="`${$selectPlaceholder}店铺名称`" allow-search allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="创建时间" field="createTime">
                <a-range-picker v-model="queryParams.createTime" :placeholder="[`${$selectPlaceholder}开始日期`, `${$selectPlaceholder}结束日期`]" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item hide-label>
                <a-space :size="18">
                  <a-button type="primary" @click="pageChange(1)">
                    <template #icon>
                      <icon-search />
                    </template>
                    查询
                  </a-button>
                  <a-button type="outline" @click="reset">
                    <template #icon>
                      <icon-refresh />
                    </template>
                    重置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <a-card :bordered="false" class="flex-1 overflow-y-hidden" :body-style="{ height: '100%' }">
        <a-row class="mb-[12px]">
          <a-col :span="16">
            <a-space>
              <a-button
                type="primary"
                @click="
                  () => {
                    selectedId = undefined
                    showAdd = true
                  }
                ">
                <template #icon>
                  <icon-plus />
                </template>
                添加公告
              </a-button>
            </a-space>
          </a-col>
        </a-row>
        <!-- :selected-keys="selectedIds"
          :row-selection="{ type: 'checkbox', showCheckedAll: true, onlyCurrent: true }"
          @select-all="selectAll"
          @select="rowSelect"
          @row-click="rowClick" -->
        <a-table size="large" row-key="id" :loading="loading" :pagination="false" :data="rows" :bordered="{ cell: true }" :scroll="{ y: 'calc(100% - 96px)' }">
          <template #columns>
            <a-table-column align="center" title="序号" :width="80">
              <template #cell="{ rowIndex }">
                {{ pagination.pageSize! * (pagination.current! - 1) + rowIndex + 1 }}
              </template>
            </a-table-column>
            <a-table-column align="center" title="公告标题" :width="200" data-index="title" />
            <!-- <a-table-column align="center" title="公告内容" :width="200" ellipsis tooltip data-index="content" /> -->
            <a-table-column align="center" title="店铺名称" :width="200" ellipsis tooltip>
              <template #cell="{ record }">{{ record.storeCategoryName }} - {{ record.storeSubCategoryName }} - {{ record.storeName }}</template>
            </a-table-column>
            <a-table-column align="center" title="状态" :width="200">
              <template #cell="{ record }">
                <a-tag :color="announcementStateOptions.find((item) => item.value === record.state)?.color ?? ''">{{ announcementStateOptions.find((item) => item.value === record.state)?.label ?? '' }}</a-tag>
              </template>
            </a-table-column>
            <a-table-column align="center" title="创建人" :width="150" ellipsis tooltip data-index="createBy" />
            <a-table-column align="center" title="创建时间" :width="180" data-index="createTime" />
            <a-table-column align="center" title="操作" :width="200" fixed="right">
              <template #cell="{ record }">
                <a-space>
                  <template #split>
                    <a-divider direction="vertical" />
                  </template>
                  <a-link
                    @click.stop="
                      () => {
                        selectedId = record.id
                        showDetail = true
                      }
                    ">
                    查看
                  </a-link>
                  <!-- <a-link
                    @click.stop="
                      () => {
                        selectedId = record.id
                        showEdit = true
                      }
                    ">
                    编辑
                  </a-link> -->
                  <a-link
                    status="danger"
                    @click.stop="
                      () => {
                        handleDel(record)
                      }
                    ">
                    删除
                  </a-link>
                </a-space>
              </template>
            </a-table-column>
          </template>
        </a-table>
        <template #actions>
          <a-pagination
            v-if="!!pagination.total"
            :current="pagination.current"
            :page-size="pagination.pageSize"
            :show-total="pagination.showTotal"
            :show-page-size="pagination.showPageSize"
            :page-size-options="pagination.pageSizeOptions"
            :total="pagination.total!"
            @change="pageChange"
            @page-size-change="pageSizeChange" />
        </template>
      </a-card>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>

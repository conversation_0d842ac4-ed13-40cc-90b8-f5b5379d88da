<script lang="ts" setup>
import useCommon from '@/hooks/useCommon'
import useHooks from '../hooks'
import { FormInstance } from '@arco-design/web-vue'
import { useTemplateRef } from 'vue'

const props = defineProps<{
  id?: string
}>()
const { roleOptions, initRoleOptions } = useCommon()
const { detail, form } = useHooks()

onMounted(() => {
  initRoleOptions()
  if (!!props.id) detail(props.id)
})

const formRef = useTemplateRef<FormInstance>('formRef')
defineExpose({
  formRef,
  form
})
</script>

<template>
  <div class="overflow-y-scroll no-scrollbar">
    <a-form ref="formRef" :model="form" auto-label-width>
      <!--禁用自动填充密码-->
      <input type="text" class="fake-input" />
      <input type="password" class="fake-input" />
      <a-form-item show-colon label="所属角色" field="roleId" :rules="[{ required: true, message: `${$selectPlaceholder}所属角色` }]">
        <a-select v-model="form.roleId" :options="roleOptions" :placeholder="`${$selectPlaceholder}所属角色`" />
      </a-form-item>
      <a-form-item show-colon label="用户名称" field="nickname" :rules="[{ required: true, message: `${$inputPlaceholder}用户名称` }]">
        <a-input v-model="form.nickname" :placeholder="`${$inputPlaceholder}用户名称`" />
      </a-form-item>
      <a-form-item show-colon label="登录账号" field="username" :rules="[{ required: true, message: `${$inputPlaceholder}登录账号` }]">
        <a-input v-model="form.username" :placeholder="`${$inputPlaceholder}登录账号`" />
      </a-form-item>
      <a-form-item show-colon v-if="!id" label="登录密码" field="password" :rules="[{ required: true, message: `${$inputPlaceholder}登录密码` }]">
        <a-input-password v-model="form.password" :placeholder="`${$inputPlaceholder}登录密码`" />
      </a-form-item>
      <a-form-item show-colon v-if="!id" label="确认密码" field="confirmPassword" :rules="[{ required: true, message: `${$inputPlaceholder}确认密码` }]">
        <a-input-password v-model="form.confirmPassword" :placeholder="`${$inputPlaceholder}确认密码`" />
      </a-form-item>
      <a-form-item show-colon label="备注" field="remark">
        <a-textarea v-model="form.remark" :auto-size="{ minRows: 5 }" :max-length="50" show-word-limit :placeholder="`${$inputPlaceholder}备注`" />
      </a-form-item>
    </a-form>
  </div>
</template>

<style lang="scss" scoped></style>

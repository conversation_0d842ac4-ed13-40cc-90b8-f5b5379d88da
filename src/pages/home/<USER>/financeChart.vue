<script lang="ts" setup>
import { ref } from 'vue'
import { graphic } from 'echarts'
import useLoading from '@/hooks/useLoading'
import useChartOption from '@/hooks/chartOption'
import { ContentDataRecord } from '@/types/echarts'
import { ToolTipFormatterParams } from '@/types/echarts'
import { AnyObject } from '@/types/global'
import useHooks from '../hooks'
import useCommon from '@/hooks/useCommon'

const { storeOptions, initStoreOptions } = useCommon()
const { storeId, rangeDate, checked, getFinanceChart, emptyChart } = useHooks()
function graphicFactory(side: AnyObject) {
  return {
    type: 'text',
    bottom: '8',
    ...side,
    style: {
      text: '',
      textAlign: 'center',
      fill: '#4E5969',
      fontSize: 12
    }
  }
}
const { loading, setLoading } = useLoading(true)
const xAxis = ref<string[]>([])
const chartsData = ref<number[]>([])
const graphicElements = ref([graphicFactory({ left: '2.6%' }), graphicFactory({ right: 0 })])
const { chartOption } = useChartOption(() => {
  return {
    grid: {
      left: '2.6%',
      right: '0',
      top: '10',
      bottom: '30'
    },
    xAxis: {
      type: 'category',
      offset: 2,
      data: xAxis.value,
      boundaryGap: false,
      axisLabel: {
        color: '#4E5969',
        formatter(value: number, idx: number) {
          if (idx === 0) return ''
          if (idx === xAxis.value.length - 1) return ''
          return `${value}`
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true,
        interval: (idx: number) => {
          if (idx === 0) return false
          if (idx === xAxis.value.length - 1) return false
          return true
        },
        lineStyle: {
          color: '#E5E8EF'
        }
      },
      axisPointer: {
        show: true,
        lineStyle: {
          color: '#23ADFF',
          width: 2
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisLabel: {
        formatter(value: any, idx: number) {
          if (idx === 0) return value
          return `${value}`
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#E5E8EF'
        }
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter(params) {
        const [firstElement] = params as ToolTipFormatterParams[]
        return `<div>
            <p class="tooltip-title">${firstElement.axisValueLabel}</p>
            <div class="content-panel"><span>营收</span><span class="tooltip-value">${Number(firstElement.value).toLocaleString()} 元</span></div>
          </div>`
      },
      className: 'echarts-tooltip-diy'
    },
    graphic: {
      elements: graphicElements.value
    },
    series: [
      {
        data: chartsData.value,
        type: 'line',
        smooth: true,
        // symbol: 'circle',
        symbolSize: 12,
        emphasis: {
          focus: 'series',
          itemStyle: {
            borderWidth: 2
          }
        },
        lineStyle: {
          width: 3,
          color: new graphic.LinearGradient(0, 0, 1, 0, [
            {
              offset: 0,
              color: 'rgba(30, 231, 255, 1)'
            },
            {
              offset: 0.5,
              color: 'rgba(36, 154, 255, 1)'
            },
            {
              offset: 1,
              color: 'rgba(111, 66, 251, 1)'
            }
          ])
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.8,
          color: new graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(17, 126, 255, 0.16)'
            },
            {
              offset: 1,
              color: 'rgba(17, 128, 255, 0)'
            }
          ])
        }
      }
    ]
  }
})
const initChart = (chartData: ContentDataRecord[]) => {
  xAxis.value = []
  chartsData.value = []
  chartData.forEach((el: ContentDataRecord, idx: number) => {
    xAxis.value.push(el.x)
    chartsData.value.push(el.y)
    if (idx === 0) {
      graphicElements.value[0].style.text = el.x
    }
    if (idx === chartData.length - 1) {
      graphicElements.value[1].style.text = el.x
    }
  })
}
const radioChange = async (val: number | string | boolean) => {
  setLoading(true)
  try {
    const chartData = await getFinanceChart(val as number)
    initChart(chartData)
  } finally {
    setLoading(false)
  }
}

const pickerChange = async () => {
  checked.value = -1
  setLoading(true)
  try {
    const chartData = await getFinanceChart()
    initChart(chartData)
  } finally {
    setLoading(false)
  }
}
radioChange(0)

onMounted(() => {
  initStoreOptions()
})
</script>

<template>
  <a-spin :loading="loading">
    <a-card :bordered="false" title="营收趋势图">
      <template #extra>
        <a-space>
          <a-radio-group type="button" v-model="checked" @change="radioChange">
            <a-radio :value="0">近30天</a-radio>
            <a-radio :value="1">近12个月</a-radio>
          </a-radio-group>
          <a-select style="width: 250px" v-model="storeId" :options="storeOptions" :placeholder="`${$selectPlaceholder}店铺名称`" @change="pickerChange" allow-search allow-clear />
          <a-range-picker style="width: 380px" v-model="rangeDate" @change="pickerChange" :allow-clear="false" />
        </a-space>
      </template>
      <transition name="fade" mode="out-in">
        <a-empty v-if="emptyChart" />
        <chart v-else height="289px" :option="chartOption"></chart>
      </transition>
    </a-card>
  </a-spin>
</template>

<style scoped lang="scss">
.unit {
  margin-left: 8px;
  color: rgb(var(--gray-8));
  font-size: 12px;
}
:deep(.arco-empty) {
  height: 289px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: scale(0.8);
}
.fade-enter-active,
.fade-leave-active {
  transition: all 0.2s;
}
</style>

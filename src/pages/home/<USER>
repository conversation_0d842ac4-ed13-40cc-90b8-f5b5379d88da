import { reqGetStatistics, reqGetUser<PERSON>hart, reqGetVisitorChart, reqGetOrderChart, reqGetFinanceChart } from '@/api/apiHome'
import { accDiv } from '@/utils'
import useDate from '@/hooks/useDate'
import { useDateFormat } from '@vueuse/core'
import { ContentDataRecord } from '@/types/echarts'
import { IStatistic, IChartOption } from './types'
import dayjs from 'dayjs'

const { lastWeekStartDate, lastWeekEndDate, weekStartDate, weekEndDate, lastMonthStartDate, lastMonthEndDate, monthStartDate, monthEndDate } = useDate()

const useHooks = () => {
  const statisticList = ref<IStatistic[]>([])
  const getStatistics = async () => {
    try {
      const res = await reqGetStatistics()
      statisticList.value = res.data as IStatistic[]
    } catch (error) {}
  }

  const storeId = ref<string | undefined>(undefined)
  const rangeDate = ref<string[]>([useDateFormat(lastWeekStartDate, 'YYYY-MM-DD').value, useDateFormat(lastWeekEndDate, 'YYYY-MM-DD').value])
  const checked = ref<number>(0)
  const emptyChart = ref<boolean>(true)

  const getOrderChart = (): Promise<
    {
      name: string
      value1: number
      value2: number
    }[]
  > => {
    return new Promise(async (resolve, reject) => {
      try {
        const res = await reqGetOrderChart()
        emptyChart.value = false
        resolve(res.data)
      } catch (error) {
        reject()
      }
    })
  }
  const getUserChart = (): Promise<{
    totalUserCount: number
    onlineUserCount: number
    offlineUserCount: number
  }> => {
    return new Promise(async (resolve, reject) => {
      try {
        const res = await reqGetUserChart()
        emptyChart.value = false
        resolve({
          totalUserCount: res.data.totalUserCount,
          onlineUserCount: res.data.onlineUserCount,
          offlineUserCount: res.data.totalUserCount - res.data.onlineUserCount
        })
      } catch (error) {
        reject()
      }
    })
  }
  const getFinanceChart = (val?: number): Promise<ContentDataRecord[]> => {
    switch (val) {
      case 0:
        // 近30天
        rangeDate.value[0] = dayjs().subtract(30, 'day').format('YYYY-MM-DD')
        rangeDate.value[1] = dayjs().format('YYYY-MM-DD')
        break
      case 1:
        // 近12个月
        rangeDate.value[0] = dayjs().subtract(12, 'month').format('YYYY-MM-DD')
        rangeDate.value[1] = dayjs().format('YYYY-MM-DD')
        break
      default:
        break
    }
    return new Promise(async (resolve, reject) => {
      try {
        const data: { x: string; y: number }[] = []
        const res = await reqGetFinanceChart({ storeId: storeId.value, startDate: rangeDate.value[0], endDate: rangeDate.value[1] })
        const resData = res.data as IChartOption[]
        resData!.forEach((item) => {
          data.push({
            x: item.label,
            y: item.value
          })
        })

        emptyChart.value = !data.length
        resolve(data)
      } catch (error) {
        reject()
      }
    })
  }
  const getVisitorChart = (val?: number): Promise<ContentDataRecord[]> => {
    switch (val) {
      case 0:
        // 上周
        rangeDate.value[0] = useDateFormat(lastWeekStartDate, 'YYYY-MM-DD').value
        rangeDate.value[1] = useDateFormat(lastWeekEndDate, 'YYYY-MM-DD').value
        break
      case 1:
        // 本周
        rangeDate.value[0] = useDateFormat(weekStartDate, 'YYYY-MM-DD').value
        rangeDate.value[1] = useDateFormat(weekEndDate, 'YYYY-MM-DD').value
        break
      case 2:
        // 上月
        rangeDate.value[0] = useDateFormat(lastMonthStartDate, 'YYYY-MM-DD').value
        rangeDate.value[1] = useDateFormat(lastMonthEndDate, 'YYYY-MM-DD').value
        break
      case 3:
        // 本月
        rangeDate.value[0] = useDateFormat(monthStartDate, 'YYYY-MM-DD').value
        rangeDate.value[1] = useDateFormat(monthEndDate, 'YYYY-MM-DD').value
        break
      default:
        break
    }
    return new Promise(async (resolve, reject) => {
      try {
        const data: { x: string; y: number }[] = []
        const res = await reqGetVisitorChart({ startDate: rangeDate.value[0], endDate: rangeDate.value[1] })
        const resData = res.data as IChartOption[]
        resData!.forEach((item) => {
          data.push({
            x: item.label,
            y: item.value
          })
        })

        emptyChart.value = !data.length
        resolve(data)
      } catch (error) {
        reject()
      }
    })
  }
  return {
    statisticList,
    getStatistics,
    storeId,
    rangeDate,
    getOrderChart,
    getUserChart,
    getFinanceChart,
    getVisitorChart,
    checked,
    emptyChart
  }
}

export default useHooks

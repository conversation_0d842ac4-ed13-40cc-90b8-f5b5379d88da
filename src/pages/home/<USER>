<script lang="ts" setup>
import StatisticCom from './components/statistic.vue'
import FastEnterCom from './components/fastEnter.vue'
import UserChartCom from './components/userChart.vue'
import OrderChartCom from './components/orderChart.vue'
import FinanceChartCom from './components/financeChart.vue'
import VisitorChartCom from './components/visitorChart.vue'

import useHooks from './hooks'

const { statisticList, getStatistics } = useHooks()
getStatistics()
</script>

<template>
  <div class="page-container">
    <div class="flex gap-x-[18px]">
      <div class="h-full flex flex-1 flex-col gap-y-[18px]">
        <statistic-com :list="statisticList"></statistic-com>
        <order-chart-com></order-chart-com>
      </div>
      <div class="w-[426px] h-full flex flex-col gap-y-[18px]">
        <fast-enter-com></fast-enter-com>
        <user-chart-com></user-chart-com>
      </div>
    </div>
    <div class="mt-[18px]">
      <finance-chart-com></finance-chart-com>
    </div>
    <div class="mt-[18px]">
      <visitor-chart-com></visitor-chart-com>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>

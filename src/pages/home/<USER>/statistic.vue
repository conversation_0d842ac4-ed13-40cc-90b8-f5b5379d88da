<script lang="ts" setup>
import { IStatistic } from '../types'
import { useUserStore } from '@/store'

defineProps<{
  list: IStatistic[]
}>()
const userStore = useUserStore()

const nickName = computed(() => userStore.getNickName)
</script>

<template>
  <a-card :bordered="false">
    <template #title>
      <a-typography-title :style="{ margin: '6px 0', fontSize: '18px' }" :heading="5">欢迎回来！{{ nickName }}</a-typography-title>
    </template>
    <a-grid :cols="5" :row-gap="16" class="panel">
      <a-grid-item class="panel-col" :span="{ xs: 5, sm: 5, lg: 1 }" v-for="(item, index) in list" :key="index">
        <a-statistic :title="item.label" :value="item.value" :value-from="0" animation show-group-separator>
          <template #suffix>
            <span class="unit">{{ item.unit }}</span>
          </template>
        </a-statistic>
      </a-grid-item>
    </a-grid>
  </a-card>
</template>

<style lang="scss" scoped>
.arco-grid.panel {
  margin-bottom: 0;
  padding: 16px 20px 0 20px;
}
.panel-col {
  @apply flex justify-center;
  &:not(:last-child) {
    border-right: 1px solid rgb(var(--gray-2));
  }
}
.col-avatar {
  margin-right: 12px;
  background-color: var(--color-fill-2);
}
.up-icon {
  color: rgb(var(--red-6));
}
.unit {
  margin-left: 8px;
  color: rgb(var(--gray-8));
  font-size: 16px;
}
:deep(.panel-border) {
  margin: 4px 0 0 0;
}
:deep(.arco-statistic-content) {
  .arco-statistic-value {
    text-align: center;
  }
}
</style>

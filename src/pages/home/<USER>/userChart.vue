<script lang="ts" setup>
import useChartOption from '@/hooks/chartOption'
import useHooks from '../hooks'
import useLoading from '@/hooks/useLoading'

const { getUserChart, emptyChart } = useHooks()

const { loading, setLoading } = useLoading(true)
const totalUserCount = ref<number>(0)
const onlineUserCount = ref<number>(0)
const offlineUserCount = ref<number>(0)
const { chartOption } = useChartOption(() => {
  return {
    legend: {
      left: 'center',
      data: ['活跃用户数', '非活跃用户数'],
      bottom: 0,
      icon: 'circle',
      itemWidth: 8,
      textStyle: {
        color: '#4E5969'
      },
      itemStyle: {
        borderWidth: 0
      }
    },
    tooltip: {
      show: true,
      trigger: 'item'
    },
    graphic: {
      elements: [
        {
          type: 'text',
          left: 'center',
          top: '45%',
          style: {
            text: '用户总数',
            textAlign: 'center',
            fill: '#4E5969',
            fontSize: 14
          }
        },
        {
          type: 'text',
          left: 'center',
          top: '55%',
          style: {
            text: `${totalUserCount.value}`,
            textAlign: 'center',
            fill: '#1D2129',
            fontSize: 18,
            fontWeight: 500
          }
        }
      ]
    },
    series: [
      {
        type: 'pie',
        radius: ['50%', '70%'],
        center: ['50%', '50%'],
        label: {
          formatter: '{d}%',
          fontSize: 14,
          color: '#4E5969'
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        data: [
          {
            value: [offlineUserCount.value],
            name: '非活跃用户数',
            itemStyle: {
              color: '#21CCFF'
            }
          },
          {
            value: [onlineUserCount.value],
            name: '活跃用户数',
            itemStyle: {
              color: '#249EFF'
            }
          }
        ]
      }
    ]
  }
})
const initChart = (chartData: { totalUserCount: number; onlineUserCount: number; offlineUserCount: number }) => {
  totalUserCount.value = chartData.totalUserCount
  onlineUserCount.value = chartData.onlineUserCount
  offlineUserCount.value = chartData.offlineUserCount
}
const getData = async () => {
  setLoading(true)
  try {
    const chartData = await getUserChart()
    initChart(chartData)
  } finally {
    setLoading(false)
  }
}
getData()
</script>

<template>
  <a-card :bordered="false" title="用户统计">
    <transition name="fade" mode="out-in">
      <a-empty v-if="emptyChart" />
      <Chart v-else height="310px" :option="chartOption" />
    </transition>
  </a-card>
</template>

<style lang="scss" scoped>
:deep(.arco-empty) {
  height: 289px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: scale(0.8);
}
.fade-enter-active,
.fade-leave-active {
  transition: all 0.2s;
}
</style>

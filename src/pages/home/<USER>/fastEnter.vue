<script lang="ts" setup>
import { useRouter } from 'vue-router'

const router = useRouter()
const links = [
  { title: '店铺审核', icon: 'icon-building', name: 'StoreApprove' },
  { title: '商品审核', icon: 'gift', name: 'CommodityApprove' },
  { title: '订单管理', icon: 'bookmark', name: 'Orders' },
  { title: '公告发布', icon: 'folder', name: 'Announcements' }
]
</script>

<template>
  <a-card :bordered="false">
    <template #title>
      <a-typography-title :style="{ margin: '6px 0', fontSize: '18px' }" :heading="5">快捷入口</a-typography-title>
    </template>
    <a-row :gutter="16">
      <a-col
        v-for="(link, index) in links"
        :key="index"
        :span="6"
        class="item"
        @click="
          () => {
            router.push({ name: link.name })
          }
        ">
        <div class="icon">
          <!-- <component :is="link.icon" :size="20"></component> -->
          <icon :icon="link.icon"></icon>
        </div>
        <p class="text">{{ link.title }}</p>
      </a-col>
    </a-row>
  </a-card>
</template>

<style lang="scss" scoped>
.item {
  @apply cursor-pointer;
  .icon {
    @apply w-[42px] h-[42px] bg-[var(--color-fill-2)] rounded-md flex justify-center items-center mx-auto;
  }
  .text {
    @apply mt-[8px] text-center text-xs;
  }
  &:hover {
    .icon {
      @apply bg-[var(--color-fill-3)];
    }
    .text {
      @apply font-bold text-[rgb(var(--primary-6))];
    }
  }
}
</style>

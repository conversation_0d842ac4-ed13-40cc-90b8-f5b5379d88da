<script lang="ts" setup>
import { ref } from 'vue'
import { graphic } from 'echarts'
import useLoading from '@/hooks/useLoading'
import useChartOption from '@/hooks/chartOption'
import { ContentDataRecord } from '@/types/echarts'
import { ToolTipFormatterParams } from '@/types/echarts'
import { AnyObject } from '@/types/global'
import useHooks from '../hooks'

const tooltipItemsHtmlString = (items: ToolTipFormatterParams[]) => {
  return items
    .map(
      (el) =>
        `
          <div class="content-panel">
            <p>
              <span style="background-color: ${el.color}" class="tooltip-item-icon"></span>
              <span>
                ${el.seriesIndex === 0 ? '快递发货' : '线下核销'}
              </span>
            </p>
            <span class="tooltip-value">
              ${Number(el.value).toLocaleString()} 单
            </span>
          </div>
        `
    )
    .join('')
}

const { getOrderChart, emptyChart } = useHooks()
const { loading, setLoading } = useLoading(false)
const xAxis = ref<string[]>([])
const orderChartsData1 = ref<number[]>([])
const orderChartsData2 = ref<number[]>([])
const { chartOption } = useChartOption((isDark) => {
  return {
    grid: {
      left: '4%',
      right: 0,
      top: '20',
      bottom: '20'
    },
    // legend: {
    //   bottom: 0,
    //   icon: 'circle',
    //   textStyle: {
    //     color: '#4E5969'
    //   }
    // },
    xAxis: {
      type: 'category',
      data: xAxis.value,
      axisLine: {
        lineStyle: {
          color: isDark ? '#3f3f3f' : '#A9AEB8'
        }
      },
      axisTick: {
        show: true,
        alignWithLabel: true,
        lineStyle: {
          color: '#86909C'
        }
      },
      axisLabel: {
        color: '#86909C'
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#86909C',
        formatter(value: number, idx: number) {
          // if (idx === 0) return `${value} 单`
          return `${value} 单`
        }
      },
      splitLine: {
        lineStyle: {
          color: isDark ? '#3F3F3F' : '#E5E6EB'
        }
      }
    },
    tooltip: {
      show: true,
      trigger: 'axis',
      formatter(params) {
        const [firstElement] = params as ToolTipFormatterParams[]
        return `<div>
            <p class="tooltip-title">${firstElement.axisValueLabel}</p>
            ${tooltipItemsHtmlString(params as ToolTipFormatterParams[])}
          </div>`
      },
      className: 'echarts-tooltip-diy'
    },
    series: [
      {
        name: '7日订单统计',
        data: orderChartsData1.value,
        // stack: 'one',
        type: 'bar',
        barWidth: 32,
        color: isDark ? '#085FEF' : '#00B2FF'
      },
      {
        name: '7日订单统计',
        data: orderChartsData2.value,
        // stack: 'one',
        type: 'bar',
        barWidth: 32,
        color: isDark ? '#00B2FF' : '#085FEF'
      }
    ]
  }
})
const initChart = (data: { name: string; value1: number; value2: number }[]) => {
  xAxis.value = data.map((el) => el.name)
  orderChartsData1.value = data.map((el) => el.value1)
  orderChartsData2.value = data.map((el) => el.value2)
}
const getData = async () => {
  setLoading(true)
  try {
    const res = await getOrderChart()
    initChart(res)
  } finally {
    setLoading(false)
  }
}
getData()
</script>

<template>
  <a-spin :loading="loading">
    <a-card :bordered="false" title="7日订单统计">
      <transition name="fade" mode="out-in">
        <a-empty v-if="emptyChart" />
        <chart v-else height="289px" :option="chartOption"></chart>
      </transition>
    </a-card>
  </a-spin>
</template>

<style scoped lang="scss">
.unit {
  margin-left: 8px;
  color: rgb(var(--gray-8));
  font-size: 12px;
}
:deep(.arco-empty) {
  height: 289px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: scale(0.8);
}
.fade-enter-active,
.fade-leave-active {
  transition: all 0.2s;
}
</style>

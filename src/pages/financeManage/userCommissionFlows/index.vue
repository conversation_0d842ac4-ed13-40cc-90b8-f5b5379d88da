<script lang="ts" setup>
import { formatNumber } from '@/utils'
import useHooks from './hooks'
import { useRoute } from 'vue-router'
import { Modal, Notification, TableData } from '@arco-design/web-vue'

const route = useRoute()
const { loading, queryParams, pagination, rows, selectedId, selectedRow, selectedIds, selectAll, rowSelect, rowClick, query, reset, pageChange, pageSizeChange, approvePayout, payout } = useHooks(
  route.name === 'UserCommissionFlows' ? 1 : -1
)
const showApprove = ref<boolean>(false)
const passLoading = ref<boolean>(false)
const submitPass = async () => {
  try {
    passLoading.value = true
    await approvePayout(selectedId.value!, { approveState: 1, approveReason: '人工审核通过' })
    passLoading.value = false
    showApprove.value = false
    Notification.success({
      title: '成功提示',
      content: `已审核通过提现申请`,
      duration: 1500
    })
    query()
  } catch (error) {
    passLoading.value = false
  }
}
const showRefuse = ref<boolean>(false)
const approveReason = ref<string | undefined>(undefined)
const submitRefuse = async (done: (closed: boolean) => void): Promise<void | boolean> => {
  try {
    if (!approveReason.value) {
      Notification.warning({
        title: '提示',
        content: `请填写拒绝理由`,
        duration: 1500
      })
      throw new Error('校验失败')
    }
    await approvePayout(selectedId.value!, { approveState: -1, approveReason: approveReason.value })
    Notification.success({
      title: '成功提示',
      content: `已审核拒绝提现申请`,
      duration: 1500
    })
    showApprove.value = false
    done(true)
    query()
  } catch (error) {
    done(false)
  }
}
const handlePayout = (id: string) => {
  Modal.confirm({
    title: '提示',
    content: () => h('div', { class: 'text-center' }, `该操作将修改打款状态，确定已线下打款？`),
    maskClosable: false,
    escToClose: false,
    hideCancel: false,
    cancelButtonProps: { type: 'outline' },
    onBeforeOk: async (done: (closed: boolean) => void): Promise<void | boolean> => {
      try {
        await payout(id)
        Notification.success({
          title: '成功提示',
          content: `已打款`,
          duration: 1500
        })
        done(true)
        query()
      } catch (error) {
        done(false)
      }
    }
  })
}
onMounted(() => {
  pageChange(1)
})
</script>

<template>
  <div class="page-container">
    <a-modal
      :visible="showApprove"
      title-align="start"
      :title="`审核详情`"
      :cancel-button-props="{ type: 'outline' }"
      cancel-text="拒绝"
      ok-text="通过"
      unmount-on-close
      :mask-closable="false"
      :esc-to-close="false"
      @cancel="showApprove = false"
      body-style="background-color: var(--color-fill-2)">
      <template #footer>
        <a-space>
          <a-button
            type="outline"
            @click="
              () => {
                approveReason = undefined
                showRefuse = true
              }
            ">
            审核拒绝
          </a-button>
          <a-button type="primary" :loading="passLoading" @click="submitPass">审核通过</a-button>
        </a-space>
      </template>
      <a-card :bordered="false">
        <a-descriptions bordered size="large" :column="1">
          <a-descriptions-item label="提现用户">{{ selectedRow?.user?.nickname }}</a-descriptions-item>
          <a-descriptions-item label="提现金额">{{ selectedRow?.amount.toFixed(2) }} 元</a-descriptions-item>
          <a-descriptions-item label="银行名称">{{ selectedRow?.payoutBankInfo?.bankName ?? '-' }}</a-descriptions-item>
          <a-descriptions-item label="银行账号">{{ selectedRow?.payoutBankInfo?.bankAccount ?? '-' }}</a-descriptions-item>
          <a-descriptions-item label="开户人姓名">{{ selectedRow?.payoutBankInfo?.contactName ?? '-' }}</a-descriptions-item>
          <a-descriptions-item label="开户人身份证号">{{ selectedRow?.payoutBankInfo?.contactIdentityCard ?? '-' }}</a-descriptions-item>
          <a-descriptions-item label="开户人手机号">{{ selectedRow?.payoutBankInfo?.contactNumber ?? '-' }}</a-descriptions-item>
        </a-descriptions>
      </a-card>
    </a-modal>
    <a-modal
      v-model:visible="showRefuse"
      title-align="start"
      :title="`拒绝理由`"
      :cancel-button-props="{ type: 'outline' }"
      ok-text="提交"
      unmount-on-close
      :mask-closable="false"
      :esc-to-close="false"
      :on-before-ok="submitRefuse">
      <a-input v-model="approveReason" :placeholder="`${$inputPlaceholder}拒绝理由`" allow-clear />
    </a-modal>
    <div class="h-full flex flex-col gap-[18px]">
      <a-card :bordered="false">
        <a-form :model="queryParams" auto-label-width>
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item show-colon label="用户昵称" field="nickname">
                <a-input v-model="queryParams.nickname" :placeholder="`${$inputPlaceholder}用户昵称`" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="日期" field="createTime">
                <a-range-picker v-model="queryParams.createTime" :placeholder="[`${$selectPlaceholder}开始日期`, `${$selectPlaceholder}结束日期`]" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item hide-label>
                <a-space :size="18">
                  <a-button type="primary" @click="pageChange(1)">
                    <template #icon>
                      <icon-search />
                    </template>
                    查询
                  </a-button>
                  <a-button type="outline" @click="reset">
                    <template #icon>
                      <icon-refresh />
                    </template>
                    重置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <a-card :bordered="false" class="flex-1 overflow-y-hidden" :body-style="{ height: '100%' }">
        <!-- :selected-keys="selectedIds"
          :row-selection="{ type: 'checkbox', showCheckedAll: true, onlyCurrent: true }"
          @select-all="selectAll"
          @select="rowSelect"
          @row-click="rowClick" -->
        <a-table size="large" row-key="id" :loading="loading" :pagination="false" :data="rows" :bordered="{ cell: true }" :scroll="{ y: 'calc(100% - 52px)' }">
          <template #columns>
            <a-table-column align="center" title="序号" :width="80">
              <template #cell="{ rowIndex }">
                {{ rowIndex + 1 }}
              </template>
            </a-table-column>
            <a-table-column align="center" title="类型" :width="150">
              <template #cell="{ record }">
                <a-tag :color="record.type === 1 ? 'green' : 'orange'">{{ record.type === 1 ? '推广订单收入' : '提现' }}</a-tag>
              </template>
            </a-table-column>
            <a-table-column align="center" title="用户信息" :width="350" ellipsis tooltip>
              <template #cell="{ record }">
                <a-space>
                  <a-avatar :size="32">
                    <img :src="record.user?.avatar" />
                  </a-avatar>
                  <div class="text-left">
                    <p>用户编号：{{ record.user?.userNo }}</p>
                    <p class="pt-1">用户昵称：{{ record.user?.nickname }}</p>
                    <p class="pt-1">用户手机：{{ formatNumber(record.user?.mobile) }}</p>
                  </div>
                </a-space>
              </template>
            </a-table-column>
            <a-table-column align="center" title="关联订单" :width="200">
              <template #cell="{ record }">
                {{ record.order?.orderNo ?? '-' }}
              </template>
            </a-table-column>
            <a-table-column align="center" title="金额" :width="150">
              <template #cell="{ record }">{{ record.amount }} 元</template>
            </a-table-column>
            <a-table-column align="center" title="审核状态" :width="150">
              <template #cell="{ record }">
                {{ record.approveState === 0 ? '待审核' : record.approveState === 1 ? '审核通过' : '审核拒绝' }}
              </template>
            </a-table-column>
            <a-table-column align="center" title="审核原因" :width="200" ellipsis tooltip data-index="approveReason" />
            <a-table-column align="center" title="审核时间" :width="180" data-index="approveTime" />
            <template v-if="route.name === 'UserCommissionPayouts'">
              <a-table-column align="center" title="打款状态" :width="150">
                <template #cell="{ record }">
                  {{ record.payoutState === -1 ? '未打款' : record.payoutState === 1 ? '已打款' : '-' }}
                </template>
              </a-table-column>
              <a-table-column align="center" title="打款时间" :width="200" ellipsis tooltip data-index="payoutTime" />
            </template>
            <a-table-column align="center" title="创建时间" :width="180" data-index="createTime" />
            <a-table-column v-if="route.name === 'UserCommissionPayouts'" align="center" title="操作" :width="200" fixed="right">
              <template #cell="{ record }">
                <a-space>
                  <template #split>
                    <a-divider direction="vertical" />
                  </template>
                  <a-link
                    v-if="record.approveState === 0"
                    @click.stop="
                      () => {
                        selectedId = record.id
                        showApprove = true
                      }
                    ">
                    审核
                  </a-link>
                  <a-link v-if="record.approveState === 1 && record.payoutState === -1" @click.stop="handlePayout(record.id)">打款</a-link>
                </a-space>
              </template>
            </a-table-column>
          </template>
        </a-table>
        <template #actions>
          <a-pagination
            v-if="!!pagination.total"
            :current="pagination.current"
            :page-size="pagination.pageSize"
            :show-total="pagination.showTotal"
            :show-page-size="pagination.showPageSize"
            :page-size-options="pagination.pageSizeOptions"
            :total="pagination.total!"
            @change="pageChange"
            @page-size-change="pageSizeChange" />
        </template>
      </a-card>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>

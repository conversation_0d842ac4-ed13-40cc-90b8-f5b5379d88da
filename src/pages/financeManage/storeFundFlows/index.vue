<script lang="ts" setup>
import { Modal, Notification, TableData } from '@arco-design/web-vue'
import useCommon from '@/hooks/useCommon'
import useHooks from './hooks'

const { storeOptions, initStoreOptions } = useCommon()
const { loading, queryParams, pagination, rows, selectedId, selectedIds, selectAll, rowSelect, rowClick, query, reset, pageChange, pageSizeChange, exports } = useHooks()

const DetailCom = defineAsyncComponent(() => import('./components/detail.vue'))
const showDetail = ref<boolean>(false)
const handleExport = () => {
  try {
    Modal.warning({
      title: '提示',
      content: () => h('div', { class: 'text-center' }, `确定导出所有流水信息？`),
      maskClosable: false,
      escToClose: false,
      hideCancel: false,
      cancelButtonProps: { type: 'outline' },
      onBeforeOk: async (done: (closed: boolean) => void): Promise<void | boolean> => {
        try {
          await exports()
          Notification.success({
            title: '操作提示',
            content: `已导出所有流水信息`,
            duration: 1500
          })
          done(true)
        } catch (error) {
          done(false)
        }
      }
    })
  } catch (error) {}
}
onMounted(() => {
  initStoreOptions()
  pageChange(1)
})
</script>

<template>
  <div class="page-container">
    <a-modal
      v-model:visible="showDetail"
      :width="1000"
      title-align="start"
      :title="`流水详情`"
      :cancel-button-props="{ type: 'outline' }"
      unmount-on-close
      :mask-closable="false"
      :esc-to-close="false"
      @cancel="showDetail = false"
      :footer="false"
      body-style="background-color: var(--color-fill-2)">
      <detail-com :id="selectedId!"></detail-com>
    </a-modal>

    <div class="h-full flex flex-col gap-[18px]">
      <a-card :bordered="false">
        <a-form :model="queryParams" auto-label-width>
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item show-colon label="店铺名称" field="storeId">
                <a-select v-model="queryParams.storeId" :options="storeOptions" :placeholder="`${$selectPlaceholder}店铺名称`" allow-search allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item show-colon label="日期" field="createTime">
                <a-range-picker v-model="queryParams.createTime" :placeholder="[`${$selectPlaceholder}开始日期`, `${$selectPlaceholder}结束日期`]" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item hide-label>
                <a-space :size="18">
                  <a-button type="primary" @click="pageChange(1)">
                    <template #icon>
                      <icon-search />
                    </template>
                    查询
                  </a-button>
                  <a-button type="outline" @click="reset">
                    <template #icon>
                      <icon-refresh />
                    </template>
                    重置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <a-card :bordered="false" class="flex-1 overflow-y-hidden" :body-style="{ height: '100%' }">
        <a-row class="mb-[12px]">
          <a-col :span="16">
            <a-space>
              <a-button :disabled="!rows.length" type="primary" @click="handleExport">
                <template #icon>
                  <icon-export />
                </template>
                导出
              </a-button>
            </a-space>
          </a-col>
        </a-row>
        <!-- :selected-keys="selectedIds"
          :row-selection="{ type: 'checkbox', showCheckedAll: true, onlyCurrent: true }"
          @select-all="selectAll"
          @select="rowSelect"
          @row-click="rowClick" -->
        <a-table size="large" row-key="id" :loading="loading" :pagination="false" :data="rows" :bordered="{ cell: true }" :scroll="{ y: 'calc(100% - 96px)' }">
          <template #columns>
            <a-table-column align="center" title="序号" :width="80">
              <template #cell="{ rowIndex }">
                {{ rowIndex + 1 }}
              </template>
            </a-table-column>
            <a-table-column align="center" title="类型" :width="150">
              <template #cell="{ record }">
                <a-tag :color="record.type === 1 ? 'green' : 'orange'">{{ record.type === 1 ? '订单收入' : '向平台分账' }}</a-tag>
              </template>
            </a-table-column>
            <a-table-column align="center" title="店铺" :width="200" ellipsis tooltip>
              <template #cell="{ record }">
                {{ record.store?.name ?? '-' }}
              </template>
            </a-table-column>
            <a-table-column align="center" title="关联订单" :width="200">
              <template #cell="{ record }">
                {{ record.order?.orderNo ?? '-' }}
              </template>
            </a-table-column>
            <a-table-column align="center" title="金额" :width="150">
              <template #cell="{ record }">{{ record.amount }} 元</template>
            </a-table-column>
            <a-table-column align="center" title="状态" :width="150">
              <template #cell="{ record }">
                {{ record.state === 1 ? '处理完成' : '处理中' }}
              </template>
            </a-table-column>
            <a-table-column align="center" title="余额" :width="150">
              <template #cell="{ record }">{{ record.balance }} 元</template>
            </a-table-column>
            <a-table-column align="center" title="可用余额" :width="150">
              <template #cell="{ record }">{{ record.availableBalance }} 元</template>
            </a-table-column>
            <a-table-column align="center" title="创建时间" :width="180" data-index="createTime" />
            <a-table-column align="center" title="操作" :width="100" fixed="right">
              <template #cell="{ record }">
                <a-space>
                  <template #split>
                    <a-divider direction="vertical" />
                  </template>
                  <a-link
                    @click.stop="
                      () => {
                        selectedId = record.id
                        showDetail = true
                      }
                    ">
                    详情
                  </a-link>
                </a-space>
              </template>
            </a-table-column>
          </template>
        </a-table>
        <template #actions>
          <a-pagination
            v-if="!!pagination.total"
            :current="pagination.current"
            :page-size="pagination.pageSize"
            :show-total="pagination.showTotal"
            :show-page-size="pagination.showPageSize"
            :page-size-options="pagination.pageSizeOptions"
            :total="pagination.total!"
            @change="pageChange"
            @page-size-change="pageSizeChange" />
        </template>
      </a-card>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>

import useLoading from '@/hooks/useLoading'
import usePagination from '@/hooks/usePagination'
import { IQueryParams } from './types'
import { reqGetStoreFundFlows, reqGetStoreFundFlowDetail, reqExportStoreFundFlow } from '@/api/apiStoreFundFlow'
import dayjs from 'dayjs'
import { Notification, TableData } from '@arco-design/web-vue'
import { reqExportStore } from '@/api/apiStore'

const useHooks = () => {
  const { loading, setLoading } = useLoading()
  const { pagination } = usePagination()
  const initQueryParams = (): IQueryParams => {
    return {
      storeId: undefined,
      createTime: undefined
    }
  }
  const queryParams = reactive<IQueryParams>(initQueryParams())
  const selectedId = ref<string | undefined>(undefined)
  const selectedRow = computed(() => {
    return !selectedId.value ? null : rows.value.find((item) => item.id === selectedId.value)
  })
  const selectedIds = ref<string[]>([])
  const selectedRows = computed(() => {
    return !selectedIds.value.length ? [] : rows.value.filter((item) => selectedIds.value.includes(item.id))
  })
  // 单选
  // const rowSelect = (rowKeys: (string | number)[], rowKey: string | number, record: TableData): void => {
  //   selectedId.value = selectedId.value === record.id ? 0 : record.id
  // }
  // const rowClick = (record: TableData): void => {
  //   selectedId.value = selectedId.value === record.id ? 0 : record.id
  // }
  // 多选
  const selectAll = (checked: boolean): void => {
    selectedIds.value = checked ? rows.value.map((item) => item.id) : []
  }
  const rowSelect = (rowKeys: (string | number)[], rowKey: string | number, record: TableData): void => {
    selectedIds.value.includes(record.id) ? selectedIds.value.splice(selectedIds.value.indexOf(record.id), 1) : selectedIds.value.push(record.id)
  }
  const rowClick = (record: TableData): void => {
    selectedIds.value.includes(record.id) ? selectedIds.value.splice(selectedIds.value.indexOf(record.id), 1) : selectedIds.value.push(record.id)
  }
  const rows = ref<any[]>([])
  const query = async () => {
    setLoading(true)
    selectedId.value = undefined
    selectedIds.value = []
    try {
      const { data } = await reqGetStoreFundFlows({ ...queryParams, pageNum: pagination.current!, pageSize: pagination.pageSize! })
      rows.value = (data.rows as any[]).map((item) => {
        item.createTime = dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss')
        return item
      })
      pagination.total = data.total as number
      setLoading(false)
    } catch (error) {
      rows.value = []
      pagination.total = 0
      setLoading(false)
    }
  }
  const reset = () => {
    pagination.current = 1
    Object.assign(queryParams, initQueryParams())
    query()
  }
  const pageChange = async (current: number) => {
    pagination.current = current
    query()
  }
  const pageSizeChange = async (pageSize: number) => {
    pagination.current = 1
    pagination.pageSize = pageSize
    query()
  }
  const form = reactive<any>({})
  const detail = async (id: string) => {
    try {
      const { data } = await reqGetStoreFundFlowDetail(id)
      Object.assign(form, data)
    } catch (error) {
      throw error
    }
  }
  const exports = async () => {
    try {
      await reqExportStoreFundFlow()
    } catch (error) {
      throw error
    }
  }
  return {
    loading,
    queryParams,
    pagination,
    rows,
    selectedId,
    selectedRow,
    selectedIds,
    selectedRows,
    selectAll,
    rowSelect,
    rowClick,
    query,
    reset,
    pageChange,
    pageSizeChange,
    detail,
    form,
    exports
  }
}
export default useHooks

<script lang="ts" setup>
import { formatNumber } from '@/utils'
import { FormInstance } from '@arco-design/web-vue'
import useCommon from '@/hooks/useCommon'
import useAddress from '@/hooks/useAddress'
import useHooks from '../hooks'
import { useTemplateRef } from 'vue'
import { useRoute } from 'vue-router'
import dayjs from 'dayjs'

const { orderStateOptions, orderDeliveryTypeOptions, orderPayTypeOptions } = useCommon()

const route = useRoute()
// console.log('route.name :>> ', route.name)
const isPayMentVersion = Number(import.meta.env.VITE_IS_PAYMENT_VERSION)

const props = defineProps<{
  id: string
}>()
const { storeBusinessStateOptions } = useCommon()
const { provinces, cities, setCities, areas, setAreas } = useAddress()
const { form, detail } = useHooks()

onMounted(async () => {
  await detail(props.id)
  setCities(form.provinceCode!)
  setAreas(form.provinceCode!, form.cityCode!)
})
const formRef = useTemplateRef<FormInstance>('formRef')
defineExpose({
  formRef,
  form
})
</script>

<template>
  <div class="h-[700px] overflow-y-scroll no-scrollbar">
    <div class="h-full flex flex-col gap-[18px]">
      <a-card :bordered="false" title="基本信息">
        <a-descriptions bordered size="large" :column="2">
          <a-descriptions-item label="类型">
            <a-tag :color="form.type === 1 ? 'green' : 'orange'">{{ form.type === 1 ? '订单收入' : '向平台分账' }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="金额">{{ form.amount }} 元</a-descriptions-item>
          <a-descriptions-item label="状态">{{ form.state === 1 ? '处理完成' : '处理中' }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ dayjs(form.createTime).format('YYYY-MM-DD HH:mm:ss') }}</a-descriptions-item>
        </a-descriptions>
      </a-card>
      <a-card :bordered="false" title="订单信息">
        <a-descriptions bordered size="large" :column="2">
          <a-descriptions-item label="订单编号">{{ form.order?.orderNo || '-' }}</a-descriptions-item>
          <a-descriptions-item label="下单时间">{{ dayjs(form.order?.createTime).format('YYYY-MM-DD HH:mm:ss') || '-' }}</a-descriptions-item>
          <a-descriptions-item label="商品名称">
            <div v-for="(commodity, index) in form.order?.commodities" :key="index">
              <a-space>
                <a-avatar shape="square" :size="32">
                  <img :src="commodity.cover" />
                </a-avatar>
                <div class="text-left">
                  <p>商品名称：{{ commodity.name }}</p>
                  <p class="pt-1">商品数量：{{ commodity.count }} {{ commodity.unit }}</p>
                </div>
              </a-space>
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="用户信息">
            <a-space>
              <a-avatar :size="32">
                <img :src="form.order?.user.avatar" />
              </a-avatar>
              <div class="text-left">
                <p>用户编号：{{ form.order?.user.no }}</p>
                <p class="pt-1">用户昵称：{{ form.order?.user.nickname }}</p>
                <p class="pt-1">用户手机：{{ formatNumber(form.order?.user.mobile) }}</p>
              </div>
            </a-space>
          </a-descriptions-item>
          <a-descriptions-item label="支付信息">
            <a-space>
              <div class="text-left">
                <p>商品金额：{{ form.order?.commodityAmount.toFixed(2) }} 元</p>
                <p class="pt-1">优惠金额：{{ form.order?.reduceAmount.toFixed(2) }} 元</p>
                <p class="pt-1">付款金额：{{ form.order?.payAmount.toFixed(2) }} 元</p>
                <p class="pt-1">付款方式：{{ orderPayTypeOptions.find((item) => item.value === form.order?.payType)?.label ?? '' }}</p>
                <p class="pt-1">支付状态：{{ form.order?.payState === 0 ? '待付款' : form.order?.payState === -1 ? '支付失败' : form.order?.payState === 1 ? '支付成功' : '' }}</p>
                <p class="pt-1 w-[210px]">付款时间：{{ dayjs(form.order?.payTime).format('YYYY-MM-DD HH:mm:ss') }}</p>
              </div>
            </a-space>
          </a-descriptions-item>
          <a-descriptions-item label="收货信息">
            <a-space>
              <div class="text-left">
                <p>
                  配送方式：
                  <a-tag :color="orderDeliveryTypeOptions.find((item) => item.value === form.order?.deliveryType)?.color ?? ''">
                    {{ orderDeliveryTypeOptions.find((item) => item.value === form.order?.deliveryType)?.label ?? '' }}
                  </a-tag>
                </p>
                <p class="pt-1">收货人：{{ form.order?.deliveryType === 1 ? form.order?.delivery?.userName : '-' }}</p>
                <p class="pt-1">手机号码：{{ form.order?.deliveryType === 1 ? form.order?.delivery?.contactNumber : '-' }}</p>
                <p class="pt-1 w-[200px]">收货地址：{{ form.order?.deliveryType === 1 ? form.order?.delivery?.address : '-' }}</p>
              </div>
            </a-space>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
      <a-card :bordered="false" title="店铺信息">
        <a-descriptions bordered size="large" :column="2">
          <a-descriptions-item label="店铺LOGO" :span="2">
            <upload-card :modelValue="form.store?.logo" disabled></upload-card>
          </a-descriptions-item>
          <a-descriptions-item label="店铺编号">{{ form.store?.storeNo }}</a-descriptions-item>
          <a-descriptions-item label="店铺名称">{{ form.store?.name }}</a-descriptions-item>
          <a-descriptions-item label="联系人姓名">{{ form.store?.contactName }}</a-descriptions-item>
          <a-descriptions-item label="联系电话">{{ formatNumber(form.store?.contactNumber?.toString()) }}</a-descriptions-item>
          <a-descriptions-item label="联系电话2">{{ formatNumber(form.store?.contactNumber2?.toString() ?? '-') }}</a-descriptions-item>
          <a-descriptions-item label="店铺状态">
            <a-tag :color="storeBusinessStateOptions.find((item) => item.value === form.store?.businessState)?.color ?? ''">
              {{ storeBusinessStateOptions.find((item) => item.value === form.store?.businessState)?.label ?? '' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="店铺地址" :span="2">{{ form.store?.provinceName }} - {{ form.store?.cityName }} - {{ form.store?.areaName }} - {{ form.store?.detailAddress }}</a-descriptions-item>
        </a-descriptions>
      </a-card>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>

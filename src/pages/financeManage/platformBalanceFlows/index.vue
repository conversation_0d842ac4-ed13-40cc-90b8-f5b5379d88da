<script lang="ts" setup>
import useHooks from './hooks'

const { loading, queryParams, pagination, rows, selectedId, selectedIds, selectAll, rowSelect, rowClick, query, reset, pageChange, pageSizeChange, balance, availableBalance } = useHooks()

onMounted(() => {
  pageChange(1)
})
</script>

<template>
  <div class="page-container">
    <div class="h-full flex flex-col gap-[18px]">
      <a-card :bordered="false">
        <a-form :model="queryParams" auto-label-width>
          <a-row :gutter="16">
            <a-col :span="6">
              <a-form-item show-colon label="日期" field="createTime">
                <a-range-picker v-model="queryParams.createTime" :placeholder="[`${$selectPlaceholder}开始日期`, `${$selectPlaceholder}结束日期`]" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item hide-label>
                <a-space :size="18">
                  <a-button type="primary" @click="pageChange(1)">
                    <template #icon>
                      <icon-search />
                    </template>
                    查询
                  </a-button>
                  <a-button type="outline" @click="reset">
                    <template #icon>
                      <icon-refresh />
                    </template>
                    重置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <a-card :bordered="false" class="flex-1 overflow-y-hidden" :body-style="{ height: '100%' }">
        <a-row class="mb-[12px]">
          <a-col :span="24" class="text-right">
            <a-space>
              <span class="font-semibold">余额：{{ balance }} 元；可用余额：{{ availableBalance }} 元</span>
              <a-button type="primary" @click="pageChange(1)">
                <template #icon>
                  <icon-refresh />
                </template>
                刷新
              </a-button>
            </a-space>
          </a-col>
        </a-row>
        <!-- :selected-keys="selectedIds"
          :row-selection="{ type: 'checkbox', showCheckedAll: true, onlyCurrent: true }"
          @select-all="selectAll"
          @select="rowSelect"
          @row-click="rowClick" -->
        <a-table size="large" row-key="id" :loading="loading" :pagination="false" :data="rows" :bordered="{ cell: true }" :scroll="{ y: 'calc(100% - 96px)' }">
          <template #columns>
            <a-table-column align="center" title="序号" :width="80">
              <template #cell="{ rowIndex }">
                {{ rowIndex + 1 }}
              </template>
            </a-table-column>
            <a-table-column align="center" title="类型" :width="150">
              <template #cell="{ record }">
                <a-tag :color="record.type === 1 ? 'green' : 'orange'">{{ record.type === 1 ? '分账收入' : '支出' }}</a-tag>
              </template>
            </a-table-column>
            <a-table-column align="center" title="店铺" :width="200" ellipsis tooltip>
              <template #cell="{ record }">
                {{ record.store?.name ?? '-' }}
              </template>
            </a-table-column>
            <a-table-column align="center" title="关联订单" :width="200">
              <template #cell="{ record }">
                {{ record.order?.orderNo ?? '-' }}
              </template>
            </a-table-column>
            <a-table-column align="center" title="金额" :width="150">
              <template #cell="{ record }">{{ record.amount }} 元</template>
            </a-table-column>
            <a-table-column align="center" title="状态" :width="150">
              <template #cell="{ record }">
                {{ record.state === 1 ? '处理完成' : '处理中' }}
              </template>
            </a-table-column>
            <a-table-column align="center" title="余额" :width="150">
              <template #cell="{ record }">{{ record.balance }} 元</template>
            </a-table-column>
            <a-table-column align="center" title="可用余额" :width="150">
              <template #cell="{ record }">{{ record.availableBalance }} 元</template>
            </a-table-column>
            <a-table-column align="center" title="创建时间" :width="180" data-index="createTime" />
          </template>
        </a-table>
        <template #actions>
          <a-pagination
            v-if="!!pagination.total"
            :current="pagination.current"
            :page-size="pagination.pageSize"
            :show-total="pagination.showTotal"
            :show-page-size="pagination.showPageSize"
            :page-size-options="pagination.pageSizeOptions"
            :total="pagination.total!"
            @change="pageChange"
            @page-size-change="pageSizeChange" />
        </template>
      </a-card>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>

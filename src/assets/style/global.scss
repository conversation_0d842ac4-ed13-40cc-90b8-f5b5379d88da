@use './tailwind.scss';
@use './arco-init.scss';

* {
  box-sizing: border-box;
}

html,
body {
  @apply w-full h-full leading-normal;
  color: var(--color-text-1);
  background-color: var(--color-bg-1);
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  margin: 0;
  left: 0;
}
img {
  // 禁止拖动
  -webkit-user-drag: none;
}
img[src=''],
img:not([src]) {
  opacity: 0;
}
// 主页container样式
.page-container {
  @apply flex-1 rounded-[var(--border-radius-medium)] overflow-x-hidden overflow-y-scroll no-scrollbar;
}
// 抽屉container样式
.drawer-container {
  @apply h-full rounded-[var(--border-radius-medium)] overflow-x-hidden overflow-y-scroll no-scrollbar;
}
// 自定义a-card样式
.general-card {
  .arco-card-header {
    padding-top: 20px !important;
    padding-bottom: 0 !important;
    padding-left: 20px !important;
    border-bottom: none;
    &::before {
      position: absolute;
      top: 23px;
      left: 4px;
      display: block;
      width: 4px;
      height: 20px;
      background-color: rgb(var(--primary-3));
      border-radius: 2px;
      content: '';
    }
  }

  .arco-card-body {
    padding: 20px !important;
  }
}
// 自定义echarts tooltip样式
.echarts-tooltip-diy {
  background: linear-gradient(304.17deg, rgba(253, 254, 255, 0.6) -6.04%, rgba(244, 247, 252, 0.6) 85.2%) !important;
  border: none !important;
  backdrop-filter: blur(10px) !important;
  border-radius: 6px !important;
  .content-panel {
    display: flex;
    justify-content: space-between;
    padding: 0 9px;
    background: rgba(255, 255, 255, 0.8);
    width: 164px;
    height: 32px;
    line-height: 32px;
    box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
    border-radius: 4px;
    margin-bottom: 4px;
  }
  .tooltip-title {
    margin: 0 0 10px 0;
  }
  p {
    margin: 0;
  }
  .tooltip-title,
  .tooltip-value {
    font-size: 13px;
    line-height: 15px;
    display: flex;
    align-items: center;
    text-align: right;
    color: #1d2129;
    font-weight: bold;
  }
  .tooltip-item-icon {
    display: inline-block;
    margin-right: 8px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
  }
}
// 自定义colorPicker样式
.zs-color-picker {
  .zs-color-picker-panel {
    border-radius: 4px;
    box-shadow: none;
    margin-top: 4px;
    right: 0;
    background: var(--color-bg-2);
    border-color: var(--color-border-1);
    transition: all 0.8s ease;
    .default-color {
      color: var(--color-text-1);
    }
    h3 {
      color: var(--color-text-1);
      margin: 10px 0;
    }
    .bottom-btn {
      h3 {
        display: none;
      }
      .finsh {
        background: rgb(var(--primary-6));
        height: 30px;
        padding: 0 15px;
        font-size: 14px;
        border-radius: var(--border-radius-small);
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 0;
        flex: 1;
      }
    }
  }
}

// 隐藏input（禁用自动填充密码）
.fake-input {
  width: 0;
  height: 0;
  padding: 0;
  border: none;
}

.arco-card {
  border-radius: var(--border-radius-medium) !important;
  &.arco-card-size-medium {
    .arco-card-header {
      margin: 0 16px;
      padding: 14px 0;
      height: auto !important;
      min-height: 46px;
    }
    .arco-card-body {
      padding-top: 20px;
    }
  }
}
.arco-drawer {
  .arco-drawer-body {
    background-color: var(--color-fill-2);
  }
}

.arco-table {
  .arco-table-content {
    .arco-table-header {
      .arco-table-tr {
        .arco-checkbox-icon {
          width: 16px;
          height: 16px;
        }
      }
    }
    .arco-table-body {
      .arco-table-tr {
        &:hover {
          cursor: pointer;
        }
        &.arco-table-tr-checked {
          .arco-table-td {
            background-color: rgb(var(--primary-1)) !important;
          }
        }
        .arco-checkbox-icon {
          width: 16px;
          height: 16px;
        }
      }
    }
  }
}

.arco-alert {
  border-radius: var(--border-radius-medium) !important;
  &.arco-alert-info {
    border-color: var(--color-primary-light-2) !important;
  }
}

.arco-table-pagination {
  margin-top: 20px !important;
}

.arco-spin {
  width: 100%;
}
.arco-picker {
  width: 100%;
  background-color: transparent !important;
  border-radius: var(--border-radius-medium) !important;
  border: 1px solid var(--color-border-2) !important;
  &.arco-picker-focused {
    border-color: rgb(var(--primary-6)) !important;
    input {
      background-color: transparent !important;
    }
  }
  &.arco-picker-disabled {
    color: var(--color-text-2) !important;
    background-color: var(--color-fill-2) !important;
  }
}
.arco-select-view {
  background-color: transparent !important;
  border-radius: var(--border-radius-medium) !important;
  border: 1px solid var(--color-border-2) !important;
  &.arco-select-view-focus {
    border-color: rgb(var(--primary-6)) !important;
  }
  &.arco-select-view-disabled {
    color: var(--color-text-2) !important;
    background-color: var(--color-fill-2) !important;
  }
  &.arco-select-view-error {
    border-color: rgb(var(--danger-6)) !important;
  }
  .arco-select-view-input[disabled] {
    -webkit-text-fill-color: var(--color-text-2) !important;
  }
}
.arco-textarea-wrapper {
  background-color: transparent !important;
  border-radius: var(--border-radius-medium) !important;
  border: 1px solid var(--color-border-2) !important;
  &.arco-textarea-focus {
    border-color: rgb(var(--primary-6)) !important;
  }
  &.arco-textarea-disabled {
    background-color: var(--color-fill-2) !important;
  }
  &.arco-textarea-error {
    border-color: rgb(var(--danger-6)) !important;
  }
}
.arco-input-wrapper {
  background-color: transparent !important;
  border-radius: var(--border-radius-medium) !important;
  border: 1px solid var(--color-border-2) !important;
  &.arco-input-focus {
    border-color: rgb(var(--primary-6)) !important;
  }
  &.arco-input-disabled {
    background-color: var(--color-fill-2) !important;
  }
  &.arco-input-error {
    border-color: rgb(var(--danger-6)) !important;
  }
  .arco-input[disabled] {
    -webkit-text-fill-color: var(--color-text-2) !important;
  }
}
.arco-input-outer {
  &.arco-input-number-mode-button {
    .arco-input-wrapper {
      .arco-input {
        text-align: center;
      }
    }
  }
  &:has(.arco-input-prepend) {
    .arco-input-wrapper {
      border-top-left-radius: 0 !important;
      border-bottom-left-radius: 0 !important;
    }
    .arco-input-prepend {
      border-top-left-radius: var(--border-radius-medium) !important;
      border-bottom-left-radius: var(--border-radius-medium) !important;
      background-color: transparent !important;
      border: 1px solid var(--color-border-2) !important;
      border-right-width: 0 !important;
    }
  }
  &:has(.arco-input-append) {
    .arco-input-wrapper {
      border-top-right-radius: 0 !important;
      border-bottom-right-radius: 0 !important;
    }
    .arco-input-append {
      border-top-right-radius: var(--border-radius-medium) !important;
      border-bottom-right-radius: var(--border-radius-medium) !important;
      background-color: transparent !important;
      border: 1px solid var(--color-border-2) !important;
      border-left-width: 0 !important;
    }
  }
}
.arco-textarea[disabled] {
  -webkit-text-fill-color: var(--color-text-2) !important;
}
.arco-picker input[disabled] {
  -webkit-text-fill-color: var(--color-text-2) !important;
}
.arco-btn {
  &.arco-btn-outline {
    color: var(--color-text-2) !important;
    border-color: var(--color-border-2) !important;
    &:hover {
      color: rgb(var(--primary-6)) !important;
      border-color: rgb(var(--primary-6)) !important;
    }
  }
}

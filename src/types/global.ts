import { RouteMeta } from 'vue-router'

export type IRoutes = {
  path: string
  hidden: boolean
  component: string
  children: IRoutes[]
  name?: string
  redirect?: string
  alwaysShow?: boolean
  meta?: RouteMeta
}
export interface IMeta {
  title: string
  icon: string
  noCache: boolean
}

export type AnyObject = {
  [key: string]: unknown
}

export type ListQueryParams = {
  pageNum?: number
  pageSize?: number
  [key: string]: unknown
}

export type Options = {
  label: string
  value: string | number
  color?: string
  extra?: any
}
export type NodeOptions = Options & {
  children?: NodeOptions[]
}

export type TimeRanger = [string, string]

export type GeneralChart = {
  xAxis: string[]
  data: Array<{ name: string; value: number[] }>
}

export type Ocr = {
  picType: number
  side?: string
  url: string
}

export type UploadFileType = {
  fileName: string
  fileSize: string
  fileType: string
  keyValue: string
  originalFileName: string
}

export type FileType =
  | (UploadFileType & {
      fileUrl: string
      id?: string
      relationId?: string
      [key: string]: unknown
    })
  | null

import { ListQueryParams } from '@/types/global'
import useHttp from './useHttp'
import axios from 'axios'
import { baseUrl } from './request'
import { getToken } from '@/utils/auth'
import { resolveBlob } from '@/utils'
import { Notification } from '@arco-design/web-vue'

// 用户列表
export const reqGetUsers = (data: ListQueryParams) => {
  return useHttp({
    url: `/admin/platform/user/list`,
    method: 'post',
    data
  })
}
// 添加用户
export const reqAddUser = (data: any) => {
  return useHttp({
    url: `/admin/platform/user/add`,
    method: 'put',
    data
  })
}
// 获取用户详情
export const reqGetUserDetail = (id: string) => {
  return useHttp({
    url: `/admin/platform/user/detail/${id}`,
    method: 'get'
  })
}
// 修改用户
export const reqEditUser = (id: string, data: any) => {
  return useHttp({
    url: `/admin/platform/user/edit/${id}`,
    method: 'put',
    data
  })
}
// 删除用户
export const reqDelUser = (id: string) => {
  return useHttp({
    url: `/admin/platform/user/del/${id}`,
    method: 'delete'
  })
}
// 恢复用户
export const reqRecoverUser = (id: string) => {
  return useHttp({
    url: `/admin/platform/user/recover/${id}`,
    method: 'delete'
  })
}

// 修改状态
export const reqChangeUserState = (id: string, state: number) => {
  return useHttp({
    url: `/admin/platform/user/changeState/${id}`,
    method: 'put',
    data: { state }
  })
}
// 批量导入用户
export const reqBatchUploadUser = (data: any) => {
  return useHttp({
    url: `/admin/platform/user/batchUpload`,
    method: 'put',
    data
  })
}
// 导出用户
export const reqExportUser = () => {
  return new Promise((resolve, reject) => {
    axios({
      url: baseUrl + `/admin/platform/user/exports`,
      method: 'post',
      responseType: 'blob',
      headers: { Authorization: 'Bearer ' + getToken() }
    })
      .then((res) => {
        if (res.data.type === 'application/vnd.openxmlformats') {
          resolveBlob(res, `application/vnd.openxmlformats;charset=utf-8`, `用户信息.xlsx`)
          resolve(true)
        } else {
          Notification.warning({
            title: '下载出错',
            content: '请稍后重试',
            duration: 3000
          })
          reject()
        }
      })
      .catch(() => {
        reject()
      })
  })
}

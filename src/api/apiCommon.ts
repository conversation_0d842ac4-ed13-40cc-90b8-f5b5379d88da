import type { Ocr } from '@/types/global'
import useHttp from './useHttp'

// 获取验证码
export const reqGetCodeImage = () => {
  return useHttp({
    url: `/common/captchaImage`,
    method: 'get'
  })
}
// OCR识别
export const ocrRecognition = (data: Ocr) => {
  return useHttp({
    url: `/common/ocr/recognition`,
    method: 'post',
    data
  })
}
// 获取下拉列表
export const reqGetOptions = (type: string, params: any = {}) => {
  return useHttp({
    url: `/common/options/${type}`,
    method: 'get',
    params: { ...params, isPaymentVersion: !!Number(import.meta.env.VITE_IS_PAYMENT_VERSION) }
  })
}
// 获取地址建议
export const reqGetAddressTips = (data: any) => {
  return useHttp({
    url: `/common/addressTips`,
    method: 'post',
    data
  })
}

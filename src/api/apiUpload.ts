import { Notification } from '@arco-design/web-vue'
import useHttp from './useHttp'
import OSS from 'ali-oss'

const refreshSTSToken = async (): Promise<{
  accessKeyId: string
  accessKeySecret: string
  stsToken: string
}> => {
  return new Promise(async (resolve, reject) => {
    try {
      const { data } = await useHttp({
        url: `/common/file/getUploadToken`,
        method: 'get'
      })
      resolve({
        accessKeyId: data.accessKeyId,
        accessKeySecret: data.accessKeySecret,
        stsToken: data.stsToken
      })
    } catch (error) {
      reject(error)
    }
  })
}
// 上传文件
export const reqUpload = async (file: File, frontUpload: boolean = true): Promise<string> => {
  if (!frontUpload) {
    // 后端上传
    return new Promise(async (resolve, reject) => {
      try {
        const formData = new FormData()
        formData.append('file', file)
        const { data } = await useHttp({
          url: `/common/file/upload`,
          method: 'post',
          data: formData
        })
        resolve(data as string)
      } catch (error) {
        reject()
      }
    })
  } else {
    // 前端上传
    return new Promise(async (resolve, reject) => {
      try {
        const token = await refreshSTSToken()
        const client = new OSS({
          endpoint: import.meta.env.VITE_OSS_ENDPOINT,
          bucket: import.meta.env.VITE_OSS_BUCKET,
          region: import.meta.env.VITE_OSS_REGION,
          accessKeyId: token.accessKeyId,
          accessKeySecret: token.accessKeySecret,
          stsToken: token.stsToken,
          cname: true,
          secure: true,
          refreshSTSTokenInterval: 3000000,
          refreshSTSToken: refreshSTSToken,
          timeout: 600000
        })
        const ossName = file.type.includes('zip') ? `${file.name}` : `${new Date().getTime()}.${file.name.split('.')[file.name.split('.').length - 1]}`
        // const ossName = file.name
        // const ossName = file.name
        if (Math.round((file.size / 1024 / 1024) * 100) / 100 > 1024) {
          // 大于1024M，分片上传
          const res = await client.multipartUpload(`/mall/${ossName}`, file, {
            // 获取分片上传进度、断点和返回值。
            progress: (p, cpt, res) => {},
            // 设置并发上传的分片数量。
            parallel: 4,
            // 设置分片大小。默认值为1 MB，最小值为100 KB。
            partSize: 1024 * 1024 * 20 // 20M
          })
          // console.log('res :>> ', `https://${import.meta.env.VITE_OSS_BUCKET}.${import.meta.env.VITE_OSS_ENDPOINT}${res.name}`)
          resolve(`https://${import.meta.env.VITE_OSS_BUCKET}.${import.meta.env.VITE_OSS_ENDPOINT}${res.name}`)
        } else {
          // 简单上传
          const res = await client.put(`/mall/${ossName}`, file)
          // console.log('res :>> ', res)
          resolve(res.url)
        }
      } catch (error) {
        Notification.warning({
          title: '错误提示',
          content: (error as Error).message,
          duration: 3000
        })
        reject()
      }
    })
  }
}

import request from './request'
import type { Method, AxiosHeaders } from 'axios'
// 定义请求类型
export type ReqConfig = {
  headers?: any
  url: string
  method: Method
  data?: { [key: string]: unknown } | FormData
  params?: { [key: string]: unknown }
}
// 定义返回类型
export type ResConfig<T> = {
  status: number
  data: T
  msg: string
}
/**
 * @param reqConfig
 * @returns
 */
const useHttp = (reqConfig: ReqConfig): Promise<ResConfig<any>> => {
  return new Promise((resolve, reject) => {
    request({
      headers: reqConfig.headers,
      url: reqConfig.url,
      method: reqConfig.method,
      data: reqConfig.data,
      params: reqConfig.params
    })
      .then((res) => {
        resolve(res.data)
      })
      .catch((err) => {
        reject(err)
      })
  })
}

export default useHttp

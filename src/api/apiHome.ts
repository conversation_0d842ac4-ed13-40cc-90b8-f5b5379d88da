import type { Ocr } from '@/types/global'
import useHttp from './useHttp'

// 统计
export const reqGetStatistics = () => {
  return useHttp({
    url: `/admin/platform/statistic`,
    method: 'get'
  })
}
// 预约图表数据
export const reqGetOrderChart = () => {
  return useHttp({
    url: `/admin/platform/orderChart`,
    method: 'get'
  })
}
// 用户图表数据
export const reqGetUserChart = () => {
  return useHttp({
    url: `/admin/platform/userChart`,
    method: 'get'
  })
}
// 营收图表数据
export const reqGetFinanceChart = (params: any) => {
  return useHttp({
    url: `/admin/platform/financeChart`,
    method: 'get',
    params
  })
}

// 访客图表数据
export const reqGetVisitorChart = (params: any) => {
  return useHttp({
    url: `/admin/platform/visitorChart`,
    method: 'get',
    params
  })
}

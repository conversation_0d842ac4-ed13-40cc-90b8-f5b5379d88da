import { ListQueryParams } from '@/types/global'
import useHttp from './useHttp'

// 账号列表
export const reqGetAccounts = (data: ListQueryParams) => {
  return useHttp({
    url: `/admin/platform/account/list`,
    method: 'post',
    data
  })
}
// 添加账号
export const reqAddAccount = (data: any) => {
  return useHttp({
    url: `/admin/platform/account/add`,
    method: 'put',
    data
  })
}
// 获取账号详情
export const reqGetAccountDetail = (id: string) => {
  return useHttp({
    url: `/admin/platform/account/detail/${id}`,
    method: 'get'
  })
}
// 修改账号
export const reqEditAccount = (id: string, data: any) => {
  return useHttp({
    url: `/admin/platform/account/edit/${id}`,
    method: 'put',
    data
  })
}
// 删除账号
export const reqDelAccount = (id: string) => {
  return useHttp({
    url: `/admin/platform/account/del/${id}`,
    method: 'delete'
  })
}

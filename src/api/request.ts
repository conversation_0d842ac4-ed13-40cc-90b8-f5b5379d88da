import axios from 'axios'
import { Notification } from '@arco-design/web-vue'
import { isLogin, clearToken, getToken } from '@/utils/auth'

export const baseUrl: string = import.meta.env.VITE_API_URL

const instance = axios.create({
  baseURL: baseUrl,
  method: 'POST',
  timeout: 600000
})
const statusEnum = {
  success: (code: string | number): boolean => [200, '200'].indexOf(code) >= 0,
  noPermission: (code: string | number): boolean => [401, '401'].indexOf(code) >= 0
}
// 添加请求拦截器
instance.interceptors.request.use(
  (config) => {
    if (isLogin()) {
      config.headers!['Authorization'] = 'Bearer ' + getToken()
    }
    // get请求映射params参数
    if (config.method === 'get' && config.params) {
      let url = config.url + '?'
      for (const propName of Object.keys(config.params)) {
        const value = config.params[propName]
        var part = encodeURIComponent(propName) + '='
        if (value !== null && typeof value !== 'undefined') {
          if (typeof value === 'object') {
            for (const key of Object.keys(value)) {
              let params = propName + '[' + key + ']'
              var subPart = encodeURIComponent(params) + '='
              url += subPart + encodeURIComponent(value[key]) + '&'
            }
          } else {
            url += part + encodeURIComponent(value) + '&'
          }
        }
      }
      url = url.slice(0, -1)
      config.params = {}
      config.url = url
    }
    return config
  },
  (error) => {
    // 处理请求错误
    console.error(error)
    return Promise.reject(error)
  }
)

// 添加响应拦截器
instance.interceptors.response.use(
  (response) => {
    const { status, msg } = response.data
    if (statusEnum.success(status)) {
      return Promise.resolve(response)
    } else {
      if (statusEnum.noPermission(status)) {
        clearToken()
        Notification.warning({
          title: '错误提示',
          content: msg,
          duration: 3000,
          onClose: () => {
            setTimeout(() => {
              location.href = import.meta.env.VITE_BASE_PATH
            }, 200)
          }
        })
      } else {
        Notification.warning({
          title: '错误提示',
          content: msg,
          duration: 3000
        })
      }
      return Promise.reject()
    }
  },
  (error) => {
    Notification.warning({
      title: '系统错误',
      content: error.message || error.response.data.message || error.response.statusText,
      duration: 5000
    })
    return Promise.reject(error)
  }
)

export default instance

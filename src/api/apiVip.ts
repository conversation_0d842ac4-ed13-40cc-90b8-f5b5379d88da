import { ListQueryParams } from '@/types/global'
import useHttp from './useHttp'
import axios from 'axios'
import { baseUrl } from './request'
import { getToken } from '@/utils/auth'
import { resolveBlob } from '@/utils'
import { Notification } from '@arco-design/web-vue'

// 会员列表
export const reqGetVips = (data: ListQueryParams) => {
  return useHttp({
    url: `/admin/platform/vip/list`,
    method: 'post',
    data
  })
}
// 获取会员详情
export const reqGetVipDetail = (id: string) => {
  return useHttp({
    url: `/admin/platform/vip/detail/${id}`,
    method: 'get'
  })
}
// 审核会员
export const reqApproveVip = (id: string, data: { approveState: number; approveReason?: string }) => {
  return useHttp({
    url: `/admin/platform/vip/approve/${id}`,
    method: 'put',
    data
  })
}
// 删除会员
export const reqDelVip = (id: string) => {
  return useHttp({
    url: `/admin/platform/vip/del/${id}`,
    method: 'delete'
  })
}
// 导出会员
export const reqExportVip = () => {
  return new Promise((resolve, reject) => {
    axios({
      url: baseUrl + `/admin/platform/vip/exports`,
      method: 'post',
      responseType: 'blob',
      headers: { Authorization: 'Bearer ' + getToken() }
    })
      .then((res) => {
        if (res.data.type === 'application/vnd.openxmlformats') {
          resolveBlob(res, `application/vnd.openxmlformats;charset=utf-8`, `会员信息.xlsx`)
          resolve(true)
        } else {
          Notification.warning({
            title: '下载出错',
            content: '请稍后重试',
            duration: 3000
          })
          reject()
        }
      })
      .catch(() => {
        reject()
      })
  })
}

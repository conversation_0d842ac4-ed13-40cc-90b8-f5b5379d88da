import { ListQueryParams } from '@/types/global'
import useHttp from './useHttp'
import axios from 'axios'
import { baseUrl } from './request'
import { resolveBlob } from '@/utils'
import { Notification } from '@arco-design/web-vue'
import { getToken } from '@/utils/auth'

// 店铺流水列表
export const reqGetStoreFundFlows = (data: ListQueryParams) => {
  return useHttp({
    url: `/admin/platform/storeFundFlow/list`,
    method: 'post',
    data
  })
}

// 店铺流水详情
export const reqGetStoreFundFlowDetail = (id: string) => {
  return useHttp({
    url: `/admin/platform/storeFundFlow/detail/${id}`,
    method: 'get'
  })
}

// 导出流水详情
export const reqExportStoreFundFlow = () => {
  return new Promise((resolve, reject) => {
    axios({
      url: baseUrl + `/admin/platform/storeFundFlow/exports`,
      method: 'post',
      responseType: 'blob',
      headers: { Authorization: 'Bearer ' + getToken() }
    })
      .then((res) => {
        if (res.data.type === 'application/vnd.openxmlformats') {
          resolveBlob(res, `application/vnd.openxmlformats;charset=utf-8`, `店铺流水信息.xlsx`)
          resolve(true)
        } else {
          Notification.warning({
            title: '下载出错',
            content: '请稍后重试',
            duration: 3000
          })
          reject()
        }
      })
      .catch(() => {
        reject()
      })
  })
}

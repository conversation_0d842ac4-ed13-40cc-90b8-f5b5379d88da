import { ListQueryParams } from '@/types/global'
import useHttp from './useHttp'

// 用户佣金列表
export const reqGetUserCommissionFlows = (data: ListQueryParams) => {
  return useHttp({
    url: `/admin/platform/userCommissionFlow/list`,
    method: 'post',
    data
  })
}
// 提现审核
export const reqApprovePayout = (id: string, data: { approveState: number; approveReason: string }) => {
  return useHttp({
    url: `/admin/platform/userCommissionFlow/approvePayout/${id}`,
    method: 'put',
    data
  })
}
// 提现打款
export const reqPayout = (id: string) => {
  return useHttp({
    url: `/admin/platform/userCommissionFlow/payout/${id}`,
    method: 'put'
  })
}

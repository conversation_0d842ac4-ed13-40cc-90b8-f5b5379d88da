import { ListQueryParams } from '@/types/global'
import useHttp from './useHttp'

// 角色列表
export const reqGetAccountRoles = (data: ListQueryParams) => {
  return useHttp({
    url: `/admin/platform/accountRole/list`,
    method: 'post',
    data
  })
}
// 添加角色
export const reqAddAccountRole = (data: any) => {
  return useHttp({
    url: `/admin/platform/accountRole/add`,
    method: 'put',
    data
  })
}
// 获取角色详情
export const reqGetAccountRoleDetail = (id: string) => {
  return useHttp({
    url: `/admin/platform/accountRole/detail/${id}`,
    method: 'get'
  })
}
// 修改角色
export const reqEditAccountRole = (id: string, data: any) => {
  return useHttp({
    url: `/admin/platform/accountRole/edit/${id}`,
    method: 'put',
    data
  })
}
// 删除角色
export const reqDelAccountRole = (id: string) => {
  return useHttp({
    url: `/admin/platform/accountRole/del/${id}`,
    method: 'delete'
  })
}
// 设置权限
export const reqSetAccountRolePermission = (id: string, data: { checkedKeys: string[]; halfCheckedKeys: string[] }) => {
  return useHttp({
    url: `/admin/platform/accountRole/permission/${id}`,
    method: 'put',
    data
  })
}

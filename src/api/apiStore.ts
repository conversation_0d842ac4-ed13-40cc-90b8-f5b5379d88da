import { ListQueryParams } from '@/types/global'
import useHttp from './useHttp'
import axios from 'axios'
import { baseUrl } from './request'
import { getToken } from '@/utils/auth'
import { resolveBlob } from '@/utils'
import { Notification } from '@arco-design/web-vue'

// 店铺列表
export const reqGetStores = (data: ListQueryParams) => {
  return useHttp({
    url: `/admin/platform/store/list`,
    method: 'post',
    data
  })
}
// 添加店铺
export const reqAddStore = (data: any) => {
  return useHttp({
    url: `/admin/platform/store/add`,
    method: 'put',
    data
  })
}
// 获取店铺详情
export const reqGetStoreDetail = (id: string) => {
  return useHttp({
    url: `/admin/platform/store/detail/${id}`,
    method: 'get'
  })
}
// 修改店铺
export const reqEditStore = (id: string, data: any) => {
  return useHttp({
    url: `/admin/platform/store/edit/${id}`,
    method: 'put',
    data
  })
}
// 删除店铺
export const reqDelStore = (id: string) => {
  return useHttp({
    url: `/admin/platform/store/del/${id}`,
    method: 'delete'
  })
}
// 审核店铺
export const reqApproveStore = (id: string, data: { approveState: number; approveReason?: string }) => {
  return useHttp({
    url: `/admin/platform/store/approve/${id}`,
    method: 'put',
    data
  })
}
// 导出店铺
export const reqExportStore = () => {
  return new Promise((resolve, reject) => {
    axios({
      url: baseUrl + `/admin/platform/store/exports`,
      method: 'post',
      responseType: 'blob',
      headers: { Authorization: 'Bearer ' + getToken() }
    })
      .then((res) => {
        if (res.data.type === 'application/vnd.openxmlformats') {
          resolveBlob(res, `application/vnd.openxmlformats;charset=utf-8`, `店铺信息.xlsx`)
          resolve(true)
        } else {
          Notification.warning({
            title: '下载出错',
            content: '请稍后重试',
            duration: 3000
          })
          reject()
        }
      })
      .catch(() => {
        reject()
      })
  })
}

// 首页推荐店铺列表
export const reqGetRecommendStores = () => {
  return useHttp({
    url: `/admin/platform/store/recommendList`,
    method: 'get'
  })
}
// 添加首页推荐店铺
export const reqAddRecommendStore = (data: any) => {
  return useHttp({
    url: `/admin/platform/store/addRecommend`,
    method: 'put',
    data
  })
}
// 删除首页推荐店铺
export const reqDelRecommendStore = (id: string) => {
  return useHttp({
    url: `/admin/platform/store/delRecommend/${id}`,
    method: 'delete'
  })
}
// 设置首页推荐店铺顺序
export const reqSetRecommendStoreSortIndex = (id: string, recommendSortIndex: number) => {
  return useHttp({
    url: `/admin/platform/store/setRecommendSortIndex/${id}`,
    method: 'put',
    data: { recommendSortIndex }
  })
}

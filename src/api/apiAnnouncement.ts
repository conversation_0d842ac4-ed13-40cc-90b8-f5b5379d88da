import { ListQueryParams } from '@/types/global'
import useHttp from './useHttp'

// 公告列表
export const reqGetAnnouncements = (data: ListQueryParams) => {
  return useHttp({
    url: `/admin/platform/announcement/list`,
    method: 'post',
    data
  })
}
// 添加公告
export const reqAddAnnouncement = (data: any) => {
  return useHttp({
    url: `/admin/platform/announcement/add`,
    method: 'put',
    data
  })
}
// 获取公告详情
export const reqGetAnnouncementDetail = (id: string) => {
  return useHttp({
    url: `/admin/platform/announcement/detail/${id}`,
    method: 'get'
  })
}
// 修改公告
export const reqEditAnnouncement = (id: string, data: any) => {
  return useHttp({
    url: `/admin/platform/announcement/edit/${id}`,
    method: 'put',
    data
  })
}
// 删除公告
export const reqDelAnnouncement = (id: string) => {
  return useHttp({
    url: `/admin/platform/announcement/del/${id}`,
    method: 'delete'
  })
}

import { ListQueryParams } from '@/types/global'
import useHttp from './useHttp'

// 商品列表
export const reqGetCommodities = (data: ListQueryParams) => {
  return useHttp({
    url: `/admin/platform/commodity/list`,
    method: 'post',
    data
  })
}
// 获取商品详情
export const reqGetCommodityDetail = (id: string) => {
  return useHttp({
    url: `/admin/platform/commodity/detail/${id}`,
    method: 'get'
  })
}
// 审核商品
export const reqApproveCommodity = (id: string, data: { approveState: number; approveReason?: string }) => {
  return useHttp({
    url: `/admin/platform/commodity/approve/${id}`,
    method: 'put',
    data
  })
}

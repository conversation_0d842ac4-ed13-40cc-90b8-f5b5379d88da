import useHttp from './useHttp'
import { LoginForm } from '@/pages/login/useLogin'

// 登录
export const reqLogin = (data: LoginForm) => {
  return useHttp({
    url: `/admin/platform/login`,
    method: 'post',
    data
  })
}
// 获取info
export const reqGetUserInfo = () => {
  return useHttp({
    url: `/admin/platform/account/getUserInfo`,
    method: 'get'
  })
}
// 修改密码
export const reqChangePassword = (data: { oldPassword: string; newPassword: string }) => {
  return useHttp({
    url: `/admin/platform/account/changePassword`,
    method: 'post',
    data
  })
}

import { ListQueryParams } from '@/types/global'
import useHttp from './useHttp'
import axios from 'axios'
import { baseUrl } from './request'
import { getToken } from '@/utils/auth'
import { resolveBlob } from '@/utils'
import { Notification } from '@arco-design/web-vue'

// 订单列表
export const reqGetOrders = (data: ListQueryParams) => {
  return useHttp({
    url: `/admin/platform/order/list`,
    method: 'post',
    data
  })
}
// 导出订单
export const reqExportOrder = () => {
  return new Promise((resolve, reject) => {
    axios({
      url: baseUrl + `/admin/platform/order/exports`,
      method: 'post',
      responseType: 'blob',
      headers: { Authorization: 'Bearer ' + getToken() }
    })
      .then((res) => {
        if (res.data.type === 'application/vnd.openxmlformats') {
          resolveBlob(res, `application/vnd.openxmlformats;charset=utf-8`, `订单记录.xlsx`)
          resolve(true)
        } else {
          Notification.warning({
            title: '下载出错',
            content: '请稍后重试',
            duration: 3000
          })
          reject()
        }
      })
      .catch(() => {
        reject()
      })
  })
}

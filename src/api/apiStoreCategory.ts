import { ListQueryParams } from '@/types/global'
import useHttp from './useHttp'

// 店铺分类列表
export const reqGetStoreCategories = (data: ListQueryParams) => {
  return useHttp({
    url: `/admin/platform/storeCategory/list`,
    method: 'post',
    data
  })
}
// 添加店铺分类
export const reqAddStoreCategory = (data: any) => {
  return useHttp({
    url: `/admin/platform/storeCategory/add`,
    method: 'put',
    data
  })
}
// 获取店铺分类详情
export const reqGetStoreCategoryDetail = (id: string) => {
  return useHttp({
    url: `/admin/platform/storeCategory/detail/${id}`,
    method: 'get'
  })
}
// 修改店铺分类
export const reqEditStoreCategory = (id: string, data: any) => {
  return useHttp({
    url: `/admin/platform/storeCategory/edit/${id}`,
    method: 'put',
    data
  })
}
// 删除店铺分类
export const reqDelStoreCategory = (id: string) => {
  return useHttp({
    url: `/admin/platform/storeCategory/del/${id}`,
    method: 'delete'
  })
}
// 设置商品分类顺序
export const reqSetStoreCategorySortIndex = (id: string, sortIndex: number) => {
  return useHttp({
    url: `/admin/platform/storeCategory/setSortIndex/${id}`,
    method: 'put',
    data: { sortIndex }
  })
}
// 金刚区店铺分类列表
export const reqGetFastEntryStoreCategories = () => {
  return useHttp({
    url: `/admin/platform/storeCategory/fastEntryList`,
    method: 'get'
  })
}
// 添加金刚区店铺分类
export const reqAddFastEntryStoreCategory = (data: any) => {
  return useHttp({
    url: `/admin/platform/storeCategory/addFastEntry`,
    method: 'put',
    data
  })
}
// 删除金刚区店铺分类
export const reqDelFastEntryStoreCategory = (id: string) => {
  return useHttp({
    url: `/admin/platform/storeCategory/delFastEntry/${id}`,
    method: 'delete'
  })
}
// 设置金刚区店铺分类顺序
export const reqSetFastEntryStoreCategorySortIndex = (id: string, fastEntrySortIndex: number) => {
  return useHttp({
    url: `/admin/platform/storeCategory/setFastEntrySortIndex/${id}`,
    method: 'put',
    data: { fastEntrySortIndex }
  })
}

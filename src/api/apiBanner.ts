import { ListQueryParams } from '@/types/global'
import useHttp from './useHttp'

// Banner列表
export const reqGetBanners = (data: ListQueryParams) => {
  return useHttp({
    url: `/admin/platform/banner/list`,
    method: 'post',
    data
  })
}
// 添加Banner封面
export const reqAddBanner = (data: any) => {
  return useHttp({
    url: `/admin/platform/banner/add`,
    method: 'put',
    data
  })
}
// 获取Banner详情
export const reqGetBannerDetail = (id: string) => {
  return useHttp({
    url: `/admin/platform/banner/detail/${id}`,
    method: 'get'
  })
}
// 删除Banner
export const reqDelBanner = (id: string) => {
  return useHttp({
    url: `/admin/platform/banner/del/${id}`,
    method: 'delete'
  })
}
// 设置展厅Banner顺序
export const reqSetBannerSortIndex = (id: string, sortIndex: number) => {
  return useHttp({
    url: `/admin/platform/banner/setSortIndex/${id}`,
    method: 'put',
    data: { sortIndex }
  })
}

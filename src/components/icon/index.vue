<script lang="ts" setup>
import { Icon } from '@arco-design/web-vue'

const IconFont = Icon.addFromIconFontCn({ src: '//at.alicdn.com/t/c/font_4679491_2367no0vgx7.js' })

withDefaults(
  defineProps<{
    icon: string
    size?: number
  }>(),
  {
    size: 18
  }
)
</script>

<template>
  <div>
    <icon-font v-if="icon.includes('icon-')" :type="icon" :size="size" />
    <component v-else :is="`icon-${icon}`" :size="size"></component>
  </div>
</template>

<style lang="scss" scoped></style>

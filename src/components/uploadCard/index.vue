<script lang="ts" setup>
import { reqUpload } from '@/api/apiUpload'
import { Notification, RequestOption } from '@arco-design/web-vue'

defineProps<{
  accept?: string
  title?: string
  disabled?: boolean
}>()
const emits = defineEmits<{
  (e: 'uploadSuccess', val: { url: string }): void
  (e: 'uploadError'): void
}>()
const src = defineModel<string | undefined>()
const uploading = ref<boolean>(false)
const handleUpload = (option: RequestOption): any => {
  upload(option)
}
const upload = async (option: RequestOption): Promise<void> => {
  const { onSuccess, onError, fileItem } = option
  try {
    uploading.value = true
    const url = await reqUpload(fileItem.file!)
    src.value = url
    uploading.value = false
    onSuccess(url)
    emits('uploadSuccess', { url })
  } catch (error) {
    emits('uploadError')
    uploading.value = false
    onError({ error })
  }
}
const showImagePreview = ref<boolean>(false)
</script>

<template>
  <div class="upload-wrapper">
    <template v-if="!!src">
      <div class="image-wrapper">
        <a-image fit="contain" :src="src" :preview-visible="showImagePreview" @preview-visible-change="showImagePreview = false"></a-image>
        <div class="wrapper__mark">
          <icon-eye :size="20" @click="showImagePreview = true" />
          <icon-delete v-if="!disabled" :size="20" @click="src = undefined" />
        </div>
      </div>
    </template>
    <a-spin v-else-if="uploading" :loading="uploading" class="!w-[120px]">
      <div class="upload-inner">
        <icon-upload :size="20" />
        <span class="mt-2 text-center">{{ title }}</span>
      </div>
    </a-spin>
    <a-upload v-else :accept="accept" :limit="1" :show-file-list="false" :custom-request="handleUpload">
      <template #upload-button>
        <div class="upload-inner">
          <icon-upload :size="20" />
          <span class="mt-2 text-center">{{ title }}</span>
        </div>
      </template>
    </a-upload>
  </div>
</template>

<style lang="scss" scoped>
.upload-wrapper {
  @apply w-full h-[120px] flex justify-start;
  .image-wrapper {
    @apply relative overflow-hidden w-[120px] h-[120px] rounded-[var(--border-radius-medium)];
    .wrapper__mark {
      @apply absolute top-0 left-0 w-full h-full bg-black/50 text-white cursor-pointer flex justify-around items-center opacity-0;
      &:hover {
        @apply opacity-100;
      }
    }
    :deep(.arco-image) {
      @apply w-full h-full bg-[var(--color-neutral-1)];
      .arco-image-img {
        @apply w-full h-full;
      }
    }
  }
  .upload-inner {
    @apply w-[120px] h-[120px] rounded-[var(--border-radius-medium)] border border-dashed border-[var(--color-border-3)] hover:border-[rgb(var(--primary-6))] flex flex-col justify-center items-center text-gray-500;
  }
}
</style>

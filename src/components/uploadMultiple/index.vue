<script lang="ts" setup>
import { reqUpload } from '@/api/apiUpload'
import { FileItem, Notification, RequestOption, Modal } from '@arco-design/web-vue'
import { v4 as uuidv4 } from 'uuid'
type MyFile = {
  uid: string
  name: string
  size: number
  url: string
}
const props = defineProps<{
  accept?: string
  title?: string
  disabled?: boolean
  limit?: number
}>()
const emits = defineEmits<{
  (e: 'uploadSuccess', val: MyFile[]): void
  (e: 'uploadError'): void
}>()
const files = defineModel<MyFile[]>({
  default: () => []
})
const uploading = ref<boolean>(false)
const handleUpload = (option: RequestOption): any => {
  upload(option)
}
const upload = async (option: RequestOption): Promise<void> => {
  const { onSuccess, onError, fileItem } = option
  try {
    // console.log('fileItem :>> ', fileItem)
    uploading.value = true
    const url = await reqUpload(fileItem.file!)
    files.value = [
      ...files.value,
      {
        uid: uuidv4(),
        name: fileItem.name!,
        size: fileItem.file!.size,
        url
      }
    ]
    uploading.value = false
    onSuccess(url)
    emits('uploadSuccess', files.value)
  } catch (error) {
    emits('uploadError')
    uploading.value = false
    onError({ error })
  }
}
const beforeRemove = (fileItem: FileItem) => {
  return new Promise((resolve, reject) => {
    Modal.warning({
      title: '提示',
      content: () => h('div', { class: 'text-center' }, `确定删除【${fileItem.name}】？`),
      maskClosable: false,
      escToClose: false,
      hideCancel: false,
      cancelButtonProps: { type: 'outline' },
      onOk: () => {
        files.value = files.value.filter((item) => item.uid !== fileItem.uid)
        resolve(true)
      },
      onCancel: () => reject('cancel')
    })
  })
}
</script>

<template>
  <a-upload :accept="accept" :file-list="files" multiple :limit="limit || 100" :custom-request="handleUpload">
    <template #upload-button>
      <a-button v-if="!disabled" :loading="uploading" type="primary">
        <template #icon>
          <icon-upload />
        </template>
        {{ title }}
      </a-button>
    </template>
    <template #upload-item="{ fileItem, index }">
      <div :key="index" class="upload-list-item">
        <div class="flex flex-1 justify-between items-center px-[12px] py-[8px] bg-[var(--color-fill-1)] rounded-md">
          <div class="flex items-center gap-x-[16px]">
            <a-image class="cursor-pointer" width="40" height="40" fit="contain" :src="fileItem.url" />
            <!-- <icon-file :size="18" class="!text-[rgb(var(--primary-6))]" /> -->
            <div>
              <div class="max-w-[300px] truncate">{{ fileItem.name }}</div>
              <span class="text-xs text-gray-500">{{ fileItem.size ? Math.round((fileItem.size / 1024 / 1024) * 100) / 100 : 0 }}M</span>
            </div>
          </div>
          <icon-loading v-if="fileItem.percent < 1" :size="18" />
          <icon-check v-else :size="18" class="!text-green-500" />
        </div>
        <icon-delete
          v-if="!disabled"
          :size="18"
          class="cursor-pointer"
          @click="
            () => {
              if (fileItem.percent < 1) return
              beforeRemove(fileItem)
            }
          " />
      </div>
    </template>
  </a-upload>
</template>

<style lang="scss" scoped>
.upload-list-item {
  @apply flex items-center justify-between mt-2 gap-x-[16px];
  transition: background-color 0.1s cubic-bezier(0, 0, 1, 1);
}
</style>

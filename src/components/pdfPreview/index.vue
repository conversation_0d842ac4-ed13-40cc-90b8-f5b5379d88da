<script lang="ts" setup>
import VueOfficePdf from '@vue-office/pdf'

defineProps<{
  title: string
  src: string
}>()
const visible = defineModel<boolean>('visible')
</script>

<template>
  <a-modal :visible="visible" :title="title" fullscreen body-style="height:100%" unmount-on-close :footer="false" @cancel="visible = false">
    <vue-office-pdf :install="() => {}" :src="src" />
  </a-modal>
</template>

<style lang="scss" scoped>
.vue-office-pdf {
  height: 100% !important;
}
:deep(.vue-office-pdf-wrapper) {
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>

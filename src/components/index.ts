import { App } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'echarts/charts'
import { Grid<PERSON>omponent, TooltipComponent, LegendComponent, DataZoomComponent, GraphicComponent } from 'echarts/components'
import Icon from './icon/index.vue'
import Chart from './chart/index.vue'
import TablePagination from './tablePagination/index.vue'
import PdfPreview from './pdfPreview/index.vue'
import DocPreview from './docPreview/index.vue'
import ExcelPreview from './excelPreview/index.vue'
import RichTextEditor from './richTextEditor/index.vue'
import UploadCard from './uploadCard/index.vue'

// Manually introduce ECharts modules to reduce packing size

use([Canvas<PERSON>ender<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>rid<PERSON>omponent, Tooltip<PERSON>omponent, LegendComponent, DataZoomComponent, GraphicComponent])

export default {
  install(Vue: App) {
    Vue.component('Icon', Icon)
    Vue.component('Chart', Chart)
    Vue.component('PdfPreview', PdfPreview)
    Vue.component('DocPreview', DocPreview)
    Vue.component('ExcelPreview', ExcelPreview), Vue.component('RichTextEditor', RichTextEditor)
    Vue.component('UploadCard', UploadCard)
  }
}

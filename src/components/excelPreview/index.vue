<script lang="ts" setup>
import VueOfficeExcel from '@vue-office/excel'
import '@vue-office/excel/lib/index.css'

defineProps<{
  title: string
  src: string
}>()
const visible = defineModel<boolean>('visible')
</script>

<template>
  <a-modal :visible="visible" :title="title" fullscreen body-style="height:100%" unmount-on-close :footer="false" @cancel="visible = false">
    <vue-office-excel :install="() => {}" :src="src" />
  </a-modal>
</template>

<style lang="scss" scoped></style>

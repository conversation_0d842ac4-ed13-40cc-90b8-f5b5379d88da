<script lang="ts" setup>
import { ECBasicOption } from 'echarts/types/dist/shared'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Pie<PERSON>hart } from 'echarts/charts'
import { TitleComponent, TooltipComponent, LegendComponent, GraphicComponent, GridComponent } from 'echarts/components'
import VChart from 'vue-echarts'
import { ref } from 'vue'

use([<PERSON>vas<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Pie<PERSON>hart, TitleComponent, TooltipComponent, LegendComponent, GraphicComponent, GridComponent])

withDefaults(
  defineProps<{
    options?: ECBasicOption
    autoResize?: boolean
    width?: string
    height?: string
  }>(),
  {
    autoResize: true,
    width: '100%',
    height: '100%'
  }
)
const emits = defineEmits(['mouseover', 'mouseout'])
const renderChart = ref(false)

nextTick(() => {
  renderChart.value = true
})

const vChartRef = ref()
defineExpose({
  vChartRef
})
</script>

<template>
  <v-chart v-if="renderChart" ref="vChartRef" :option="options" :autoresize="autoResize" :style="{ width, height }" @mouseover="emits('mouseover', $event)" @mouseout="emits('mouseout', $event)" />
</template>

<style lang="scss" scoped></style>

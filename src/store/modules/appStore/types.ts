// import type { RouteRecordNormalized } from 'vue-router'

export type AppState = {
  appTheme: string
  showNavBar: boolean
  showBreadcrumb: boolean
  showSideMenu: boolean
  showTopMenu: boolean
  showTabBar: boolean
  hideMenu: boolean
  menuCollapse: boolean
  showFooter: boolean
  themeColor: string
  menuWidth: number
  showGlobalSettingsDrawer: boolean
  showChangePasswordModal: boolean
  showNoticeDrawer: boolean
  device: string
  menuFromServer: boolean
  // serverMenu: RouteRecordNormalized[]
  [key: string]: unknown
}
//

import { defineStore } from 'pinia'
import type { RouteRecordNormalized } from 'vue-router'
import defaultSettings from '@/config/settings.json'
import type { AppState } from './types'

const useAppStore = defineStore('appStore', {
  state: (): AppState => ({ ...defaultSettings }),

  getters: {
    appCurrentSetting(state: AppState): AppState {
      return { ...state }
    },
    appDevice(state: AppState) {
      return state.device
    },
    appAsyncMenus(state: AppState): RouteRecordNormalized[] {
      return state.serverMenu as unknown as RouteRecordNormalized[]
    },
    appMenuCollapse(state: AppState): boolean {
      return state.menuCollapse
    }
  },

  actions: {
    // 修改页面设置
    updateAppSettings(data: Partial<AppState>) {
      // @ts-ignore-next-line
      this.$patch({ ...data })
    },
    // 修改主题色
    toggleAppTheme(dark: boolean) {
      if (localStorage.getItem('app-theme') === 'auto' || localStorage.getItem('app-theme') === null || localStorage.getItem('app-theme') === undefined) {
        this.appTheme = 'light'
        document.body.removeAttribute('arco-theme')
      } else {
        this.appTheme = 'dark'
        document.body.setAttribute('arco-theme', 'dark')
      }
    },
    // 切换设备
    toggleDevice(device: string) {
      this.device = device
    }
  }
})

export default useAppStore

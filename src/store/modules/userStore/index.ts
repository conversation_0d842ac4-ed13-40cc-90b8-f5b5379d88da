import type { UserState } from './types'
import { defineStore } from 'pinia'
import { LoginForm } from '@/pages/login/useLogin'
import { reqGetUserInfo, reqLogin } from '@/api/apiLogin'
import { setToken, clearToken } from '@/utils/auth'

const useUserStore = defineStore('userStoare', {
  state: (): UserState => ({
    username: undefined,
    nickname: undefined,
    role: undefined,
    state: undefined,
    permission: undefined
  }),
  getters: {
    getUserRole(state: UserState): string | undefined {
      return state.role
    },
    getSystemName(state: UserState): string {
      return import.meta.env.VITE_TITLE
    },
    getNickName(state: UserState): string {
      return state.nickname || '用户'
    },
    getUserName(state: UserState): string {
      return state.username || ''
    },
    getPermission(state: UserState): string[] {
      return state.permission || []
    }
  },
  actions: {
    async login(loginForm: LoginForm) {
      try {
        const { data: token } = await reqLogin(loginForm)
        setToken(token as string)
      } catch (error) {
        clearToken()
        throw error
      }
    },
    async getUserInfo() {
      try {
        const { data } = await reqGetUserInfo()
        this.setInfo({ ...data })
      } catch (error) {
        throw error
      }
    },
    logout() {
      this.setInfo({ username: undefined, nickname: undefined, role: undefined, state: undefined, permission: undefined })
    },
    setInfo(data: UserState) {
      this.$patch(data)
    }
  }
})

export default useUserStore

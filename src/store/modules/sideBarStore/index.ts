import type { SideBarState } from './types'
import { defineStore } from 'pinia'
import { Router, RouteRecordName, RouteRecordRaw } from 'vue-router'
import { filterAsyncRouter, treeFilter, treeToArray } from '@/utils/auth'
import { IRoutes } from '@/types/global'
import { DEFAULT_ROUTE_NAME } from '@/router/constants'
import useUserStore from '../userStore'

const useSideBarStore = defineStore('sideBarStore', {
  state: (): SideBarState => ({
    selectedKeys: [],
    allMenuItems: [], // 所有路由
    allMenuNames: [], // 所有路由名称
    menuItems: [], // 用户有权限的路由
    menuNames: [], // 用户有权限的路由名称
    visitedMenus: [] // 访问过的menus
  }),
  getters: {
    getSelectedKeys(state: SideBarState): RouteRecordName[] {
      return state.selectedKeys.length ? state.selectedKeys : [DEFAULT_ROUTE_NAME]
    },
    getAllMenus(state: SideBarState): RouteRecordRaw[] {
      return state.allMenuItems
    },
    getAllMenuNames(state: SideBarState): string[] {
      return state.allMenuNames
    },
    getMenus(state: SideBarState): RouteRecordRaw[] {
      const isPayMentVersion = Number(import.meta.env.VITE_IS_PAYMENT_VERSION)
      if (!!isPayMentVersion) {
        return state.menuItems
      } else {
        return state.menuItems.filter((item) => item.path !== 'popularizes' && item.path !== 'orders')
      }
    },
    getMenuNames(state: SideBarState): string[] {
      const isPayMentVersion = Number(import.meta.env.VITE_IS_PAYMENT_VERSION)
      if (!!isPayMentVersion) {
        return state.menuNames
      } else {
        return state.menuNames.filter((item) => !item.includes('Popularizes') && !item.includes('Orders'))
      }
    },
    getVisitedMenus(state: SideBarState): { path: string; name: string; title: string; params: any; query: any }[] {
      return state.visitedMenus || []
    }
  },
  actions: {
    setSelectedKeys(key: RouteRecordName) {
      this.selectedKeys = [key]
    },
    // 获取菜单列表
    // async getMenuList(router: Router) {
    //   try {
    //     const { data } = await reqGetMenuList()
    //     this.menuItems = filterAsyncRouter(data as IRoutes[])
    //     this.menuItems.unshift({
    //       path: 'home',
    //       name: 'Home',
    //       component: () => import('@/pages/home/<USER>'),
    //       meta: {
    //         title: '首页',
    //         icon: 'apps',
    //         noCache: true
    //       }
    //     })
    //     // 添加动态路由
    //     if (!!this.menuItems.length) {
    //       for (const item of this.menuItems) {
    //         router.addRoute(DEFAULT_ROUTE_NAME, item)
    //       }
    //     }
    //     router.addRoute({
    //       path: '/:pathMatch(.*)',
    //       redirect: '/404'
    //     })
    //   } catch (error) {}
    // },
    // 获取菜单列表
    async getMenuList(router: Router) {
      return new Promise((resolve) => {
        const userStore = useUserStore()
        const allMenuTree = router.options.routes.find((item) => item.name === DEFAULT_ROUTE_NAME)?.children ?? []
        const allMenuNames = treeToArray<RouteRecordRaw, string>(allMenuTree, 'name')
        // console.log('allMenuTree :>> ', allMenuTree)
        // console.log('allMenuNames :>> ', allMenuNames)
        // const filterMenuTree = treeFilter(allMenuTree, allMenuNames)
        const filterMenuTree = treeFilter(allMenuTree, toRaw(userStore.getPermission))
        const filterMenuNames = treeToArray<RouteRecordRaw, string>(filterMenuTree, 'name')
        // console.log('filterMenuTree :>> ', filterMenuTree)
        this.allMenuItems = allMenuTree
        this.allMenuNames = allMenuNames
        this.menuItems = filterMenuTree
        this.menuNames = filterMenuNames
        router.addRoute({
          path: '/:pathMatch(.*)',
          redirect: '/404'
        })
        resolve(true)
      })
    },
    // 清空菜单
    clearMenuList() {
      this.menuItems = []
    },
    addVisitedMenu(menu: any) {
      if (menu.path === '/home' || this.visitedMenus.some((v) => v.path === menu.path)) return
      this.visitedMenus.push(menu)
    },
    // 删除访问过的菜单
    removeVisitedMenu(menuPath: string) {
      for (const [i, v] of this.visitedMenus.entries()) {
        if (v.path === menuPath) {
          this.visitedMenus.splice(i, 1)
          break
        }
      }
    }
  }
})

export default useSideBarStore

import { createApp, toRaw } from 'vue'
import App from './App.vue'

// 额外引入图标库
import ArcoVueIcon from '@arco-design/web-vue/es/icon'
// 导入Message,Notification等组件的样式
import '@arco-design/web-vue/es/modal/style/css.js'
import '@arco-design/web-vue/es/message/style/css.js'
import '@arco-design/web-vue/es/notification/style/css.js'
import store from './store'
import router from './router'
import ColorPicker from 'colorpicker-v3'
import 'colorpicker-v3/style.css'
import '@/assets/style/global.scss'
import { spaceFormat, trim } from './utils'

// 自定义类型申明
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $inputPlaceholder: string
    $selectPlaceholder: string
    $uploadPlaceholder: string
    $spaceFormat: (value: string, isNumber?: boolean) => string
    $trim: (value: string) => string
  }
}
const app = createApp(App)
app.use(ArcoVueIcon)
app.use(ColorPicker)
app.use(store)
app.use(router)
// 定义全局变量
app.config.globalProperties.$inputPlaceholder = '请输入'
app.config.globalProperties.$selectPlaceholder = '请选择'
app.config.globalProperties.$uploadPlaceholder = '请上传'
// 定义全局方法
app.config.globalProperties.$spaceFormat = spaceFormat
app.config.globalProperties.$trim = trim
app.mount('#app')

declare module 'colorpicker-v3' {
  interface Colorpicker {
    defaultColor: string
    hex: string
    rgba: string
    btnStyle: object
    opacity: number
    showOpacity: boolean
    standardColor: string[]
    themeColor: string[]
    change: ({ hex: string, rgba: string }) => void
    finish: ({ hex: string, rgba: string }) => void
    close: ({ hex: string, rgba: string }) => void
  }
  const colorpicker: Colorpicker
  export default colorPicker
}

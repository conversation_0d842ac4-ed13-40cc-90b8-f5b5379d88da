import type { NavigationGuardNext, RouteLocationNormalized, Router } from 'vue-router'
// import { setRouteEmitter } from '@/utils/routeListener'
import setupPermission from './permission'

const setupGuard = (router: Router) => {
  router.beforeEach((to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) => {
    // 监听路由变化统一分发
    // setRouteEmitter(to)
    // 设置页面title
    document.title = `${to.meta.title} - ${import.meta.env.VITE_TITLE}`
    next()
  })
}
const routeGuard = (router: Router) => {
  setupGuard(router)
  setupPermission(router)
}
export default routeGuard

import type { Router, RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
import { isLogin } from '@/utils/auth'
import { LOGIN_ROUTE_NAME, DEFAULT_ROUTE_NAME, NO_PERMISSION_NAME, NOT_FOUND_NAME } from '../constants'
import { useUserStore, useSideBarStore } from '@/store'
const setupPermission = (router: Router) => {
  router.beforeEach(async (to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) => {
    const sideBarStore = useSideBarStore()
    const userStore = useUserStore()
    if (isLogin()) {
      if (to.name === LOGIN_ROUTE_NAME) {
        next({ name: DEFAULT_ROUTE_NAME })
      } else {
        if (!userStore.getUserRole && !sideBarStore.getMenus.length) {
          // 获取用户信息
          await userStore.getUserInfo()
          // 获取路由表
          await sideBarStore.getMenuList(router)
          if (sideBarStore.getMenus[0].children) {
            sideBarStore.setSelectedKeys(sideBarStore.getMenus[0].children[0].name as string)
          } else {
            sideBarStore.setSelectedKeys(sideBarStore.getMenus[0].name as string)
          }
          if (to.name === DEFAULT_ROUTE_NAME) {
            if (sideBarStore.getMenus[0].children) {
              next({ name: sideBarStore.getMenus[0].children[0].name as string, replace: true })
            } else {
              next({ name: sideBarStore.getMenus[0].name as string, replace: true })
            }
          } else {
            const allMenuNames = toRaw(sideBarStore.getAllMenuNames)
            const menuNames = toRaw(sideBarStore.getMenuNames)
            // console.log('to :>> ', to)
            // console.log('allMenuNames :>> ', allMenuNames)
            // console.log('menuNames :>> ', menuNames)
            if (!to.name || !allMenuNames.includes(to.name as string)) {
              next({ name: NOT_FOUND_NAME })
            } else {
              if (!menuNames.includes(to.name as string)) {
                next({ name: NO_PERMISSION_NAME })
              } else {
                next({ ...to, replace: true })
              }
            }
          }
        } else {
          if (to.name && to.name !== DEFAULT_ROUTE_NAME) sideBarStore.setSelectedKeys(to.name!)
          next()
        }
      }
    } else {
      if (to.name === LOGIN_ROUTE_NAME) {
        next()
      } else {
        next({ name: LOGIN_ROUTE_NAME })
      }
    }
  })
}

export default setupPermission

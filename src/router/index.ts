import { createRouter, createWebHistory, createWebHashHistory, Router } from 'vue-router'
import routes from './routes'
import routeGuard from './guard'

const router: Router = createRouter({
  history: createWebHashHistory(import.meta.env.VITE_BASE_PATH),
  routes,
  // 保持页面位置
  scrollBehavior: (to, from, savePosition) => {
    if (savePosition) {
      return savePosition
    } else {
      return {
        top: 0
      }
    }
  }
})
routeGuard(router)
export default router

import type { RouteRecordRaw } from 'vue-router'
import { LOGIN_ROUTE_NAME, DEFAULT_ROUTE_NAME, NOT_FOUND_NAME, NO_PERMISSION_NAME } from './constants'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: DEFAULT_ROUTE_NAME,
    component: () => import('@/pages/main.vue'),
    // children: []
    children: [
      {
        path: 'home',
        name: 'Home',
        component: () => import('@/pages/home/<USER>'),
        meta: {
          title: '首页',
          icon: 'home',
          noCache: true
        }
      },
      {
        path: 'users',
        name: 'Users',
        component: () => import('@/pages/users/index.vue'),
        meta: {
          title: '用户管理',
          icon: 'user-group',
          noCache: true
        }
      },
      {
        path: 'orders',
        name: 'Orders',
        component: () => import('@/pages/orders/index.vue'),
        meta: {
          title: '订单管理',
          icon: 'bookmark',
          noCache: true
        }
      },
      {
        path: 'vips',
        name: 'Vips',
        meta: {
          title: '会员管理',
          icon: 'stamp',
          noCache: true
        },
        children: [
          {
            path: 'approve',
            name: 'VipApprove',
            component: () => import('@/pages/vips/index.vue'),
            meta: {
              title: '会员审核',
              // icon: 'stamp',
              noCache: true
            }
          },
          {
            path: 'list',
            name: 'VipList',
            component: () => import('@/pages/vips/index.vue'),
            meta: {
              title: '会员列表',
              noCache: true
            }
          }
        ]
      },
      {
        path: 'stores',
        name: 'Stores',
        meta: {
          title: '店铺管理',
          icon: 'icon-building',
          noCache: true
        },
        children: [
          {
            path: 'categories',
            name: 'StoreCategories',
            component: () => import('@/pages/storeCategories/index.vue'),
            meta: {
              title: '店铺分类',
              noCache: true
            }
          },
          {
            path: 'approve',
            name: 'StoreApprove',
            component: () => import('@/pages/stores/approve.vue'),
            meta: {
              title: '入驻审核',
              // icon: 'stamp',
              noCache: true
            }
          },
          {
            path: 'list',
            name: 'StoreList',
            component: () => import('@/pages/stores/index.vue'),
            meta: {
              title: '店铺列表',
              noCache: true
            }
          }
        ]
      },
      // {
      //   path: 'approves',
      //   name: 'Approves',
      //   meta: {
      //     title: '审核管理',
      //     icon: 'stamp',
      //     noCache: true
      //   },
      //   children: [
      //     {
      //       path: 'stores',
      //       name: 'StoreApprove',
      //       component: () => import('@/pages/stores/approve.vue'),
      //       meta: {
      //         title: '入驻审核',
      //         noCache: true
      //       }
      //     },
      //     {
      //       path: 'commodities',
      //       name: 'CommodityApprove',
      //       component: () => import('@/pages/commodities/approve.vue'),
      //       meta: {
      //         title: '商品审核',
      //         noCache: true
      //       }
      //     }
      //   ]
      // },
      {
        path: 'marketings',
        name: 'Marketings',
        meta: {
          title: '营销管理',
          icon: 'relation',
          noCache: true
        },
        children: [
          {
            path: 'banners',
            name: 'Banners',
            component: () => import('@/pages/marketings/banners/index.vue'),
            meta: {
              title: 'Banner',
              noCache: true
            }
          },
          // {
          //   path: 'fastEntryStoreCategories',
          //   name: 'FastEntryStoreCategories',
          //   component: () => import('@/pages/marketings/fastEntryStoreCategories/index.vue'),
          //   meta: {
          //     title: '金刚区',
          //     noCache: true
          //   }
          // },
          {
            path: 'recommendStores',
            name: 'RecommendStores',
            component: () => import('@/pages/marketings/recommendStores/index.vue'),
            meta: {
              title: '推荐店铺',
              noCache: true
            }
          }
        ]
      },
      {
        path: 'popularizes',
        name: 'Popularizes',
        meta: {
          title: '推广管理',
          icon: 'folder',
          noCache: true
        },
        children: [
          {
            path: 'users',
            name: 'PopularizesUsers',
            component: () => import('@/pages/users/popularizes.vue'),
            meta: {
              title: '推广明细',
              noCache: true
            }
          },
          {
            path: 'orders',
            name: 'PopularizesOrders',
            component: () => import('@/pages/orders/popularizes.vue'),
            meta: {
              title: '推广订单',
              noCache: true
            }
          }
        ]
      },
      {
        path: 'financeManage',
        name: 'FinanceManage',
        meta: {
          title: '财务管理',
          icon: 'computer',
          noCache: true
        },
        children: [
          {
            path: 'storeFundFlows',
            name: 'StoreFundFlows',
            component: () => import('@/pages/financeManage/storeFundFlows/index.vue'),
            meta: {
              title: '店铺流水',
              noCache: true
            }
          },
          {
            path: 'platformBalanceFlows',
            name: 'PlatformBalanceFlows',
            component: () => import('@/pages/financeManage/platformBalanceFlows/index.vue'),
            meta: {
              title: '平台佣金',
              noCache: true
            }
          },
          {
            path: 'userCommissionFlows',
            name: 'UserCommissionFlows',
            component: () => import('@/pages/financeManage/userCommissionFlows/index.vue'),
            meta: {
              title: '用户佣金',
              noCache: true
            }
          },
          {
            path: 'userCommissionPayouts',
            name: 'UserCommissionPayouts',
            component: () => import('@/pages/financeManage/userCommissionFlows/index.vue'),
            meta: {
              title: '提现记录',
              noCache: true
            }
          }
        ]
      },
      {
        path: 'system',
        name: 'System',
        meta: {
          title: '系统设置',
          icon: 'apps',
          noCache: true
        },
        children: [
          {
            path: 'accountRoles',
            name: 'AccountRoles',
            component: () => import('@/pages/system/accountRoles/index.vue'),
            meta: {
              title: '角色设置',
              noCache: true
            }
          },
          {
            path: 'accounts',
            name: 'Accounts',
            component: () => import('@/pages/system/accounts/index.vue'),
            meta: {
              title: '用户设置',
              noCache: true
            }
          },
          {
            path: 'announcements',
            name: 'Announcements',
            component: () => import('@/pages/system/announcements/index.vue'),
            meta: {
              title: '公告管理',
              noCache: true
            }
          },
          {
            path: 'docs',
            name: 'Docs',
            component: () => import('@/pages/system/docs/index.vue'),
            meta: {
              title: '协议设置',
              noCache: true
            }
          },
          {
            path: 'configSettings',
            name: 'ConfigSettings',
            component: () => import('@/pages/system/configSettings/index.vue'),
            meta: {
              title: '参数设置',
              noCache: true
            }
          }
        ]
      }
    ]
  },
  {
    path: '/login',
    name: LOGIN_ROUTE_NAME,
    component: () => import('@/pages/login/index.vue'),
    meta: {
      title: '登录'
    }
  },
  {
    path: '/401',
    name: NO_PERMISSION_NAME,
    component: () => import('@/pages/401.vue'),
    meta: {
      title: '无权访问该页面'
    }
  },
  {
    path: '/404',
    name: NOT_FOUND_NAME,
    component: () => import('@/pages/404.vue'),
    meta: {
      title: '页面不存在'
    }
  }
  // 跳转404页面
  // {
  //   path: '/:pathMatch(.*)',
  //   redirect: '/404'
  // }
]

export default routes

/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const vueComponent: DefineComponent<{}, {}, any>
  export default vueComponent
}

interface ImportMetaEnv extends Readonly<Record<string, string | boolean | undefined>> {
  readonly VITE_TITLE: string
  readonly VITE_API_URL: string
  readonly VITE_WS_URL: string
  readonly VITE_OSS_ENDPOINT: string
  readonly VITE_OSS_BUCKET: string
  readonly VITE_OSS_REGION: string
  readonly VITE_BASE_PATH: string
  readonly VITE_AMAP_KEY: string
  readonly VITE_IS_PAYMENT_VERSION: string
  // more env variables...
}

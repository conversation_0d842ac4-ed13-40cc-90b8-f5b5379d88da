<script lang="ts" setup></script>

<template>
  <router-view v-slot="{ Component, route }">
    <transition name="forward-transform" mode="out-in" appear>
      <!-- <keep-alive> -->
      <component :is="Component" :key="route.fullPath" />
      <!-- </keep-alive> -->
    </transition>
  </router-view>
</template>

<style lang="scss" scoped>
.forward-transform-enter-from {
  opacity: 0;
  transform: translateX(-4px);
}
.forward-transform-leave-to {
  opacity: 0;
  transform: translateX(4px);
}
.forward-transform-enter-active,
.forward-transform-leave-active {
  transition: all 0.1s;
}
</style>

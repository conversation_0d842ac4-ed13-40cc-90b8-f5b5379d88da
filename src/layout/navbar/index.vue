<script lang="ts" setup>
import logo from '@/assets/images/logo.png'
import { useDark, useToggle, useFullscreen } from '@vueuse/core'
import { useAppStore, useUserStore, useSideBarStore } from '@/store'
import { clearToken } from '@/utils/auth'
import { useRouter } from 'vue-router'
import { Modal, Notification } from '@arco-design/web-vue'

const props = withDefaults(
  defineProps<{
    messageCount?: number
  }>(),
  {
    messageCount: 0
  }
)
const { isFullscreen, toggle: toggleFullScreen } = useFullscreen()
const appStore = useAppStore()
const userStore = useUserStore()
const sideBarStore = useSideBarStore()
const router = useRouter()
const theme = computed(() => appStore.appTheme)
const menuWidth = computed(() => appStore.menuWidth + 'px')
const title = computed(() => userStore.getSystemName)
const nickName = computed(() => userStore.getNickName)
const userRole = computed(() => userStore.getUserRole)
const isDark = useDark({
  selector: 'body',
  attribute: 'arco-theme',
  valueDark: 'dark',
  valueLight: 'light',
  storageKey: 'app-theme',
  onChanged(dark: boolean) {
    appStore.toggleAppTheme(dark)
  }
})
const handleCollapse = () => {
  appStore.updateAppSettings({ menuCollapse: !appStore.appMenuCollapse })
}
const handleToggleTheme = useToggle(isDark)
const logout = () => {
  Modal.info({
    title: '提示',
    content: () => h('div', { class: 'text-center' }, '确定退出当前登录？'),
    maskClosable: false,
    escToClose: false,
    hideCancel: false,
    cancelButtonProps: { type: 'outline' },
    onOk: () => {
      clearToken()
      userStore.logout()
      sideBarStore.clearMenuList()
      location.href = import.meta.env.VITE_BASE_PATH
    }
  })
}
</script>

<template>
  <div class="navbar">
    <div class="left-side">
      <img alt="logo" style="margin-left: 10px; height: 28px" :src="logo" />
      <span>{{ title }}</span>
    </div>
    <!-- <div class="flex justify-center items-center px-2">
      <a-button @click="handleCollapse">
        <template #icon>
          <icon-menu-fold v-if="!appStore.appMenuCollapse" />
          <icon-menu-unfold v-else />
        </template>
      </a-button>
    </div> -->
    <div class="center-side">
      <slot></slot>
    </div>
    <ul class="right-side">
      <li>
        <a-tooltip content="消息通知">
          <a-badge :count="messageCount">
            <!-- @click="appStore.updateAppSettings({ showNoticeDrawer: true })" -->
            <a-button
              class="nav-btn"
              type="outline"
              shape="circle"
              @click="
                () => {
                  Notification.warning({
                    title: '提示',
                    content: `当前无消息通知`,
                    duration: 1500
                  })
                }
              ">
              <template #icon>
                <icon-notification />
              </template>
            </a-button>
          </a-badge>
        </a-tooltip>
      </li>
      <!-- <li>
        <a-tooltip content="使用手册">
          <a-button class="nav-btn" type="outline" shape="circle">
            <template #icon>
              <icon-book />
            </template>
          </a-button>
        </a-tooltip>
      </li> -->
      <li>
        <a-tooltip :content="theme === 'light' ? '点击切换为黑暗模式' : '点击切换为亮色模式'">
          <a-button class="nav-btn" type="outline" shape="circle" @click="handleToggleTheme()">
            <template #icon>
              <icon-moon-fill v-if="theme === 'dark'" />
              <icon-sun-fill v-else />
            </template>
          </a-button>
        </a-tooltip>
      </li>
      <li>
        <a-tooltip :content="isFullscreen ? '点击退出全屏模式' : '点击进入全屏模式'">
          <a-button class="nav-btn" type="outline" shape="circle" @click="toggleFullScreen">
            <template #icon>
              <icon-fullscreen-exit v-if="isFullscreen" />
              <icon-fullscreen v-else />
            </template>
          </a-button>
        </a-tooltip>
      </li>
      <!-- <li>
        <a-tooltip content="页面配置">
          <a-button class="nav-btn" type="outline" shape="circle" @click="appStore.updateAppSettings({ showGlobalSettingsDrawer: true })">
            <template #icon>
              <icon-settings />
            </template>
          </a-button>
        </a-tooltip>
      </li> -->
      <li>
        <a-dropdown trigger="click">
          <a-space class="cursor-pointer">
            <a-avatar :size="32" :style="{ backgroundColor: 'rgb(var(--primary-6))' }">
              <icon-user style="color: white" />
              <!-- <img alt="avatar" src="https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/3ee5f13fb09879ecb5185e440cef6eb9.png~tplv-uwbnlip3yd-webp.webp" /> -->
            </a-avatar>
            <span class="select-none">{{ nickName }}({{ userRole }})</span>
          </a-space>
          <template #content>
            <a-doption>
              <a-space @click="appStore.updateAppSettings({ showChangePasswordModal: true })">
                <icon-settings />
                <span>修改密码</span>
              </a-space>
            </a-doption>
            <a-doption>
              <a-space @click="logout">
                <icon-export />
                <span>退出登录</span>
              </a-space>
            </a-doption>
          </template>
        </a-dropdown>
      </li>
    </ul>
  </div>
</template>

<style lang="scss" scoped>
.navbar {
  --menu-width: v-bind(menuWidth);

  @apply flex justify-between h-full bg-[var(--color-bg-2)];
  border-bottom: 1px solid var(--color-border);
  .left-side {
    @apply flex justify-center items-center gap-2 font-bold text-base min-w-[var(--menu-width)] select-none;
  }
  .center-side {
    @apply flex flex-1 items-center pr-5 gap-x-2;
  }
  .right-side {
    @apply flex;
    :deep(.locale-select) {
      @apply rounded-[20px];
    }
    li {
      @apply flex items-center py-0 px-[10px];
    }
    .nav-btn {
      border-color: rgb(var(--gray-2)) !important;
      color: rgb(var(--gray-8)) !important;
      font-size: 16px;
      &:hover {
        border-color: rgb(var(--gray-2)) !important;
        color: rgb(var(--gray-8)) !important;
      }
    }
    .trigger-btn,
    .ref-btn {
      @apply absolute bottom-[14px];
    }
    .trigger-btn {
      @apply ml-[14px];
    }
  }
}
</style>

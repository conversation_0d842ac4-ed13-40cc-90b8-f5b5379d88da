<script lang="ts" setup>
import { useRoute } from 'vue-router'

const route = useRoute()
const list = ref<string[]>([])
watchEffect(() => {
  list.value = route.name === 'Home' ? [] : route.matched.map((item) => item.meta?.title ?? '').filter((item) => item !== '')
})
</script>

<template>
  <a-breadcrumb class="container-breadcrumb">
    <a-breadcrumb-item>首页</a-breadcrumb-item>
    <a-breadcrumb-item v-for="item in list" :key="item">{{ item }}</a-breadcrumb-item>
  </a-breadcrumb>
</template>

<style lang="scss" scoped></style>

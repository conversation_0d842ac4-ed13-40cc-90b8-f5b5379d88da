<script lang="ts" setup>
import GlobalSetting from './globalSetting/index.vue'
import ChangePassword from './changePassword/index.vue'
import NavBar from './navbar/index.vue'
import Breadcrumb from './breadcrumb/index.vue'
import tabBar from './tabBar/index.vue'
import TopMenu from './topMenu/index.vue'
import SideMenu from './sideMenu/index.vue'
import PageContainer from './pageContainer/index.vue'
import PageFooter from './pageFooter/index.vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '@/store'
import useResponsive from '@/hooks/responsive'
// import { socket } from '@/utils/socket'
// import { Notification } from '@arco-design/web-vue'
useResponsive(true)
const route = useRoute()
const appStore = useAppStore()
const showNavBar: ComputedRef<boolean> = computed(() => appStore.showNavBar)
const showSideMenu: ComputedRef<boolean> = computed(() => appStore.showSideMenu && !appStore.showTopMenu)
const showBreadcrumb: ComputedRef<boolean> = computed(() => appStore.showBreadcrumb)
const showTopMenu: ComputedRef<boolean> = computed(() => appStore.showTopMenu && !appStore.showSideMenu)
const showTabBar: ComputedRef<boolean> = computed(() => appStore.showTabBar)
const navHeight: ComputedRef<string> = computed(() => (appStore.showNavBar ? '60px' : '0px'))
const menuWidth: ComputedRef<string> = computed(() => {
  return showSideMenu.value ? (appStore.menuCollapse ? '48px' : appStore.menuWidth + 'px') : '0px'
})
const showFooter: ComputedRef<boolean> = computed(() => appStore.showFooter)
const messageCount = ref<number>(0)
const initMessageCount = async () => {
  try {
    messageCount.value = 0
  } catch (error) {}
}
// // 组件挂载前让socket连接起来
// onBeforeMount(() => {
//   socket.connect()
// })
// // 组件挂载完毕完成后，监听onApplyStoreUserMessage事件
// onMounted(() => {
//   initMessageCount()
//   socket.on('onApplyStoreUserMessage', (res: { message: string }) => {
//     messageCount.value += 1
//     // 处理接收到的消息
//     Notification.info({
//       title: '消息提示',
//       content: res.message,
//       duration: 3000
//     })
//   })
// })
// // 组件销毁时断开连接
// onUnmounted(() => {
//   socket.disconnect()
// })
</script>

<template>
  <a-layout class="layout">
    <div v-if="showNavBar" class="layout-navbar">
      <nav-bar :messageCount="messageCount">
        <breadcrumb v-if="showBreadcrumb"></breadcrumb>
        <top-menu v-if="showTopMenu"></top-menu>
      </nav-bar>
    </div>
    <a-layout>
      <a-layout-sider v-if="showSideMenu" class="layout-sider" breakpoint="xl" :width="parseInt(menuWidth)" collapsible :collapsed="false" hide-trigger>
        <div class="menu-wrapper">
          <side-menu></side-menu>
        </div>
      </a-layout-sider>
      <!-- <a-drawer placement="left" :footer="false" mask-closable :closable="false">
          <Menu />
        </a-drawer> -->
      <a-layout class="layout-content">
        <tab-bar v-if="showTabBar"></tab-bar>
        <a-layout-content class="layout-content-inner">
          <page-container></page-container>
        </a-layout-content>
        <page-footer v-if="showFooter"></page-footer>
      </a-layout>
    </a-layout>
  </a-layout>
  <global-setting></global-setting>
  <change-password></change-password>
</template>

<style lang="scss" scoped>
.layout {
  --nav-height: v-bind(navHeight);
  --menu-width: v-bind(menuWidth);
  @apply w-full h-full overflow-hidden;
}
.layout-navbar {
  @apply fixed top-0 left-0 z-[100] w-full h-[var(--nav-height)];
}
.layout-sider {
  @apply fixed top-0 left-0 z-[99] h-full pt-[var(--nav-height)];
  transition: all 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
  &::after {
    @apply absolute top-0 -right-[1px] w-[1px] h-full block bg-[var(--color-border)];
    content: '';
  }

  :deep(.arco-layout-sider-children) {
    @apply overflow-y-hidden;
  }
}
.menu-wrapper {
  @apply h-full overflow-auto overflow-x-hidden;
  :deep(.arco-menu) {
    ::-webkit-scrollbar {
      @apply w-[12px] h-[4px];
    }

    ::-webkit-scrollbar-thumb {
      border: 4px solid transparent;
      background-clip: padding-box;
      border-radius: 7px;
      background-color: var(--color-text-4);
    }

    ::-webkit-scrollbar-thumb:hover {
      background-color: var(--color-text-3);
    }
  }
}

.layout-content {
  @apply flex-auto pt-[var(--nav-height)] pl-[var(--menu-width)] h-screen overflow-y-hidden bg-[var(--color-fill-2)];
  transition: padding 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
  .layout-content-inner {
    @apply flex-1 overflow-x-hidden overflow-y-scroll mx-[20px] flex flex-col no-scrollbar rounded-[var(--border-radius-medium)];
  }
}
</style>

<script lang="ts" setup>
const props = defineProps<{
  type: string
  name: string
  defaultValue: boolean | number | string
}>()
const emits = defineEmits<{
  (e: 'inputChange', { value, key }: { value: unknown; key: string }): void
}>()

const hex = ref<string>('#f00')
const handleChange = (value: unknown) => {
  emits('inputChange', {
    value,
    key: props.name
  })
}
const changeColor = (e: unknown) => {
  console.log('e :>> ', e)
  emits('inputChange', {
    value: props.defaultValue,
    key: props.name
  })
}
</script>
<template>
  <color-picker v-if="type === 'string'" @finish="changeColor" :show-opacity="false" :default-color="(defaultValue as string)" v-model:hex="(defaultValue as string)"></color-picker>
  <a-input-number v-else-if="type === 'number'" style="width: 100px" hide-button size="small" :default-value="(defaultValue as number)" @change="handleChange">
    <template #append>px</template>
  </a-input-number>
  <a-switch v-else :default-checked="(defaultValue as boolean)" size="small" @change="handleChange"></a-switch>
</template>

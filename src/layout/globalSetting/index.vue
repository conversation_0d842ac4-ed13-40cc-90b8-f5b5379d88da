<script lang="ts" setup>
import { Notification } from '@arco-design/web-vue'
import { useAppStore } from '@/store'
import { useClipboard } from '@vueuse/core'
import formWrapper from './form-wrapper.vue'
import type { ContentOption } from './types'
const { copy } = useClipboard()
const appStore = useAppStore()
const visible = computed(() => appStore.showGlobalSettingsDrawer)
const contentOptions: ComputedRef<ContentOption[]> = computed(() => [
  { name: '导航栏', key: 'showNavBar', defaultVal: appStore.showNavBar },
  { name: '菜单栏', key: 'showSideMenu', defaultVal: appStore.showSideMenu },
  { name: '顶部菜单栏', key: 'showTopMenu', defaultVal: appStore.showTopMenu },
  { name: '面包屑', key: 'showBreadcrumb', defaultVal: appStore.showBreadcrumb },
  { name: '底部', key: 'showFooter', defaultVal: appStore.showFooter },
  { name: '多页签', key: 'showTabBar', defaultVal: appStore.showTabBar },
  // { name: '菜单来源于后台', key: 'menuFromServer', defaultVal: appStore.menuFromServer },
  { name: '菜单宽度 (px)', key: 'menuWidth', defaultVal: appStore.menuWidth, type: 'number' }
  // { name: '主题色', key: 'themeColor', defaultVal: appStore.themeColor, type: 'string' }
])
const handleClose = () => {
  appStore.updateAppSettings({ showGlobalSettingsDrawer: false })
}
const handleChange = async ({ key, value }: { key: string; value: unknown }) => {
  if (key === 'showTopMenu') {
    appStore.updateAppSettings({
      menuCollapse: false
    })
  }
  appStore.updateAppSettings({ [key]: value })
}
const copySettings = async () => {
  const text = JSON.stringify(appStore.$state, null, 2)
  await copy(text)
  Notification.success({
    title: '成功提示',
    content: `'复制成功，请粘贴到 src/config/settings.json 文件中'`,
    duration: 1500
  })
}
</script>

<template>
  <a-drawer :width="300" :visible="visible" unmount-on-close cancel-text="关闭" :cancel-button-props="{ type: 'outline' }" ok-text="复制配置" @cancel="handleClose" @ok="copySettings">
    <template #title>页面配置</template>
    <div class="block">
      <h5 class="title">内容区域</h5>
      <div v-for="option in contentOptions" :key="option.name" class="switch-wrapper">
        <span>{{ option.name }}</span>
        <form-wrapper :type="option.type || 'switch'" :name="option.key" :default-value="option.defaultVal" @input-change="handleChange"></form-wrapper>
      </div>
    </div>
  </a-drawer>
</template>

<style lang="scss" scoped>
.block {
  margin-bottom: 24px;
  .title {
    margin: 10px 0;
    padding: 0;
    font-size: 14px;
  }
  .switch-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 32px;
  }
}
</style>

<script lang="ts" setup>
import { useRoute, useRouter } from 'vue-router'
import { useSideBarStore } from '@/store'

const route = useRoute()
const router = useRouter()
const sideBarStore = useSideBarStore()
const scrollRef = ref<any>()
watch(
  route,
  (val) => {
    sideBarStore.addVisitedMenu({
      path: val.path,
      name: val.name,
      title: val.meta?.title ?? '',
      params: val.params,
      query: val.query
    })
  },
  { immediate: true }
)
const handleCloseMenu = (menu: any, index: number) => {
  if (menu.path === route.path) {
    if (index === sideBarStore.getVisitedMenus.length - 1) {
      sideBarStore.getVisitedMenus[index - 1] ? router.push(sideBarStore.getVisitedMenus[index - 1].path) : router.push('/home')
    } else {
      router.push(sideBarStore.getVisitedMenus[index + 1].path)
    }
  }
  sideBarStore.removeVisitedMenu(menu.path)
}
</script>

<template>
  <div class="tab-bar-container">
    <a-scrollbar ref="scrollRef" style="overflow-x: auto">
      <div class="scroll-container" style="width: fit-content">
        <router-link to="/home" custom v-slot="{ navigate, isActive }">
          <span @click="navigate" class="tab-bar-item" :class="{ active: sideBarStore.getSelectedKeys.includes('Home') }">
          <a-space>
            <icon-tag />
            <span>首页</span>
          </a-space>
          </span>
        </router-link>
        <router-link 
          v-for="(menu, index) in sideBarStore.getVisitedMenus" 
          :key="index" 
          :to="menu.path" 
          custom 
          v-slot="{ navigate, isActive }"
        >
          <span 
            @click="navigate" 
            class="tab-bar-item" 
            :class="{ active: sideBarStore.getSelectedKeys.includes(menu.name) }"
          >
          <a-space>
            <icon-tag />
            <span>{{ menu.title }}</span>
            <icon-close @click.prevent.stop="handleCloseMenu(menu, index)" />
          </a-space>
          </span>
        </router-link>
      </div>
    </a-scrollbar>
  </div>
</template>

<style lang="scss" scoped>
.tab-bar-container {
  @apply bg-[var(--color-bg-2)] w-full mb-[15px] overflow-visible h-[35px];
  :deep(.arco-scrollbar) {
    .arco-scrollbar-container {
      @apply pt-[7px];
      .scroll-container {
        @apply h-[43px] whitespace-nowrap;
        .tab-bar-item {
          @apply inline-block h-[28px] leading-[28px] px-[18px] rounded-t-[6px] cursor-pointer select-none;
          &.active {
            @apply bg-[var(--color-fill-2)] text-[rgb(var(--primary-6))] font-bold;
          }
        }
      }
    }
  }
}
</style>

<script lang="ts" setup>
import type { RouteMeta, RouteRecordRaw } from 'vue-router'
import { regexUrl, openWindow } from '@/utils'
import { useRouter } from 'vue-router'
defineProps<{
  menuItems?: RouteRecordRaw[]
}>()
const router = useRouter()
const goto = (routeRecordRaw: RouteRecordRaw, e?: Event) => {
  // 阻止默认事件，防止页面刷新
  if (e && typeof e.preventDefault === 'function') {
    e.preventDefault()
  }
  
  // console.log('menuItem.routeRecordRaw :>> ', routeRecordRaw)
  // 打开外部链接
  if (regexUrl.test(routeRecordRaw.path)) {
    openWindow(routeRecordRaw.path)
    return
  }
  // // Eliminate external link side effects
  // const { hideInMenu, activeMenu } = routeRecordRaw.meta as RouteMeta
  // if (route.name === routeRecordRaw.name && !hideInMenu && !activeMenu) {
  //   return
  // }
  router.push({
    name: routeRecordRaw.name
  })
}
</script>

<template>
  <span>
    <template v-for="menuItem in menuItems" :key="(menuItem.name as string)">
      <template v-if="menuItem.children?.length">
        <a-sub-menu :key="(menuItem.name as string)">
          <template #title>{{ menuItem.meta?.title }}</template>
          <menu-item :menuItems="menuItem.children"></menu-item>
        </a-sub-menu>
      </template>
      <template v-else>
        <a-menu-item :key="(menuItem.name as string)" @click="(e) => goto(menuItem, e)">
          {{ menuItem.meta?.title }}
        </a-menu-item>
      </template>
    </template>
  </span>
</template>

<style lang="scss" scoped></style>

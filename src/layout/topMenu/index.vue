<script lang="ts" setup>
import MenuItem from './menuItem.vue'
import { useSideBarStore } from '@/store'
import { RouteRecordRaw, useRouter, useRoute } from 'vue-router'

const router = useRouter()

const sideBarStore = useSideBarStore()

const selectedKeys = computed(() => sideBarStore.getSelectedKeys)
const menuItems = computed(() => sideBarStore.getMenus)
</script>

<template>
  <a-menu :style="{ flex: 1, height: '100%' }" mode="horizontal" :selected-keys="selectedKeys">
    <menu-item :menuItems="menuItems"></menu-item>
  </a-menu>
</template>

<style lang="scss" scoped></style>

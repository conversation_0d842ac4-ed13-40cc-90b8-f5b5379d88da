<script lang="ts" setup>
import { FormInstance } from '@arco-design/web-vue'
import { ChangePasswordForm } from './types'
import { useTemplateRef } from 'vue'

const form = reactive<ChangePasswordForm>({
  oldPassword: undefined,
  newPassword: undefined,
  confirmPassword: undefined
})

const formRef = useTemplateRef<FormInstance>('formRef')
defineExpose({
  formRef,
  form
})
</script>

<template>
  <a-form ref="formRef" :model="form" layout="vertical">
    <!--禁用自动填充密码-->
    <input type="text" class="fake-input" />
    <input type="password" class="fake-input" />
    <a-form-item show-colon label="旧密码" field="oldPassword" :rules="{ required: true, message: `${$inputPlaceholder}旧密码` }">
      <a-input-password v-model="form.oldPassword" :placeholder="`${$inputPlaceholder}旧密码`" />
    </a-form-item>
    <a-form-item
      show-colon
      field="newPassword"
      label="新密码"
      :rules="[
        { required: true, message: `${$inputPlaceholder}新密码` },
        { match: /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[\da-zA-Z!#$%^&@*]{8,16}$/, message: `新密码长度8-16位，同时含有数字、大、小写英文字母，不含有空格` }
      ]">
      <a-input-password v-model="form.newPassword" placeholder="新密码长度8-16位，同时含有数字、大、小写英文字母，不含有空格" />
    </a-form-item>
    <a-form-item show-colon label="确认密码" field="confirmPassword" :rules="{ required: true, message: `${$inputPlaceholder}确认密码` }">
      <a-input-password v-model="form.confirmPassword" :placeholder="`${$inputPlaceholder}确认密码`" />
    </a-form-item>
  </a-form>
</template>

<style lang="scss" scoped></style>

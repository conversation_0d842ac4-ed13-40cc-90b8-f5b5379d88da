<script lang="ts" setup>
import { Modal, Notification } from '@arco-design/web-vue'
import FormCom from './form.vue'
import { useAppStore, useUserStore, useSideBarStore } from '@/store'
import { reqChangePassword } from '@/api/apiLogin'
import useLoading from '@/hooks/useLoading'
import { clearToken } from '@/utils/auth'

const appStore = useAppStore()
const userStore = useUserStore()
const sideBarStore = useSideBarStore()
const visible = computed(() => appStore.showChangePasswordModal)
const { loading, setLoading } = useLoading()

const changePasswordRef = ref<InstanceType<typeof FormCom>>()

const submit = async () => {
  try {
    const errors = await changePasswordRef.value?.formRef?.validate()
    if (!!errors) return
    if (changePasswordRef.value!.form.newPassword === changePasswordRef.value!.form.oldPassword) {
      Notification.error({
        title: '错误提示',
        content: '新密码不能与旧密码相同',
        duration: 1500
      })
      return
    }
    if (changePasswordRef.value!.form.newPassword !== changePasswordRef.value!.form.confirmPassword) {
      Notification.error({
        title: '错误提示',
        content: '两次密码输入不一致',
        duration: 1500
      })
      return
    }
    setLoading(true)
    await reqChangePassword({ oldPassword: changePasswordRef.value!.form.oldPassword!, newPassword: changePasswordRef.value!.form.newPassword! })
    setLoading(false)
    appStore.updateAppSettings({ showChangePasswordModal: false })
    Modal.success({
      title: '密码已修改',
      content: () => h('div', { class: 'text-center' }, '请重新登录'),
      maskClosable: false,
      escToClose: false,
      cancelButtonProps: { type: 'outline' },
      onOk: () => {
        clearToken()
        userStore.logout()
        sideBarStore.clearMenuList()
        location.href = import.meta.env.VITE_BASE_PATH
      }
    })
  } catch (error) {
    setLoading(false)
  }
}
</script>

<template>
  <a-modal
    :visible="visible"
    title-align="start"
    title="修改密码"
    :cancel-button-props="{ type: 'outline' }"
    unmount-on-close
    :ok-loading="loading"
    :mask-closable="false"
    :esc-to-close="false"
    @ok="submit"
    @cancel="appStore.updateAppSettings({ showChangePasswordModal: false })">
    <form-com ref="changePasswordRef"></form-com>
  </a-modal>
</template>

<style lang="scss" scoped>
.block {
  margin-bottom: 24px;
  .title {
    margin: 10px 0;
    padding: 0;
    font-size: 14px;
  }
  .switch-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 32px;
  }
}
</style>

<script lang="ts" setup>
import MenuItem from './menuItem.vue'
import { useAppStore, useSideBarStore } from '@/store'
import { RouteRecordRaw, useRouter, useRoute } from 'vue-router'

const router = useRouter()
const appStore = useAppStore()
const sideBarStore = useSideBarStore()

const selectedKeys = computed(() => sideBarStore.getSelectedKeys)
const menuItems = computed(() => sideBarStore.getMenus)
const handleCollapse = (collapsed: boolean) => {
  appStore.updateAppSettings({ menuCollapse: collapsed })
}
</script>

<template>
  <!-- theme="dark"  -->
  <a-menu
    :style="{ width: '100%', height: '100%' }"
    :collapsed="appStore.appMenuCollapse"
    :show-collapse-button="true"
    accordion
    breakpoint="xl"
    auto-open-selected
    :selected-keys="selectedKeys"
    @collapse="handleCollapse">
    <menu-item :menuItems="menuItems"></menu-item>
  </a-menu>
</template>

<style lang="scss" scoped></style>

<script lang="ts" setup>
import type { RouteMeta, RouteRecordRaw } from 'vue-router'
import { regexUrl, openWindow } from '@/utils'
import { useRouter, useRoute } from 'vue-router'
defineProps<{
  menuItems?: RouteRecordRaw[]
}>()
const router = useRouter()
const route = useRoute()
const goto = (routeRecordRaw: RouteRecordRaw, e?: Event) => {
  // 阻止默认事件，防止页面刷新
  if (e && typeof e.preventDefault === 'function') {
    e.preventDefault()
  }
  
  // 打开外部链接
  if (regexUrl.test(routeRecordRaw.path)) {
    openWindow(routeRecordRaw.path)
    return
  }
  // // Eliminate external link side effects
  // const { hideInMenu, activeMenu } = routeRecordRaw.meta as RouteMeta
  // if (route.name === routeRecordRaw.name && !hideInMenu && !activeMenu) {
  //   return
  // }
  if (route.name === routeRecordRaw.name) return
  router.push({
    name: routeRecordRaw.name
  })
}
</script>

<template>
  <div v-for="menuItem in menuItems" :key="(menuItem.name as string)" class="menu-item">
    <template v-if="menuItem.children?.length">
      <a-sub-menu :key="(menuItem.name as string)">
        <template #icon>
          <icon v-if="menuItem.meta?.icon" :icon="menuItem.meta?.icon"></icon>
        </template>
        <template #title>{{ menuItem.meta?.title }}</template>
        <menu-item :menuItems="menuItem.children"></menu-item>
      </a-sub-menu>
    </template>
    <template v-else>
      <a-menu-item :key="(menuItem.name as string)" @click="(e) => goto(menuItem, e)">
        <template #icon>
          <icon v-if="menuItem.meta?.icon" :icon="menuItem.meta?.icon"></icon>
        </template>
        {{ menuItem.meta?.title }}
      </a-menu-item>
    </template>
  </div>
</template>

<style lang="scss" scoped>
// .menu-item {
//   :deep(.arco-menu-item) {
//     &.arco-menu-selected {
//       background-color: rgb(var(--primary-1));
//       position: relative;
//       &::after {
//         content: '';
//         position: absolute;
//         right: 0;
//         top: 0;
//         height: 100%;
//         width: 3px;
//         background-color: rgb(var(--primary-6));
//       }
//     }
//   }
//   :deep(.menu-item-child) {
//     padding-left: 22px !important;
//   }
// }
</style>

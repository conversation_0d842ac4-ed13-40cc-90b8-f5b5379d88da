import{i as e}from"./ResizeObserver.es-CzGuHLZU.js";import{a2 as s,a3 as a}from"./index-D-8JbLQk.js";import{d as r,r as l,c as t,w as o,o as n,G as u,Q as i}from"./vue-D-10XvVk.js";var v=r({name:"ResizeObserver",emits:["resize"],setup(r,{emit:v,slots:m}){let c;const d=l(),f=t((()=>s(d.value)?d.value.$el:d.value)),p=s=>{s&&(c=new e((e=>{const s=e[0];v("resize",s)})),c.observe(s))},z=()=>{c&&(c.disconnect(),c=null)};return o(f,(e=>{c&&z(),e&&p(e)})),n((()=>{f.value&&p(f.value)})),u((()=>{z()})),()=>{var e,s;const r=a(null!=(s=null==(e=m.default)?void 0:e.call(m))?s:[]);return r?i(r,{ref:d},!0):null}}});export{v as R};

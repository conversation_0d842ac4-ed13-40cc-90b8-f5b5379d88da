import"./index-DOhy6BH_.js";import"./index-DDFSMqsG.js";import{T as e}from"./index-Cuq5XRs0.js";import"./index-DD6vSYIM.js";import"./index-DQjhgQFu.js";import{T as a,a as l,P as t}from"./index-DdMaxvYa.js";import{B as i,S as o}from"./index-DGtjsHgS.js";import{C as r}from"./index-CdWxsKz_.js";import{C as s,R as n}from"./index-BEo1tUsK.js";import{F as d,a as c}from"./index-DVDXfQhn.js";import{R as u}from"./index-dpn1_5z1.js";import{u as p}from"./useLoading-D5mh7tTu.js";import{u as m}from"./usePagination-Dd_EW2BO.js";import{O as g}from"./index-D-8JbLQk.js";import{d as f}from"./dayjs.min-Daes5FZc.js";import{B as h,r as v,c as w,d as _,o as x,k as j,q as y,A as b,z,f as C,j as S,u as T,I as k,y as P,p as B,J as I}from"./vue-D-10XvVk.js";import"./pick-Ccd8Sfcm.js";import"./ResizeObserver.es-CzGuHLZU.js";import"./resize-observer-Dtogi-DJ.js";import"./index-DfEXMvnc.js";import"./use-children-components-v8i8lsOx.js";import"./render-function-CAXdZVZM.js";const O=()=>{const{loading:e,setLoading:a}=p(),{pagination:l}=m(),t=h({createTime:void 0}),i=v(void 0),o=w((()=>i.value?n.value.find((e=>e.id===i.value)):null)),r=v([]),s=w((()=>r.value.length?n.value.filter((e=>r.value.includes(e.id))):[])),n=v([]),d=v("0.00"),c=v("0.00"),u=async()=>{a(!0),i.value=void 0,r.value=[];try{const{data:e}=await(e=>g({url:"/admin/platform/platformBalanceFlow/list",method:"post",data:e}))({...t,pageNum:l.current,pageSize:l.pageSize});n.value=e.rows.map((e=>(e.createTime=f(e.createTime).format("YYYY-MM-DD HH:mm:ss"),e))),l.total=e.total,d.value=e.balance,c.value=e.availableBalance,a(!1)}catch(e){n.value=[],l.total=0,a(!1)}};return{loading:e,queryParams:t,pagination:l,rows:n,selectedId:i,selectedRow:o,selectedIds:r,selectedRows:s,selectAll:e=>{r.value=e?n.value.map((e=>e.id)):[]},rowSelect:(e,a,l)=>{r.value.includes(l.id)?r.value.splice(r.value.indexOf(l.id),1):r.value.push(l.id)},rowClick:e=>{r.value.includes(e.id)?r.value.splice(r.value.indexOf(e.id),1):r.value.push(e.id)},query:u,reset:()=>{l.current=1,Object.assign(t,{createTime:void 0}),u()},pageChange:async e=>{l.current=e,u()},pageSizeChange:async e=>{l.current=1,l.pageSize=e,u()},balance:d,availableBalance:c}},q={class:"page-container"},R={class:"h-full flex flex-col gap-[18px]"},$={class:"font-semibold"},Y=_({__name:"index",setup(p){const{loading:m,queryParams:g,pagination:f,rows:h,selectedId:v,selectedIds:w,selectAll:_,rowSelect:Y,rowClick:A,query:V,reset:D,pageChange:F,pageSizeChange:H,balance:L,availableBalance:M}=O();return x((()=>{F(1)})),(p,v)=>{const w=u,_=d,x=s,O=C("icon-search"),Y=i,A=C("icon-refresh"),V=o,N=n,J=c,U=r,E=a,G=e,K=l,Q=t;return S(),j("div",q,[y("div",R,[b(U,{bordered:!1},{default:z((()=>[b(J,{model:T(g),"auto-label-width":""},{default:z((()=>[b(N,{gutter:16},{default:z((()=>[b(x,{span:6},{default:z((()=>[b(_,{"show-colon":"",label:"日期",field:"createTime"},{default:z((()=>[b(w,{modelValue:T(g).createTime,"onUpdate:modelValue":v[0]||(v[0]=e=>T(g).createTime=e),placeholder:[`${p.$selectPlaceholder}开始日期`,`${p.$selectPlaceholder}结束日期`]},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),b(x,{span:6},{default:z((()=>[b(_,{"hide-label":""},{default:z((()=>[b(V,{size:18},{default:z((()=>[b(Y,{type:"primary",onClick:v[1]||(v[1]=e=>T(F)(1))},{icon:z((()=>[b(O)])),default:z((()=>[v[3]||(v[3]=k(" 查询 "))])),_:1}),b(Y,{type:"outline",onClick:T(D)},{icon:z((()=>[b(A)])),default:z((()=>[v[4]||(v[4]=k(" 重置 "))])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),b(U,{bordered:!1,class:"flex-1 overflow-y-hidden","body-style":{height:"100%"}},{actions:z((()=>[T(f).total?(S(),P(Q,{key:0,current:T(f).current,"page-size":T(f).pageSize,"show-total":T(f).showTotal,"show-page-size":T(f).showPageSize,"page-size-options":T(f).pageSizeOptions,total:T(f).total,onChange:T(F),onPageSizeChange:T(H)},null,8,["current","page-size","show-total","show-page-size","page-size-options","total","onChange","onPageSizeChange"])):B("",!0)])),default:z((()=>[b(N,{class:"mb-[12px]"},{default:z((()=>[b(x,{span:24,class:"text-right"},{default:z((()=>[b(V,null,{default:z((()=>[y("span",$,"余额："+I(T(L))+" 元；可用余额："+I(T(M))+" 元",1),b(Y,{type:"primary",onClick:v[2]||(v[2]=e=>T(F)(1))},{icon:z((()=>[b(A)])),default:z((()=>[v[5]||(v[5]=k(" 刷新 "))])),_:1})])),_:1})])),_:1})])),_:1}),b(K,{size:"large","row-key":"id",loading:T(m),pagination:!1,data:T(h),bordered:{cell:!0},scroll:{y:"calc(100% - 96px)"}},{columns:z((()=>[b(E,{align:"center",title:"序号",width:80},{cell:z((({rowIndex:e})=>[k(I(e+1),1)])),_:1}),b(E,{align:"center",title:"类型",width:150},{cell:z((({record:e})=>[b(G,{color:1===e.type?"green":"orange"},{default:z((()=>[k(I(1===e.type?"分账收入":"支出"),1)])),_:2},1032,["color"])])),_:1}),b(E,{align:"center",title:"店铺",width:200,ellipsis:"",tooltip:""},{cell:z((({record:e})=>{var a;return[k(I((null==(a=e.store)?void 0:a.name)??"-"),1)]})),_:1}),b(E,{align:"center",title:"关联订单",width:200},{cell:z((({record:e})=>{var a;return[k(I((null==(a=e.order)?void 0:a.orderNo)??"-"),1)]})),_:1}),b(E,{align:"center",title:"金额",width:150},{cell:z((({record:e})=>[k(I(e.amount)+" 元",1)])),_:1}),b(E,{align:"center",title:"状态",width:150},{cell:z((({record:e})=>[k(I(1===e.state?"处理完成":"处理中"),1)])),_:1}),b(E,{align:"center",title:"余额",width:150},{cell:z((({record:e})=>[k(I(e.balance)+" 元",1)])),_:1}),b(E,{align:"center",title:"可用余额",width:150},{cell:z((({record:e})=>[k(I(e.availableBalance)+" 元",1)])),_:1}),b(E,{align:"center",title:"创建时间",width:180,"data-index":"createTime"})])),_:1},8,["loading","data"])])),_:1})])])}}});export{Y as default};

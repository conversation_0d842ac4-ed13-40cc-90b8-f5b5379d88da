const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/info-B2IvGELY.js","assets/index-DOhy6BH_.js","assets/index-D-8JbLQk.js","assets/vue-D-10XvVk.js","assets/index-DxPaQOvH.css","assets/index-DB09tZwb.css","assets/index-BEo1tUsK.js","assets/pick-Ccd8Sfcm.js","assets/index-6rnfXikd.css","assets/index-DVDXfQhn.js","assets/index-DGtjsHgS.js","assets/index-BJBnsrKF.css","assets/index-DDFSMqsG.js","assets/ResizeObserver.es-CzGuHLZU.js","assets/index-RZyF5P1Y.css","assets/index-8er2yjEK.css","assets/index-DkZuZOQi.js","assets/index-CdWxsKz_.js","assets/index-CX9L_GU1.css","assets/apiUpload-DpATemHF.js","assets/_plugin-vue_export-helper-BCo6x5W8.js","assets/index-BcWAySBs.css","assets/index-Cuq5XRs0.js","assets/resize-observer-Dtogi-DJ.js","assets/index-DD6vSYIM.js","assets/index-C0ni2jp2.css","assets/index-DQjhgQFu.js","assets/index-Db7LPRu1.css","assets/index-Dbgee0nK.css","assets/useCommon-BuUbRw8e.js","assets/apiCommon-DcubqwY_.js","assets/index-DdMaxvYa.js","assets/index-DfEXMvnc.js","assets/use-children-components-v8i8lsOx.js","assets/index-B5FzkxT_.css","assets/index-DmW4RN1x.js","assets/index-Bl_vBcmJ.css","assets/index-dpn1_5z1.js","assets/dayjs.min-Daes5FZc.js","assets/render-function-CAXdZVZM.js","assets/index-Cf9H8fsj.css","assets/useLoading-D5mh7tTu.js","assets/usePagination-Dd_EW2BO.js","assets/index-O7pr3qsq.js","assets/index-CJ6Fn8S6.css","assets/detail-D2Jf7rXv.js","assets/index-DIKBiUsz.js","assets/use-index-D_ozg7PK.js","assets/index-Cf1pvoHl.css"])))=>i.map(i=>d[i]);
import{O as e,N as t,m as a}from"./index-D-8JbLQk.js";import"./index-DOhy6BH_.js";import{I as o}from"./index-DDFSMqsG.js";import{S as l,T as i,L as r}from"./index-Cuq5XRs0.js";import"./index-DD6vSYIM.js";import"./index-DQjhgQFu.js";import{T as s,a as n,P as d}from"./index-DdMaxvYa.js";import{B as c,S as u}from"./index-DGtjsHgS.js";import{C as m}from"./index-CdWxsKz_.js";import{D as p}from"./index-DmW4RN1x.js";import{C as f,R as g}from"./index-BEo1tUsK.js";import{F as v,a as h}from"./index-DVDXfQhn.js";import{R as y}from"./index-dpn1_5z1.js";/* empty css              */import{B as w,r as _,c as C,d as b,K as I,o as x,k as S,A as j,z as k,u as z,e as T,q as P,N,f as O,j as V,I as $,y as R,p as B,J as Y,af as D,M,h as U}from"./vue-D-10XvVk.js";import{u as E}from"./useCommon-BuUbRw8e.js";import{u as q}from"./useLoading-D5mh7tTu.js";import{u as A}from"./usePagination-Dd_EW2BO.js";import{d as L}from"./dayjs.min-Daes5FZc.js";import{M as H}from"./index-O7pr3qsq.js";const F=()=>{const{loading:t,setLoading:a}=q(),{pagination:o}=A(),l=w({title:void 0,state:void 0,storeId:void 0,createTime:void 0}),i=_(void 0),r=C((()=>i.value?d.value.find((e=>e.id===i.value)):null)),s=_([]),n=C((()=>s.value.length?d.value.filter((e=>s.value.includes(e.id))):[])),d=_([]),c=async()=>{a(!0),i.value=void 0,s.value=[];try{const{data:t}=await(t=>e({url:"/admin/platform/announcement/list",method:"post",data:t}))({...l,pageNum:o.current,pageSize:o.pageSize});d.value=t.rows.map((e=>(e.createTime=L(e.createTime).format("YYYY-MM-DD HH:mm:ss"),e))),o.total=t.total,a(!1)}catch(t){d.value=[],o.total=0,a(!1)}},u=w({title:void 0,content:void 0,storeId:void 0,storeName:void 0,storeCategoryId:void 0,storeCategoryName:void 0,storeSubCategoryId:void 0,storeSubCategoryName:void 0,createBy:void 0,createTime:void 0});return{loading:t,queryParams:l,pagination:o,rows:d,selectedId:i,selectedRow:r,selectedIds:s,selectedRows:n,selectAll:e=>{s.value=e?d.value.map((e=>e.id)):[]},rowSelect:(e,t,a)=>{s.value.includes(a.id)?s.value.splice(s.value.indexOf(a.id),1):s.value.push(a.id)},rowClick:e=>{s.value.includes(e.id)?s.value.splice(s.value.indexOf(e.id),1):s.value.push(e.id)},query:c,reset:()=>{o.current=1,Object.assign(l,{title:void 0,state:void 0,storeId:void 0,createTime:void 0}),c()},pageChange:async e=>{o.current=e,c()},pageSizeChange:async e=>{o.current=1,o.pageSize=e,c()},form:u,add:async t=>{try{const{title:a,content:o,storeId:l,storeCategoryId:i,storeSubCategoryId:r}=t;await(t=>e({url:"/admin/platform/announcement/add",method:"put",data:t}))({title:a,content:o,storeId:l,storeCategoryId:i,storeSubCategoryId:r})}catch(a){throw a}},detail:async t=>{try{const{data:a}=await(t=>e({url:`/admin/platform/announcement/detail/${t}`,method:"get"}))(t),{title:o,content:l,storeId:i,storeName:r,storeCategoryId:s,storeCategoryName:n,storeSubCategoryId:d,storeSubCategoryName:c,createBy:m,createTime:p}=a;Object.assign(u,{title:o,content:l,storeId:i,storeName:r,storeCategoryId:s,storeCategoryName:n,storeSubCategoryId:d,storeSubCategoryName:c,createBy:m,createTime:L(p).format("YYYY-MM-DD HH:mm:ss")})}catch(a){throw a}},edit:async(t,a)=>{try{const{title:o,content:l,storeId:i,storeCategoryId:r,storeSubCategoryId:s}=a;await((t,a)=>e({url:`/admin/platform/announcement/edit/${t}`,method:"put",data:a}))(t,{title:o,content:l,storeId:i,storeCategoryId:r,storeSubCategoryId:s})}catch(o){throw o}},del:async t=>{try{await(t=>e({url:`/admin/platform/announcement/del/${t}`,method:"delete"}))(t)}catch(a){throw a}}}},J={class:"page-container"},K={class:"h-full flex flex-col gap-[18px]"},G=b({__name:"index",setup(e){const w=M((()=>a((()=>import("./info-B2IvGELY.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44])))),C=M((()=>a((()=>import("./detail-D2Jf7rXv.js")),__vite__mapDeps([45,1,2,3,4,5,46,6,7,8,47,48,12,10,11,13,14,22,23,24,25,17,18,26,27,28,31,32,33,34,35,36,9,15,37,38,39,40,29,30,41,42,43,44])))),{announcementStateOptions:b,storeOptions:q,initStoreOptions:A}=E(),{loading:L,queryParams:G,pagination:Q,rows:W,selectedId:X,selectedIds:Z,selectAll:ee,rowSelect:te,rowClick:ae,query:oe,reset:le,pageChange:ie,pageSizeChange:re,add:se,edit:ne,del:de}=F(),ce=I("addRef"),ue=_(!1),me=async e=>{var a,o;try{if(await(null==(o=null==(a=ce.value)?void 0:a.formRef)?void 0:o.validate()))throw new Error("校验失败");await se(N(ce.value.form)),t.success({title:"成功提示",content:"已添加公告",duration:1500}),e(!0),oe()}catch(l){e(!1)}},pe=I("editRef"),fe=_(!1),ge=async e=>{var a,o;try{if(await(null==(o=null==(a=pe.value)?void 0:a.formRef)?void 0:o.validate()))throw new Error("校验失败");await ne(X.value,N(pe.value.form)),t.success({title:"成功提示",content:"已修改公告",duration:1500}),e(!0),oe()}catch(l){e(!1)}},ve=_(!1);return x((()=>{A(),ie(1)})),(e,a)=>{const _=H,I=o,x=v,N=f,M=l,E=y,A=O("icon-search"),F=c,Z=O("icon-refresh"),ee=u,te=g,ae=h,se=m,ne=O("icon-plus"),he=s,ye=i,we=p,_e=r,Ce=n,be=d;return V(),S("div",J,[j(_,{visible:z(ue),"onUpdate:visible":a[0]||(a[0]=e=>T(ue)?ue.value=e:null),width:1e3,"title-align":"start",title:"添加公告","cancel-button-props":{type:"outline"},"unmount-on-close":"","mask-closable":!1,"esc-to-close":!1,"on-before-ok":me,onCancel:a[1]||(a[1]=e=>ue.value=!1)},{default:k((()=>[j(z(w),{ref_key:"addRef",ref:ce},null,512)])),_:1},8,["visible"]),j(_,{visible:z(fe),"onUpdate:visible":a[2]||(a[2]=e=>T(fe)?fe.value=e:null),width:1e3,"title-align":"start",title:"修改公告","cancel-button-props":{type:"outline"},"unmount-on-close":"","mask-closable":!1,"esc-to-close":!1,"on-before-ok":ge,onCancel:a[3]||(a[3]=e=>fe.value=!1)},{default:k((()=>[j(z(w),{ref_key:"editRef",ref:pe,id:z(X)},null,8,["id"])])),_:1},8,["visible"]),j(_,{visible:z(ve),"onUpdate:visible":a[4]||(a[4]=e=>T(ve)?ve.value=e:null),width:1e3,"title-align":"start",title:"公告详情","cancel-button-props":{type:"outline"},"unmount-on-close":"","mask-closable":!1,"esc-to-close":!1,onCancel:a[5]||(a[5]=e=>ve.value=!1),footer:!1},{default:k((()=>[j(z(C),{id:z(X)},null,8,["id"])])),_:1},8,["visible"]),P("div",K,[j(se,{bordered:!1},{default:k((()=>[j(ae,{model:z(G),"auto-label-width":""},{default:k((()=>[j(te,{gutter:16},{default:k((()=>[j(N,{span:6},{default:k((()=>[j(x,{"show-colon":"",label:"公告标题",field:"title"},{default:k((()=>[j(I,{modelValue:z(G).title,"onUpdate:modelValue":a[6]||(a[6]=e=>z(G).title=e),placeholder:`${e.$inputPlaceholder}公告标题`,"allow-clear":""},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),j(N,{span:6},{default:k((()=>[j(x,{"show-colon":"",label:"公告状态",field:"state"},{default:k((()=>[j(M,{modelValue:z(G).state,"onUpdate:modelValue":a[7]||(a[7]=e=>z(G).state=e),options:z(b),placeholder:`${e.$selectPlaceholder}公告状态`,"allow-clear":""},null,8,["modelValue","options","placeholder"])])),_:1})])),_:1}),j(N,{span:6},{default:k((()=>[j(x,{"show-colon":"",label:"店铺名称",field:"storeId"},{default:k((()=>[j(M,{modelValue:z(G).storeId,"onUpdate:modelValue":a[8]||(a[8]=e=>z(G).storeId=e),options:z(q),placeholder:`${e.$selectPlaceholder}店铺名称`,"allow-search":"","allow-clear":""},null,8,["modelValue","options","placeholder"])])),_:1})])),_:1}),j(N,{span:6},{default:k((()=>[j(x,{"show-colon":"",label:"创建时间",field:"createTime"},{default:k((()=>[j(E,{modelValue:z(G).createTime,"onUpdate:modelValue":a[9]||(a[9]=e=>z(G).createTime=e),placeholder:[`${e.$selectPlaceholder}开始日期`,`${e.$selectPlaceholder}结束日期`]},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),j(N,{span:24},{default:k((()=>[j(x,{"hide-label":""},{default:k((()=>[j(ee,{size:18},{default:k((()=>[j(F,{type:"primary",onClick:a[10]||(a[10]=e=>z(ie)(1))},{icon:k((()=>[j(A)])),default:k((()=>[a[12]||(a[12]=$(" 查询 "))])),_:1}),j(F,{type:"outline",onClick:z(le)},{icon:k((()=>[j(Z)])),default:k((()=>[a[13]||(a[13]=$(" 重置 "))])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),j(se,{bordered:!1,class:"flex-1 overflow-y-hidden","body-style":{height:"100%"}},{actions:k((()=>[z(Q).total?(V(),R(be,{key:0,current:z(Q).current,"page-size":z(Q).pageSize,"show-total":z(Q).showTotal,"show-page-size":z(Q).showPageSize,"page-size-options":z(Q).pageSizeOptions,total:z(Q).total,onChange:z(ie),onPageSizeChange:z(re)},null,8,["current","page-size","show-total","show-page-size","page-size-options","total","onChange","onPageSizeChange"])):B("",!0)])),default:k((()=>[j(te,{class:"mb-[12px]"},{default:k((()=>[j(N,{span:16},{default:k((()=>[j(ee,null,{default:k((()=>[j(F,{type:"primary",onClick:a[11]||(a[11]=()=>{X.value=void 0,ue.value=!0})},{icon:k((()=>[j(ne)])),default:k((()=>[a[14]||(a[14]=$(" 添加公告 "))])),_:1})])),_:1})])),_:1})])),_:1}),j(Ce,{size:"large","row-key":"id",loading:z(L),pagination:!1,data:z(W),bordered:{cell:!0},scroll:{y:"calc(100% - 96px)"}},{columns:k((()=>[j(he,{align:"center",title:"序号",width:80},{cell:k((({rowIndex:e})=>[$(Y(z(Q).pageSize*(z(Q).current-1)+e+1),1)])),_:1}),j(he,{align:"center",title:"公告标题",width:200,"data-index":"title"}),j(he,{align:"center",title:"店铺名称",width:200,ellipsis:"",tooltip:""},{cell:k((({record:e})=>[$(Y(e.storeCategoryName)+" - "+Y(e.storeSubCategoryName)+" - "+Y(e.storeName),1)])),_:1}),j(he,{align:"center",title:"状态",width:200},{cell:k((({record:e})=>{var t;return[j(ye,{color:(null==(t=z(b).find((t=>t.value===e.state)))?void 0:t.color)??""},{default:k((()=>{var t;return[$(Y((null==(t=z(b).find((t=>t.value===e.state)))?void 0:t.label)??""),1)]})),_:2},1032,["color"])]})),_:1}),j(he,{align:"center",title:"创建人",width:150,ellipsis:"",tooltip:"","data-index":"createBy"}),j(he,{align:"center",title:"创建时间",width:180,"data-index":"createTime"}),j(he,{align:"center",title:"操作",width:200,fixed:"right"},{cell:k((({record:e})=>[j(ee,null,{split:k((()=>[j(we,{direction:"vertical"})])),default:k((()=>[j(_e,{onClick:D((()=>{X.value=e.id,ve.value=!0}),["stop"])},{default:k((()=>a[15]||(a[15]=[$(" 查看 ")]))),_:2},1032,["onClick"]),j(_e,{status:"danger",onClick:D((()=>{(e=>{H.warning({title:"提示",content:()=>U("div",{class:"text-center"},`确定删除【${e.title}】？`),maskClosable:!1,escToClose:!1,hideCancel:!1,cancelButtonProps:{type:"outline"},onBeforeOk:async a=>{try{await de(e.id),t.success({title:"成功提示",content:"已删除公告",duration:1500}),a(!0),oe()}catch(o){a(!1)}}})})(e)}),["stop"])},{default:k((()=>a[16]||(a[16]=[$(" 删除 ")]))),_:2},1032,["onClick"])])),_:2},1024)])),_:1})])),_:1},8,["loading","data"])])),_:1})])])}}}),Q=Object.freeze(Object.defineProperty({__proto__:null,default:G},Symbol.toStringTag,{value:"Module"}));export{Q as i,F as u};

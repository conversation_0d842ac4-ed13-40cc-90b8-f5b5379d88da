const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/info-CPnL5oPc.js","assets/index-DOhy6BH_.js","assets/index-D-8JbLQk.js","assets/vue-D-10XvVk.js","assets/index-DxPaQOvH.css","assets/index-DB09tZwb.css","assets/index-BEo1tUsK.js","assets/pick-Ccd8Sfcm.js","assets/index-6rnfXikd.css","assets/index-DVDXfQhn.js","assets/index-DGtjsHgS.js","assets/index-BJBnsrKF.css","assets/index-DDFSMqsG.js","assets/ResizeObserver.es-CzGuHLZU.js","assets/index-RZyF5P1Y.css","assets/index-8er2yjEK.css","assets/index-DiBSSeoD.js","assets/resize-observer-Dtogi-DJ.js","assets/index-B4zgCFsq.css","assets/index-Cuq5XRs0.js","assets/index-DD6vSYIM.js","assets/index-C0ni2jp2.css","assets/index-CdWxsKz_.js","assets/index-CX9L_GU1.css","assets/index-DQjhgQFu.js","assets/index-Db7LPRu1.css","assets/index-Dbgee0nK.css","assets/useCommon-BuUbRw8e.js","assets/apiCommon-DcubqwY_.js","assets/index-DdMaxvYa.js","assets/index-DfEXMvnc.js","assets/use-children-components-v8i8lsOx.js","assets/index-B5FzkxT_.css","assets/index-DmW4RN1x.js","assets/index-Bl_vBcmJ.css","assets/useLoading-D5mh7tTu.js","assets/usePagination-Dd_EW2BO.js","assets/dayjs.min-Daes5FZc.js","assets/index-O7pr3qsq.js","assets/index-CJ6Fn8S6.css"])))=>i.map(i=>d[i]);
import{O as e,N as a,m as t}from"./index-D-8JbLQk.js";import"./index-DOhy6BH_.js";import{I as l}from"./index-DDFSMqsG.js";import{S as o,L as i}from"./index-Cuq5XRs0.js";import"./index-DD6vSYIM.js";import"./index-DQjhgQFu.js";import{T as n,a as r,P as s}from"./index-DdMaxvYa.js";import{B as d,S as c}from"./index-DGtjsHgS.js";import{C as u}from"./index-CdWxsKz_.js";import{D as m}from"./index-DmW4RN1x.js";import{C as p,R as f}from"./index-BEo1tUsK.js";import{F as v,a as h}from"./index-DVDXfQhn.js";/* empty css              */import{B as g,r as w,c as y,d as _,K as k,o as x,k as b,A as j,z as C,u as z,e as S,q as I,N as P,f as O,j as R,I as T,y as $,p as V,J as q,af as E,M,h as A}from"./vue-D-10XvVk.js";import{u as B}from"./useCommon-BuUbRw8e.js";import{u as D}from"./useLoading-D5mh7tTu.js";import{u as L}from"./usePagination-Dd_EW2BO.js";import{d as N}from"./dayjs.min-Daes5FZc.js";import{M as U}from"./index-O7pr3qsq.js";const Y=()=>{const{loading:a,setLoading:t}=D(),{pagination:l}=L(),o=g({nickname:void 0,roleId:void 0}),i=w(void 0),n=y((()=>i.value?d.value.find((e=>e.id===i.value)):null)),r=w([]),s=y((()=>r.value.length?d.value.filter((e=>r.value.includes(e.id))):[])),d=w([]),c=async()=>{t(!0),i.value=void 0,r.value=[];try{const{data:a}=await(a=>e({url:"/admin/platform/account/list",method:"post",data:a}))({...o,pageNum:l.current,pageSize:l.pageSize});d.value=a.rows.map((e=>(e.createTime=N(e.createTime).format("YYYY-MM-DD HH:mm:ss"),e))),l.total=a.total,t(!1)}catch(a){d.value=[],l.total=0,t(!1)}},u=g({roleId:void 0,username:void 0,nickname:void 0,password:void 0,confirmPassword:void 0,remark:void 0});return{loading:a,queryParams:o,pagination:l,rows:d,selectedId:i,selectedRow:n,selectedIds:r,selectedRows:s,selectAll:e=>{r.value=e?d.value.map((e=>e.id)):[]},rowSelect:(e,a,t)=>{r.value.includes(t.id)?r.value.splice(r.value.indexOf(t.id),1):r.value.push(t.id)},rowClick:e=>{r.value.includes(e.id)?r.value.splice(r.value.indexOf(e.id),1):r.value.push(e.id)},query:c,reset:()=>{l.current=1,Object.assign(o,{nickname:void 0,roleId:void 0}),c()},pageChange:async e=>{l.current=e,c()},pageSizeChange:async e=>{l.current=1,l.pageSize=e,c()},form:u,add:async a=>{try{await(a=>e({url:"/admin/platform/account/add",method:"put",data:a}))(a)}catch(t){throw t}},detail:async a=>{try{const{data:t}=await(a=>e({url:`/admin/platform/account/detail/${a}`,method:"get"}))(a),{role:l,username:o,nickname:i,remark:n}=t;Object.assign(u,{roleId:l.id,username:o,nickname:i,remark:n})}catch(t){throw t}},edit:async(a,t)=>{try{await((a,t)=>e({url:`/admin/platform/account/edit/${a}`,method:"put",data:t}))(a,t)}catch(l){throw l}},del:async a=>{try{await(a=>e({url:`/admin/platform/account/del/${a}`,method:"delete"}))(a)}catch(t){throw t}}}},H={class:"page-container"},F={class:"h-full flex flex-col gap-[18px]"},J=_({__name:"index",setup(e){const g=M((()=>t((()=>import("./info-CPnL5oPc.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39])))),{roleOptions:y,initRoleOptions:_}=B(),{loading:D,queryParams:L,pagination:N,rows:J,selectedId:K,selectedIds:G,selectAll:Q,rowSelect:W,rowClick:X,query:Z,reset:ee,pageChange:ae,pageSizeChange:te,add:le,edit:oe,del:ie}=Y(),ne=k("addRef"),re=w(!1),se=async e=>{var t,l;try{if(await(null==(l=null==(t=ne.value)?void 0:t.formRef)?void 0:l.validate()))throw new Error("校验失败");if(ne.value.form.password!==ne.value.form.confirmPassword)throw a.error({title:"错误提示",content:"两次密码输入不一致",duration:1500}),new Error("校验失败");await le(P(ne.value.form)),a.success({title:"成功提示",content:"已添加用户",duration:1500}),e(!0),Z()}catch(o){e(!1)}},de=k("editRef"),ce=w(!1),ue=async e=>{var t,l;try{if(await(null==(l=null==(t=de.value)?void 0:t.formRef)?void 0:l.validate()))throw new Error("校验失败");await oe(K.value,P(de.value.form)),a.success({title:"成功提示",content:"已修改用户",duration:1500}),e(!0),Z()}catch(o){e(!1)}};return x((()=>{_(),ae(1)})),(e,t)=>{const w=U,_=l,k=v,x=p,P=o,M=O("icon-search"),B=d,Y=O("icon-refresh"),G=c,Q=f,W=h,X=u,le=O("icon-plus"),oe=n,me=m,pe=i,fe=r,ve=s;return R(),b("div",H,[j(w,{visible:z(re),"onUpdate:visible":t[0]||(t[0]=e=>S(re)?re.value=e:null),width:600,"title-align":"start",title:"新增用户","cancel-button-props":{type:"outline"},"unmount-on-close":"","mask-closable":!1,"esc-to-close":!1,"on-before-ok":se,onCancel:t[1]||(t[1]=e=>re.value=!1)},{default:C((()=>[j(z(g),{ref_key:"addRef",ref:ne},null,512)])),_:1},8,["visible"]),j(w,{visible:z(ce),"onUpdate:visible":t[2]||(t[2]=e=>S(ce)?ce.value=e:null),width:600,"title-align":"start",title:"修改用户","cancel-button-props":{type:"outline"},"unmount-on-close":"","mask-closable":!1,"esc-to-close":!1,"on-before-ok":ue,onCancel:t[3]||(t[3]=e=>ce.value=!1)},{default:C((()=>[j(z(g),{ref_key:"editRef",ref:de,id:z(K)},null,8,["id"])])),_:1},8,["visible"]),I("div",F,[j(X,{bordered:!1},{default:C((()=>[j(W,{model:z(L),"auto-label-width":""},{default:C((()=>[j(Q,{gutter:16},{default:C((()=>[j(x,{span:6},{default:C((()=>[j(k,{"show-colon":"",label:"用户名称",field:"nickname"},{default:C((()=>[j(_,{modelValue:z(L).nickname,"onUpdate:modelValue":t[4]||(t[4]=e=>z(L).nickname=e),placeholder:`${e.$inputPlaceholder}用户名称`,"allow-clear":""},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),j(x,{span:6},{default:C((()=>[j(k,{"show-colon":"",label:"所属角色",field:"roleId"},{default:C((()=>[j(P,{modelValue:z(L).roleId,"onUpdate:modelValue":t[5]||(t[5]=e=>z(L).roleId=e),options:z(y),placeholder:`${e.$selectPlaceholder}所属角色`,"allow-clear":""},null,8,["modelValue","options","placeholder"])])),_:1})])),_:1}),j(x,{span:12},{default:C((()=>[j(k,{"hide-label":""},{default:C((()=>[j(G,{size:18},{default:C((()=>[j(B,{type:"primary",onClick:t[6]||(t[6]=e=>z(ae)(1))},{icon:C((()=>[j(M)])),default:C((()=>[t[8]||(t[8]=T(" 查询 "))])),_:1}),j(B,{type:"outline",onClick:z(ee)},{icon:C((()=>[j(Y)])),default:C((()=>[t[9]||(t[9]=T(" 重置 "))])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),j(X,{bordered:!1,class:"flex-1 overflow-y-hidden","body-style":{height:"100%"}},{actions:C((()=>[z(N).total?(R(),$(ve,{key:0,current:z(N).current,"page-size":z(N).pageSize,"show-total":z(N).showTotal,"show-page-size":z(N).showPageSize,"page-size-options":z(N).pageSizeOptions,total:z(N).total,onChange:z(ae),onPageSizeChange:z(te)},null,8,["current","page-size","show-total","show-page-size","page-size-options","total","onChange","onPageSizeChange"])):V("",!0)])),default:C((()=>[j(Q,{class:"mb-[12px]"},{default:C((()=>[j(x,{span:16},{default:C((()=>[j(B,{type:"primary",onClick:t[7]||(t[7]=()=>{K.value=void 0,re.value=!0})},{icon:C((()=>[j(le)])),default:C((()=>[t[10]||(t[10]=T(" 添加用户 "))])),_:1})])),_:1})])),_:1}),j(fe,{size:"large","row-key":"id",loading:z(D),pagination:!1,data:z(J),bordered:{cell:!0},scroll:{y:"calc(100% - 96px)"}},{columns:C((()=>[j(oe,{align:"center",title:"序号",width:80},{cell:C((({rowIndex:e})=>[T(q(z(N).pageSize*(z(N).current-1)+e+1),1)])),_:1}),j(oe,{align:"center",title:"用户名称",width:200,ellipsis:"",tooltip:"","data-index":"nickname"}),j(oe,{align:"center",title:"所属角色",width:200,ellipsis:"",tooltip:""},{cell:C((({record:e})=>{var a;return[T(q(null==(a=e.role)?void 0:a.roleName),1)]})),_:1}),j(oe,{align:"center",title:"登录账号",width:200,ellipsis:"",tooltip:"","data-index":"username"}),j(oe,{align:"center",title:"备注",width:200,ellipsis:"",tooltip:"","data-index":"remark"}),j(oe,{align:"center",title:"创建时间",width:180,"data-index":"createTime"}),j(oe,{align:"center",title:"操作",width:200,fixed:"right"},{cell:C((({record:e})=>[j(G,null,{split:C((()=>[j(me,{direction:"vertical"})])),default:C((()=>[j(pe,{onClick:E((()=>{K.value=e.id,ce.value=!0}),["stop"])},{default:C((()=>t[11]||(t[11]=[T(" 编辑 ")]))),_:2},1032,["onClick"]),j(pe,{status:"danger",onClick:E((t=>(e=>{U.warning({title:"提示",content:()=>A("div",{class:"text-center"},`确定删除【${e.username}】？`),maskClosable:!1,escToClose:!1,hideCancel:!1,cancelButtonProps:{type:"outline"},onBeforeOk:async t=>{try{await ie(e.id),a.success({title:"成功提示",content:"已删除用户",duration:1500}),t(!0),Z()}catch(l){t(!1)}}})})(e)),["stop"])},{default:C((()=>t[12]||(t[12]=[T("删除")]))),_:2},1032,["onClick"])])),_:2},1024)])),_:1})])),_:1},8,["loading","data"])])),_:1})])])}}}),K=Object.freeze(Object.defineProperty({__proto__:null,default:J},Symbol.toStringTag,{value:"Module"}));export{K as i,Y as u};

import{_ as e,ad as a,B as t,g as r,k as l,l as o,s,f as n}from"./index-D-8JbLQk.js";import{u as i}from"./use-index-D_ozg7PK.js";import{R as u}from"./index-DDFSMqsG.js";import{d as c,t as d,i as p,r as g,c as v,o as m,w as f,f as y,j as x,k as h,A as I,z,q as b,l as S,F as C,m as $,p as j,O as k,n as F,a3 as E,B as L,I as O}from"./vue-D-10XvVk.js";import{P as R}from"./index-CHOaln3D.js";const w=Symbol("ArcoAvatarGroup");var A=Object.defineProperty,T=Object.getOwnPropertySymbols,P=Object.prototype.hasOwnProperty,B=Object.prototype.propertyIsEnumerable,U=(e,a,t)=>a in e?A(e,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[a]=t,G=(e,a)=>{for(var t in a||(a={}))P.call(a,t)&&U(e,t,a[t]);if(T)for(var t of T(a))B.call(a,t)&&U(e,t,a[t]);return e};const N=c({name:"Avatar",components:{ResizeObserver:u,IconImageClose:a,IconLoading:t},props:{shape:{type:String,default:"circle"},imageUrl:String,size:Number,autoFixFontSize:{type:Boolean,default:!0},triggerType:{type:String,default:"button"},triggerIconStyle:{type:Object},objectFit:{type:String}},emits:{click:e=>!0,error:()=>!0,load:()=>!0},setup(e,{slots:a,emit:t,attrs:o}){const{shape:s,size:n,autoFixFontSize:u,triggerType:c,triggerIconStyle:y}=d(e),x=r("avatar"),h=p(w,void 0),I=g(),z=g(),b=v((()=>{var e;return null!=(e=null==h?void 0:h.shape)?e:s.value})),S=v((()=>{var e;return null!=(e=null==h?void 0:h.size)?e:n.value})),C=v((()=>{var e;return null!=(e=null==h?void 0:h.autoFixFontSize)?e:u.value})),$=g(!1),j=g(!1),k=g(!0),E=g(!1),L=h?i({itemRef:I,selector:`.${x}`}).computedIndex:g(-1),O=v((()=>{var e;const a=l(S.value)?{width:`${S.value}px`,height:`${S.value}px`,fontSize:S.value/2+"px"}:{};return h&&(a.zIndex=h.zIndexAscend?L.value+1:h.total-L.value,a.marginLeft=0!==L.value?`-${(null!=(e=S.value)?e:40)/4}px`:"0"),a})),R=W({triggerIconStyle:null==y?void 0:y.value,inlineStyle:o.style,triggerType:c.value}),A=()=>{$.value||e.imageUrl||F((()=>{var e;if(!z.value||!I.value)return;const a=z.value.clientWidth,t=null!=(e=S.value)?e:I.value.offsetWidth,r=t/(a+8);t&&r<1&&(z.value.style.transform=`scale(${r}) translateX(-50%)`),k.value=!0}))};m((()=>{var e;(null==(e=z.value)?void 0:e.firstElementChild)&&["IMG","PICTURE"].includes(z.value.firstElementChild.tagName)&&($.value=!0),C.value&&A()})),f(n,(()=>{C.value&&A()}));const T=v((()=>[x,`${x}-${b.value}`])),P=v((()=>$.value||e.imageUrl?`${x}-image`:`${x}-text`));return{prefixCls:x,itemRef:I,cls:T,outerStyle:O,wrapperRef:z,wrapperCls:P,computedTriggerIconStyle:R,isImage:$,shouldLoad:k,isLoaded:E,hasError:j,onClick:e=>{t("click",e)},handleResize:()=>{C.value&&A()},handleImgLoad:()=>{E.value=!0,t("load")},handleImgError:()=>{j.value=!0,t("error")}}}}),W=({triggerType:e,inlineStyle:a={},triggerIconStyle:t={}})=>{let r={};return"button"===e&&(!t||t&&!t.color)&&a&&a.backgroundColor&&(r={color:a.backgroundColor}),G(G({},t),r)},_=["src"];var q=e(N,[["render",function(e,a,t,r,l,o){const s=y("IconImageClose"),n=y("IconLoading"),i=y("resize-observer");return x(),h("div",{ref:"itemRef",style:k(e.outerStyle),class:S([e.cls,{[`${e.prefixCls}-with-trigger-icon`]:Boolean(e.$slots["trigger-icon"])}]),onClick:a[2]||(a[2]=(...a)=>e.onClick&&e.onClick(...a))},[I(i,{onResize:e.handleResize},{default:z((()=>[b("span",{ref:"wrapperRef",class:S(e.wrapperCls)},[e.imageUrl?(x(),h(C,{key:0},[e.hasError?$(e.$slots,"error",{key:0},(()=>[b("div",{class:S(`${e.prefixCls}-image-icon`)},[I(s)],2)])):j("v-if",!0),e.hasError||!e.shouldLoad||e.isLoaded?j("v-if",!0):$(e.$slots,"default",{key:1},(()=>[b("div",{class:S(`${e.prefixCls}-image-icon`)},[I(n)],2)])),!e.hasError&&e.shouldLoad?(x(),h("img",{key:2,src:e.imageUrl,style:k({width:e.size+"px",height:e.size+"px",objectFit:e.objectFit}),alt:"avatar",onLoad:a[0]||(a[0]=(...a)=>e.handleImgLoad&&e.handleImgLoad(...a)),onError:a[1]||(a[1]=(...a)=>e.handleImgError&&e.handleImgError(...a))},null,44,_)):j("v-if",!0)],64)):$(e.$slots,"default",{key:1})],2)])),_:3},8,["onResize"]),e.$slots["trigger-icon"]?(x(),h("div",{key:0,class:S(`${e.prefixCls}-trigger-icon-${e.triggerType}`),style:k(e.computedTriggerIconStyle)},[$(e.$slots,"trigger-icon")],6)):j("v-if",!0)],6)}]]);const M=c({name:"AvatarGroup",props:{shape:{type:String,default:"circle"},size:Number,autoFixFontSize:{type:Boolean,default:!0},maxCount:{type:Number,default:0},zIndexAscend:{type:Boolean,default:!1},maxStyle:{type:Object},maxPopoverTriggerProps:{type:Object}},setup(e,{slots:a}){const{shape:t,size:l,autoFixFontSize:s,zIndexAscend:n}=d(e),i=r("avatar-group"),u=g(0);return E(w,L({shape:t,size:l,autoFixFontSize:s,zIndexAscend:n,total:u})),()=>{var t,r;const l=o(null!=(r=null==(t=a.default)?void 0:t.call(a))?r:[]),s=e.maxCount>0?l.slice(0,e.maxCount):l,n=e.maxCount>0?l.slice(e.maxCount):[];return u.value!==l.length&&(u.value=l.length),I("div",{class:i},[s,n.length>0&&I(R,e.maxPopoverTriggerProps,{default:()=>[I(q,{class:`${i}-max-count-avatar`,style:e.maxStyle},{default:()=>[O("+"),n.length]})],content:()=>I("div",null,[n])})])}}}),X=Object.assign(q,{Group:M,install:(e,a)=>{s(e,a);const t=n(a);e.component(t+q.name,q),e.component(t+M.name,M)}});export{X as A};

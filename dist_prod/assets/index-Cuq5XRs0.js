import{a2 as e,e as l,_ as a,g as t,j as o,h as n,k as u,a5 as r,c as i,ay as s,a3 as d,ao as p,I as v,a as c,B as f,s as m,f as b,i as y,C as g,ae as h,ap as O,r as S,az as C,aA as w}from"./index-D-8JbLQk.js";import{j as x,K as V,E as $,o as k,F as B,B as I,T as j}from"./index-DDFSMqsG.js";import{p as P}from"./pick-Ccd8Sfcm.js";import{r as z,o as R,w as N,c as E,d as _,t as F,i as K,f as A,j as T,k as L,l as M,m as D,p as H,y as G,a4 as q,Z as W,a7 as J,z as Q,q as U,v as Z,S as X,B as Y,b as ee,I as le,J as ae,g as te,n as oe,a3 as ne,Q as ue,F as re,L as ie,O as se,A as de,af as pe,T as ve,a as ce,H as fe,a5 as me}from"./vue-D-10XvVk.js";import{u as be}from"./index-DGtjsHgS.js";import{u as ye}from"./index-DOhy6BH_.js";import{R as ge}from"./resize-observer-Dtogi-DJ.js";import{S as he,E as Oe,u as Se}from"./index-DD6vSYIM.js";import{S as Ce}from"./index-CdWxsKz_.js";import{C as we}from"./index-DQjhgQFu.js";const xe=l=>{const a=z(),t=()=>e(a.value)?a.value.$refs[l]:a.value,o=z();return R((()=>{o.value=t()})),N([a],(()=>{o.value=t()})),{componentRef:a,elementRef:o}};var Ve=Object.defineProperty,$e=Object.getOwnPropertySymbols,ke=Object.prototype.hasOwnProperty,Be=Object.prototype.propertyIsEnumerable,Ie=(e,l,a)=>l in e?Ve(e,l,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[l]=a;const je=e=>({displayScrollbar:E((()=>Boolean(e.value))),scrollbarProps:E((()=>{if(e.value)return((e,l)=>{for(var a in l||(l={}))ke.call(l,a)&&Ie(e,a,l[a]);if($e)for(var a of $e(l))Be.call(l,a)&&Ie(e,a,l[a]);return e})({type:"embed"},l(e.value)?void 0:e.value)}))});var Pe=a(_({name:"SelectDropdown",components:{ScrollbarComponent:he,Empty:Oe,Spin:Ce},props:{loading:Boolean,empty:Boolean,virtualList:Boolean,bottomOffset:{type:Number,default:0},scrollbar:{type:[Boolean,Object],default:!0},onScroll:{type:[Function,Array]},onReachBottom:{type:[Function,Array]},showHeaderOnEmpty:{type:Boolean,default:!1},showFooterOnEmpty:{type:Boolean,default:!1}},emits:["scroll","reachBottom"],setup(e,{emit:l,slots:a}){var n,u,r;const{scrollbar:i}=F(e),s=t("select-dropdown"),d=K(o,void 0),p=null==(r=null==(u=null==d?void 0:(n=d.slots).empty)?void 0:u.call(n,{component:"select"}))?void 0:r[0],{componentRef:v,elementRef:c}=xe("containerRef"),{displayScrollbar:f,scrollbarProps:m}=je(i),b=E((()=>[s,{[`${s}-has-header`]:Boolean(a.header),[`${s}-has-footer`]:Boolean(a.footer)}]));return{prefixCls:s,SelectEmpty:p,cls:b,wrapperRef:c,wrapperComRef:v,handleScroll:a=>{const{scrollTop:t,scrollHeight:o,offsetHeight:n}=a.target;o-(t+n)<=e.bottomOffset&&l("reachBottom",a),l("scroll",a)},displayScrollbar:f,scrollbarProps:m}}}),[["render",function(e,l,a,t,o,n){const u=A("spin");return T(),L("div",{class:M(e.cls)},[!e.$slots.header||e.empty&&!e.showHeaderOnEmpty?H("v-if",!0):(T(),L("div",{key:0,class:M(`${e.prefixCls}-header`)},[D(e.$slots,"header")],2)),e.loading?(T(),G(u,{key:1,class:M(`${e.prefixCls}-loading`)},null,8,["class"])):e.empty?(T(),L("div",{key:2,class:M(`${e.prefixCls}-empty`)},[D(e.$slots,"empty",{},(()=>[(T(),G(q(e.SelectEmpty?e.SelectEmpty:"Empty")))]))],2)):H("v-if",!0),!e.virtualList||e.loading||e.empty?H("v-if",!0):D(e.$slots,"virtual-list",{key:3}),e.virtualList?H("v-if",!0):W((T(),G(q(e.displayScrollbar?"ScrollbarComponent":"div"),Z({key:4,ref:"wrapperComRef",class:`${e.prefixCls}-list-wrapper`},e.scrollbarProps,{onScroll:e.handleScroll}),{default:Q((()=>[U("ul",{class:M(`${e.prefixCls}-list`)},[D(e.$slots,"default")],2)])),_:3},16,["class","onScroll"])),[[J,!e.loading&&!e.empty]]),!e.$slots.footer||e.empty&&!e.showFooterOnEmpty?H("v-if",!0):(T(),L("div",{key:5,class:M(`${e.prefixCls}-footer`)},[D(e.$slots,"footer")],2))],2)}]]);const ze=Symbol("ArcoSelectContext");var Re=Object.defineProperty,Ne=Object.defineProperties,Ee=Object.getOwnPropertyDescriptors,_e=Object.getOwnPropertySymbols,Fe=Object.prototype.hasOwnProperty,Ke=Object.prototype.propertyIsEnumerable,Ae=(e,l,a)=>l in e?Re(e,l,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[l]=a,Te=(e,l)=>{for(var a in l||(l={}))Fe.call(l,a)&&Ae(e,a,l[a]);if(_e)for(var a of _e(l))Ke.call(l,a)&&Ae(e,a,l[a]);return e},Le=(e,l)=>Ne(e,Ee(l));const Me=e=>n(e)&&"isGroup"in e,De=(e,l="value")=>String(n(e)?e[l]:e),He=(e,a="value")=>n(e)?`__arco__option__object__${e[a]}`:e||u(e)||r(e)||l(e)?`__arco__option__${typeof e}-${e}`:"",Ge=(e,{valueKey:l,fieldNames:a,origin:t,index:o=-1})=>{var u;if(n(e)){const n=e[a.value];return{raw:e,index:o,key:He(n,l),origin:t,value:n,label:null!=(u=e[a.label])?u:De(n,l),render:e[a.render],disabled:Boolean(e[a.disabled]),tagProps:e[a.tagProps]}}const r={value:e,label:String(e),disabled:!1};return Te({raw:r,index:o,key:He(e,l),origin:t},r)},qe=(e,{valueKey:l,fieldNames:a,origin:t,optionInfoMap:o})=>{var u;const r=[];for(const s of e)if(n(i=s)&&"isGroup"in i){const e=qe(null!=(u=s.options)?u:[],{valueKey:l,fieldNames:a,origin:t,optionInfoMap:o});e.length>0&&r.push(Le(Te({},s),{key:`__arco__group__${s.label}`,options:e}))}else{const e=Ge(s,{valueKey:l,fieldNames:a,origin:t});r.push(e),o.get(e.key)||o.set(e.key,e)}var i;return r},We=(e,{inputValue:l,filterOption:a})=>{const t=e=>{var o;const n=[];for(const u of e)if(Me(u)){const e=t(null!=(o=u.options)?o:[]);e.length>0&&n.push(Le(Te({},u),{options:e}))}else Je(u,{inputValue:l,filterOption:a})&&n.push(u);return n};return t(e)},Je=(e,{inputValue:l,filterOption:a})=>i(a)?!l||a(l,e.raw):!a||e.label.toLowerCase().includes((null!=l?l:"").toLowerCase()),Qe=(e,l)=>{const a=Object.prototype.toString.call(e);return a===Object.prototype.toString.call(l)&&("[object Object]"===a?((e,l)=>{if(!e||!l)return!1;if(e.length!==l.length)return!1;for(const a of Object.keys(e))if(!Qe(e[a],l[a]))return!1;return!0})(e,l):"[object Array]"===a?((e,l)=>{if(!e||!l)return!1;const{length:a}=e;if(a!==l.length)return!1;for(let t=0;t<a;t++)if(!Qe(e[t],l[t]))return!1;return!0})(e,l):"[object Function]"===a?e===l||e.toString()===l.toString():e===l)};var Ue=a(_({name:"Option",components:{Checkbox:we},props:{value:{type:[String,Number,Boolean,Object],default:void 0},label:String,disabled:Boolean,tagProps:{type:Object},extra:{type:Object},index:{type:Number},internal:Boolean},setup(e){const{disabled:l,tagProps:a,index:o}=F(e),n=t("select-option"),u=K(ze,void 0),r=te(),i=z(),s=z(a.value);N(a,((e,l)=>{Qe(e,l)||(s.value=e)}));const d=z(""),p=E((()=>{var l,a;return null!=(a=null!=(l=e.value)?l:e.label)?a:d.value})),v=E((()=>{var l;return null!=(l=e.label)?l:d.value})),c=E((()=>He(p.value,null==u?void 0:u.valueKey))),f=E((()=>{var e;return null!=(e=null==u?void 0:u.component)?e:"li"})),m=()=>{var l;if(!e.label&&i.value){const e=null!=(l=i.value.textContent)?l:"";d.value!==e&&(d.value=e)}};R((()=>m())),X((()=>m()));const b=E((()=>{var e;return null!=(e=null==u?void 0:u.valueKeys.includes(c.value))&&e})),y=E((()=>(null==u?void 0:u.activeKey)===c.value));let g=z(!0);if(!e.internal){const e=Y({raw:{value:p,label:v,disabled:l,tagProps:s},ref:i,index:o,key:c,origin:"slot",value:p,label:v,disabled:l,tagProps:s});g=E((()=>Je(e,{inputValue:null==u?void 0:u.inputValue,filterOption:null==u?void 0:u.filterOption}))),r&&(null==u||u.addSlotOptionInfo(r.uid,e)),ee((()=>{r&&(null==u||u.removeSlotOptionInfo(r.uid))}))}const h=E((()=>[n,{[`${n}-disabled`]:e.disabled,[`${n}-selected`]:b.value,[`${n}-active`]:y.value,[`${n}-multiple`]:null==u?void 0:u.multiple}]));return{prefixCls:n,cls:h,selectCtx:u,itemRef:i,component:f,isSelected:b,isValid:g,handleClick:l=>{e.disabled||null==u||u.onSelect(c.value,l)},handleMouseEnter:()=>{e.disabled||null==u||u.setActiveKey(c.value)},handleMouseLeave:()=>{e.disabled||null==u||u.setActiveKey()}}}}),[["render",function(e,l,a,t,o,n){const u=A("checkbox");return W((T(),G(q(e.component),{ref:"itemRef",class:M([e.cls,{[`${e.prefixCls}-has-suffix`]:Boolean(e.$slots.suffix)}]),onClick:e.handleClick,onMouseenter:e.handleMouseEnter,onMouseleave:e.handleMouseLeave},{default:Q((()=>[e.$slots.icon?(T(),L("span",{key:0,class:M(`${e.prefixCls}-icon`)},[D(e.$slots,"icon")],2)):H("v-if",!0),e.selectCtx&&e.selectCtx.multiple?(T(),G(u,{key:1,class:M(`${e.prefixCls}-checkbox`),"model-value":e.isSelected,disabled:e.disabled,"uninject-group-context":""},{default:Q((()=>[D(e.$slots,"default",{},(()=>[le(ae(e.label),1)]))])),_:3},8,["class","model-value","disabled"])):(T(),L("span",{key:2,class:M(`${e.prefixCls}-content`)},[D(e.$slots,"default",{},(()=>[le(ae(e.label),1)]))],2)),e.$slots.suffix?(T(),L("span",{key:3,class:M(`${e.prefixCls}-suffix`)},[D(e.$slots,"suffix")],2)):H("v-if",!0)])),_:3},8,["class","onClick","onMouseenter","onMouseleave"])),[[J,e.isValid]])}]]),Ze=Object.defineProperty,Xe=Object.defineProperties,Ye=Object.getOwnPropertyDescriptors,el=Object.getOwnPropertySymbols,ll=Object.prototype.hasOwnProperty,al=Object.prototype.propertyIsEnumerable,tl=(e,l,a)=>l in e?Ze(e,l,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[l]=a,ol=(e,l)=>{for(var a in l||(l={}))ll.call(l,a)&&tl(e,a,l[a]);if(el)for(var a of el(l))al.call(l,a)&&tl(e,a,l[a]);return e};const nl={value:"value",label:"label",disabled:"disabled",tagProps:"tagProps",render:"render"},ul=({options:e,extraOptions:l,inputValue:a,filterOption:t,showExtraOptions:o,valueKey:n,fieldNames:r})=>{const i=E((()=>ol(ol({},nl),null==r?void 0:r.value))),s=Y(new Map),d=E((()=>Array.from(s.values()).sort(((e,l)=>u(e.index)&&u(l.index)?e.index-l.index:0)))),p=E((()=>{var l,a;const t=new Map;return{optionInfos:qe(null!=(l=null==e?void 0:e.value)?l:[],{valueKey:null!=(a=null==n?void 0:n.value)?a:"value",fieldNames:i.value,origin:"options",optionInfoMap:t}),optionInfoMap:t}})),v=E((()=>{var e,a;const t=new Map;return{optionInfos:qe(null!=(e=null==l?void 0:l.value)?e:[],{valueKey:null!=(a=null==n?void 0:n.value)?a:"value",fieldNames:i.value,origin:"extraOptions",optionInfoMap:t}),optionInfoMap:t}})),c=Y(new Map);N([d,null!=e?e:z([]),null!=l?l:z([]),null!=n?n:z("value")],(()=>{c.clear(),d.value.forEach(((e,l)=>{var a;c.set(e.key,(a=ol({},e),Xe(a,Ye({index:l}))))})),p.value.optionInfoMap.forEach((e=>{c.has(e.key)||(e.index=c.size,c.set(e.key,e))})),v.value.optionInfoMap.forEach((e=>{c.has(e.key)||(e.index=c.size,c.set(e.key,e))}))}),{immediate:!0,deep:!0});const f=E((()=>{var e;const l=We(p.value.optionInfos,{inputValue:null==a?void 0:a.value,filterOption:null==t?void 0:t.value});return(null==(e=null==o?void 0:o.value)||e)&&l.push(...We(v.value.optionInfos,{inputValue:null==a?void 0:a.value,filterOption:null==t?void 0:t.value})),l})),m=E((()=>Array.from(c.values()).filter((e=>("extraOptions"!==e.origin||!1!==(null==o?void 0:o.value))&&Je(e,{inputValue:null==a?void 0:a.value,filterOption:null==t?void 0:t.value}))))),b=E((()=>m.value.filter((e=>!e.disabled)).map((e=>e.key))));return{validOptions:f,optionInfoMap:c,validOptionInfos:m,enabledOptionKeys:b,getNextSlotOptionIndex:()=>s.size,addSlotOptionInfo:(e,l)=>{s.set(e,l)},removeSlotOptionInfo:e=>{s.delete(e)}}},rl=({dataKeys:e,contentRef:l,fixedSize:a,estimatedSize:t,buffer:o})=>{const n=z(0),u=new Map,r=E((()=>e.value.length)),i=z(0),s=E((()=>{const e=i.value+3*o.value;return e>r.value?r.value:e})),d=E((()=>{const e=r.value-3*o.value;return e<0?0:e})),p=z(a.value),v=E((()=>30!==t.value?t.value:n.value||t.value)),c=l=>{var a;if(p.value)return v.value;const t=e.value[l];return null!=(a=u.get(t))?a:v.value};R((()=>{const e=Array.from(u.values()).reduce(((e,l)=>e+l),0);e>0&&(n.value=e/u.size)}));const f=(e,l)=>{let a=0;for(let t=e;t<l;t++)a+=c(t);return a},m=E((()=>p.value?v.value*i.value:f(0,i.value))),b=E((()=>p.value?v.value*(r.value-s.value):f(s.value,r.value)));return{frontPadding:m,behindPadding:b,start:i,end:s,getStartByScroll:e=>{const l=(e=>{const l=e>=m.value;let a=Math.abs(e-m.value);const t=l?i.value:i.value-1;let o=0;for(;a>0;)a-=c(t+o),l?o++:o--;return o})(e),a=i.value+l-o.value;return a<0?0:a>d.value?d.value:a},setItemSize:(e,l)=>{u.set(e,l)},hasItemSize:e=>u.has(e),setStart:e=>{e<0?i.value=0:e>d.value?i.value=d.value:i.value=e},getScrollOffset:e=>p.value?v.value*e:f(0,e)}};var il=_({name:"VirtualListItem",props:{hasItemSize:{type:Function,required:!0},setItemSize:{type:Function,required:!0}},setup(e,{slots:l}){var a;const t=null==(a=te())?void 0:a.vnode.key,o=z(),n=()=>{var l,a,n,u;const r=null!=(a=null==(l=o.value)?void 0:l.$el)?a:o.value,i=null!=(u=null==(n=null==r?void 0:r.getBoundingClientRect)?void 0:n.call(r).height)?u:null==r?void 0:r.offsetHeight;i&&e.setItemSize(t,i)};return R((()=>n())),ee((()=>n())),()=>{var e;const a=d(null==(e=l.default)?void 0:e.call(l));return a?ue(a,{ref:o},!0):null}}}),sl=Object.defineProperty,dl=Object.getOwnPropertySymbols,pl=Object.prototype.hasOwnProperty,vl=Object.prototype.propertyIsEnumerable,cl=(e,l,a)=>l in e?sl(e,l,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[l]=a;var fl=a(_({name:"VirtualList",components:{VirtualListItem:il},props:{height:{type:[Number,String],default:200},data:{type:Array,default:()=>[]},threshold:{type:Number,default:0},itemKey:{type:String,default:"key"},fixedSize:{type:Boolean,default:!1},estimatedSize:{type:Number,default:30},buffer:{type:Number,default:10},component:{type:[String,Object],default:"div"},listAttrs:{type:Object},contentAttrs:{type:Object},paddingPosition:{type:String,default:"content"}},emits:{scroll:e=>!0,reachBottom:e=>!0},setup(e,{emit:l}){const{data:a,itemKey:o,fixedSize:r,estimatedSize:i,buffer:s,height:d}=F(e),p=t("virtual-list"),v=E((()=>n(e.component)?((e,l)=>{for(var a in l||(l={}))pl.call(l,a)&&cl(e,a,l[a]);if(dl)for(var a of dl(l))vl.call(l,a)&&cl(e,a,l[a]);return e})({container:"div",list:"div",content:"div"},e.component):{container:e.component,list:"div",content:"div"})),c=z(),f=z(),m=E((()=>({height:u(d.value)?`${d.value}px`:d.value,overflow:"auto"}))),b=E((()=>a.value.map(((e,l)=>{var a;return null!=(a=e[o.value])?a:l})))),{frontPadding:y,behindPadding:g,start:h,end:O,getStartByScroll:S,setItemSize:C,hasItemSize:w,setStart:x,getScrollOffset:V}=rl({dataKeys:b,contentRef:f,fixedSize:r,estimatedSize:i,buffer:s}),$=E((()=>e.threshold&&a.value.length<=e.threshold?a.value:a.value.slice(h.value,O.value))),k=e=>{var l,a;if(c.value)if(u(e))c.value.scrollTop=e;else{const t=null!=(a=e.index)?a:b.value.indexOf(null!=(l=e.key)?l:"");x(t-s.value),c.value.scrollTop=V(t),oe((()=>{if(c.value){const e=V(t);e!==c.value.scrollTop&&(c.value.scrollTop=e)}}))}};return{prefixCls:p,containerRef:c,contentRef:f,frontPadding:y,currentList:$,behindPadding:g,onScroll:e=>{const{scrollTop:a,scrollHeight:t,offsetHeight:o}=e.target,n=S(a);n!==h.value&&(x(n),oe((()=>{k(a)}))),l("scroll",e);Math.floor(t-(a+o))<=0&&l("reachBottom",e)},setItemSize:C,hasItemSize:w,start:h,scrollTo:k,style:m,mergedComponent:v}}}),[["render",function(e,l,a,t,o,n){const u=A("VirtualListItem");return T(),G(q(e.mergedComponent.container),{ref:"containerRef",class:M(e.prefixCls),style:se(e.style),onScroll:e.onScroll},{default:Q((()=>[(T(),G(q(e.mergedComponent.list),Z(e.listAttrs,{style:"list"===e.paddingPosition?{paddingTop:`${e.frontPadding}px`,paddingBottom:`${e.behindPadding}px`}:{}}),{default:Q((()=>[(T(),G(q(e.mergedComponent.content),Z({ref:"contentRef"},e.contentAttrs,{style:"content"===e.paddingPosition?{paddingTop:`${e.frontPadding}px`,paddingBottom:`${e.behindPadding}px`}:{}}),{default:Q((()=>[(T(!0),L(re,null,ie(e.currentList,((l,a)=>{var t;return T(),G(u,{key:null!=(t=l[e.itemKey])?t:e.start+a,"has-item-size":e.hasItemSize,"set-item-size":e.setItemSize},{default:Q((()=>[D(e.$slots,"item",{item:l,index:e.start+a})])),_:2},1032,["has-item-size","set-item-size"])})),128))])),_:3},16,["style"]))])),_:3},16,["style"]))])),_:3},8,["class","style","onScroll"])}]]);var ml=_({name:"InputLabel",inheritAttrs:!1,props:{modelValue:Object,inputValue:{type:String,default:""},enabledInput:Boolean,formatLabel:Function,placeholder:String,retainInputValue:Boolean,disabled:Boolean,baseCls:String,size:String,error:Boolean,focused:Boolean,uninjectFormItemContext:Boolean},emits:["update:inputValue","inputValueChange","focus","blur"],setup(e,{attrs:l,emit:a,slots:o}){var n;const{size:u,disabled:r,error:i,inputValue:s,uninjectFormItemContext:d}=F(e),v=null!=(n=e.baseCls)?n:t("input-label"),{mergedSize:c,mergedDisabled:f,mergedError:m,eventHandlers:b}=be({size:u,disabled:r,error:i,uninject:null==d?void 0:d.value}),{mergedSize:y}=ye(c),{inputRef:g,_focused:h,computedValue:O,handleInput:S,handleComposition:C,handleFocus:w,handleBlur:x,handleMousedown:V}=(({defaultValue:e,modelValue:l,emit:a,eventName:t="input",updateEventName:o="update:modelValue",eventHandlers:n})=>{var u;const r=z(),i=z(null!=(u=null==e?void 0:e.value)?u:""),s=z(!1),d=z(!1),p=z("");let v;const c=E((()=>{var e;return null!=(e=null==l?void 0:l.value)?e:i.value})),f=(e,l)=>{i.value=e,a(o,e),a(t,e,l)},m=e=>{"input"===t&&c.value!==v&&(v=c.value,a("change",c.value,e))};return N(c,(e=>{r.value&&e!==r.value.value&&(r.value.value=e)})),{inputRef:r,_value:i,_focused:s,isComposition:d,compositionValue:p,computedValue:c,handleInput:e=>{const{value:l}=e.target;d.value||(f(l,e),oe((()=>{r.value&&c.value!==r.value.value&&(r.value.value=c.value)})))},handleComposition:e=>{var l;const{value:a}=e.target;"compositionend"===e.type?(d.value=!1,p.value="",f(a,e),oe((()=>{r.value&&c.value!==r.value.value&&(r.value.value=c.value)}))):(d.value=!0,p.value=c.value+(null!=(l=e.data)?l:""))},handleFocus:e=>{var l,t;s.value=!0,v=c.value,a("focus",e),null==(t=null==(l=null==n?void 0:n.value)?void 0:l.onFocus)||t.call(l,e)},handleBlur:e=>{var l,t;s.value=!1,a("blur",e),null==(t=null==(l=null==n?void 0:n.value)?void 0:l.onBlur)||t.call(l,e),m(e)},handleKeyDown:e=>{const l=e.key||e.code;d.value||l!==$.key||(a("pressEnter",e),m(e))},handleMousedown:e=>{r.value&&e.target!==r.value&&(e.preventDefault(),r.value.focus())}}})({modelValue:s,emit:a,eventName:"inputValueChange",updateEventName:"update:inputValue",eventHandlers:b}),B=E((()=>{var l;return null!=(l=e.focused)?l:h.value})),I=E((()=>e.enabledInput&&h.value||!e.modelValue)),j=()=>{var l,a;return e.modelValue?null!=(a=null==(l=e.formatLabel)?void 0:l.call(e,e.modelValue))?a:e.modelValue.label:""},R=E((()=>e.enabledInput&&e.modelValue?j():e.placeholder)),_=E((()=>[v,`${v}-size-${y.value}`,{[`${v}-search`]:e.enabledInput,[`${v}-focus`]:B.value,[`${v}-disabled`]:f.value,[`${v}-error`]:m.value}])),K=E((()=>k(l,p))),A=E((()=>P(l,p)));return{inputRef:g,render:()=>{return de("span",Z(K.value,{class:_.value,title:j(),onMousedown:V}),[o.prefix&&de("span",{class:`${v}-prefix`},[o.prefix()]),de("input",Z(A.value,{ref:g,class:[`${v}-input`,{[`${v}-input-hidden`]:!I.value}],value:O.value,readonly:!e.enabledInput,placeholder:R.value,disabled:f.value,onInput:S,onFocus:w,onBlur:x,onCompositionstart:C,onCompositionupdate:C,onCompositionend:C}),null),de("span",{class:[`${v}-value`,{[`${v}-value-hidden`]:I.value}]},[e.modelValue?null!=(a=null==(l=o.default)?void 0:l.call(o,{data:e.modelValue}))?a:j():null]),o.suffix&&de("span",{class:`${v}-suffix`},[o.suffix()])]);var l,a}}},methods:{focus(){var e;null==(e=this.inputRef)||e.focus()},blur(){var e;null==(e=this.inputRef)||e.blur()}},render(){return this.render()}}),bl=Object.defineProperty,yl=Object.getOwnPropertySymbols,gl=Object.prototype.hasOwnProperty,hl=Object.prototype.propertyIsEnumerable,Ol=(e,l,a)=>l in e?bl(e,l,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[l]=a,Sl=(e,l)=>{for(var a in l||(l={}))gl.call(l,a)&&Ol(e,a,l[a]);if(yl)for(var a of yl(l))hl.call(l,a)&&Ol(e,a,l[a]);return e};const Cl=["red","orangered","orange","gold","lime","green","cyan","blue","arcoblue","purple","pinkpurple","magenta","gray"];var wl=a(_({name:"Tag",components:{IconHover:v,IconClose:c,IconLoading:f},props:{color:{type:String},size:{type:String},bordered:{type:Boolean,default:!1},visible:{type:Boolean,default:void 0},defaultVisible:{type:Boolean,default:!0},loading:{type:Boolean,default:!1},closable:{type:Boolean,default:!1},checkable:{type:Boolean,default:!1},checked:{type:Boolean,default:void 0},defaultChecked:{type:Boolean,default:!0},nowrap:{type:Boolean,default:!1}},emits:{"update:visible":e=>!0,"update:checked":e=>!0,close:e=>!0,check:(e,l)=>!0},setup(e,{emit:l}){const{size:a}=F(e),o=t("tag"),n=E((()=>e.color&&Cl.includes(e.color))),u=E((()=>e.color&&!Cl.includes(e.color))),r=z(e.defaultVisible),i=z(e.defaultChecked),s=E((()=>{var l;return null!=(l=e.visible)?l:r.value})),d=E((()=>{var l;return!e.checkable||(null!=(l=e.checked)?l:i.value)})),{mergedSize:p}=ye(a),v=E((()=>"mini"===p.value?"small":p.value)),c=E((()=>[o,`${o}-size-${v.value}`,{[`${o}-loading`]:e.loading,[`${o}-hide`]:!s.value,[`${o}-${e.color}`]:n.value,[`${o}-bordered`]:e.bordered,[`${o}-checkable`]:e.checkable,[`${o}-checked`]:d.value,[`${o}-custom-color`]:u.value}])),f=E((()=>{if(u.value)return{backgroundColor:e.color}}));return{prefixCls:o,cls:c,style:f,computedVisible:s,computedChecked:d,handleClick:a=>{if(e.checkable){const e=!d.value;i.value=e,l("update:checked",e),l("check",e,a)}},handleClose:e=>{r.value=!1,l("update:visible",!1),l("close",e)}}}}),[["render",function(e,l,a,t,o,n){const u=A("icon-close"),r=A("icon-hover"),i=A("icon-loading");return e.computedVisible?(T(),L("span",{key:0,class:M(e.cls),style:se(e.style),onClick:l[0]||(l[0]=(...l)=>e.handleClick&&e.handleClick(...l))},[e.$slots.icon?(T(),L("span",{key:0,class:M(`${e.prefixCls}-icon`)},[D(e.$slots,"icon")],2)):H("v-if",!0),e.nowrap?(T(),L("span",{key:1,class:M(`${e.prefixCls}-text`)},[D(e.$slots,"default")],2)):D(e.$slots,"default",{key:2}),e.closable?(T(),G(r,{key:3,role:"button","aria-label":"Close",prefix:e.prefixCls,class:M(`${e.prefixCls}-close-btn`),onClick:pe(e.handleClose,["stop"])},{default:Q((()=>[D(e.$slots,"close-icon",{},(()=>[de(u)]))])),_:3},8,["prefix","class","onClick"])):H("v-if",!0),e.loading?(T(),L("span",{key:4,class:M(`${e.prefixCls}-loading-icon`)},[de(i)],2)):H("v-if",!0)],6)):H("v-if",!0)}]]);const xl=Object.assign(wl,{install:(e,l)=>{m(e,l);const a=b(l);e.component(a+wl.name,wl)}});var Vl=Object.defineProperty,$l=Object.getOwnPropertySymbols,kl=Object.prototype.hasOwnProperty,Bl=Object.prototype.propertyIsEnumerable,Il=(e,l,a)=>l in e?Vl(e,l,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[l]=a,jl=(e,l)=>{for(var a in l||(l={}))kl.call(l,a)&&Il(e,a,l[a]);if($l)for(var a of $l(l))Bl.call(l,a)&&Il(e,a,l[a]);return e};const Pl={value:"value",label:"label",closable:"closable",tagProps:"tagProps"};var zl=_({name:"InputTag",inheritAttrs:!1,props:{modelValue:{type:Array},defaultValue:{type:Array,default:()=>[]},inputValue:String,defaultInputValue:{type:String,default:""},placeholder:String,disabled:{type:Boolean,default:!1},error:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},allowClear:{type:Boolean,default:!1},size:{type:String},maxTagCount:{type:Number,default:0},retainInputValue:{type:[Boolean,Object],default:!1},formatTag:{type:Function},uniqueValue:{type:Boolean,default:!1},fieldNames:{type:Object},tagNowrap:{type:Boolean,default:!1},baseCls:String,focused:Boolean,disabledInput:Boolean,uninjectFormItemContext:Boolean},emits:{"update:modelValue":e=>!0,"update:inputValue":e=>!0,change:(e,l)=>!0,inputValueChange:(e,l)=>!0,pressEnter:(e,l)=>!0,remove:(e,l)=>!0,clear:e=>!0,focus:e=>!0,blur:e=>!0},setup(e,{emit:l,slots:a,attrs:o}){const{size:r,disabled:i,error:s,uninjectFormItemContext:d,modelValue:f}=F(e),m=e.baseCls||t("input-tag"),b=z(),h=z(),{mergedSize:O,mergedDisabled:S,mergedError:C,feedback:w,eventHandlers:x}=be({size:r,disabled:i,error:s,uninject:null==d?void 0:d.value}),{mergedSize:V}=ye(O),j=E((()=>jl(jl({},Pl),e.fieldNames))),_=z(!1),K=z(e.defaultValue),A=z(e.defaultInputValue),T=z(!1),L=z(""),M=E((()=>n(e.retainInputValue)?jl({create:!1,blur:!1},e.retainInputValue):{create:e.retainInputValue,blur:e.retainInputValue})),D=Y({width:"12px"}),H=E((()=>e.focused||_.value)),G=(e,a)=>{A.value=e,l("update:inputValue",e),l("inputValueChange",e,a)},q=e=>{var l;const{value:a}=e.target;"compositionend"===e.type?(T.value=!1,L.value="",G(a,e),oe((()=>{b.value&&J.value!==b.value.value&&(b.value.value=J.value)}))):(T.value=!0,L.value=J.value+(null!=(l=e.data)?l:""))},W=E((()=>{var l;return null!=(l=e.modelValue)?l:K.value})),J=E((()=>{var l;return null!=(l=e.inputValue)?l:A.value}));N(f,(e=>{(y(e)||g(e))&&(K.value=[])}));const Q=e=>{b.value&&e.target!==b.value&&(e.preventDefault(),b.value.focus())},U=e=>{const{value:l}=e.target;T.value||(G(l,e),oe((()=>{b.value&&J.value!==b.value.value&&(b.value.value=J.value)})))},X=E((()=>((e,l)=>{const a=[];for(const t of e)if(n(t))a.push({raw:t,value:t[l.value],label:t[l.label],closable:t[l.closable],tagProps:t[l.tagProps]});else if(e||u(e)){const e={value:t,label:String(t),closable:!0};a.push(Sl({raw:e},e))}return a})(W.value,j.value))),ee=E((()=>{if(e.maxTagCount>0){const l=X.value.length-e.maxTagCount;if(l>0){const a=X.value.slice(0,e.maxTagCount),t={value:"__arco__more",label:`+${l}...`,closable:!1};return a.push(jl({raw:t},t)),a}}return X.value})),le=(e,a)=>{var t,o;K.value=e,l("update:modelValue",e),l("change",e,a),null==(o=null==(t=x.value)?void 0:t.onChange)||o.call(t,a)},ae=(e,a,t)=>{var o;const n=null==(o=W.value)?void 0:o.filter(((e,l)=>l!==a));le(n,t),l("remove",e,t)},te=e=>{le([],e),l("clear",e)},ne=E((()=>!S.value&&!e.readonly&&e.allowClear&&Boolean(W.value.length))),ue=e=>{var a,t;_.value=!0,l("focus",e),null==(t=null==(a=x.value)?void 0:a.onFocus)||t.call(a,e)},re=e=>{var a,t;_.value=!1,!M.value.blur&&J.value&&G("",e),l("blur",e),null==(t=null==(a=x.value)?void 0:a.onBlur)||t.call(a,e)},ie=a=>{if(S.value||e.readonly)return;const t=a.key||a.code;if(!T.value&&J.value&&t===$.key&&(a=>{var t;if(J.value){if(a.preventDefault(),e.uniqueValue&&(null==(t=W.value)?void 0:t.includes(J.value)))return void l("pressEnter",J.value,a);const o=W.value.concat(J.value);le(o,a),l("pressEnter",J.value,a),M.value.create||G("",a)}})(a),!T.value&&ee.value.length>0&&!J.value&&t===I.key){const e=(()=>{for(let e=X.value.length-1;e>=0;e--)if(X.value[e].closable)return e;return-1})();e>=0&&ae(X.value[e].value,e,a)}},se=e=>{D.width=e>12?`${e}px`:"12px"};R((()=>{h.value&&se(h.value.offsetWidth)}));const pe=()=>{h.value&&se(h.value.offsetWidth)};N(J,(e=>{b.value&&!T.value&&e!==b.value.value&&(b.value.value=e)}));const ce=E((()=>[m,`${m}-size-${V.value}`,{[`${m}-disabled`]:S.value,[`${m}-disabled-input`]:e.disabledInput,[`${m}-error`]:C.value,[`${m}-focus`]:H.value,[`${m}-readonly`]:e.readonly,[`${m}-has-tag`]:ee.value.length>0,[`${m}-has-prefix`]:Boolean(a.prefix),[`${m}-has-suffix`]:Boolean(a.suffix)||ne.value||w.value,[`${m}-has-placeholder`]:!W.value.length}])),fe=E((()=>k(o,p))),me=E((()=>P(o,p)));return{inputRef:b,render:()=>{var l;return de("span",Z({class:ce.value,onMousedown:Q},fe.value),[de(ge,{onResize:pe},{default:()=>[de("span",{ref:h,class:`${m}-mirror`},[ee.value.length>0?L.value||J.value:L.value||J.value||e.placeholder])]}),a.prefix&&de("span",{class:`${m}-prefix`},[a.prefix()]),de(ve,{tag:"span",name:"input-tag-zoom",class:[`${m}-inner`,{[`${m}-nowrap`]:e.tagNowrap}]},{default:()=>[ee.value.map(((l,t)=>de(xl,Z({key:`tag-${l.value}`,class:`${m}-tag`,closable:!S.value&&!e.readonly&&l.closable,visible:!0,nowrap:e.tagNowrap},l.tagProps,{onClose:e=>ae(l.value,t,e)}),{default:()=>{var t,o,n,u;return[null!=(u=null!=(n=null==(t=a.tag)?void 0:t.call(a,{data:l.raw}))?n:null==(o=e.formatTag)?void 0:o.call(e,l.raw))?u:l.label]}}))),de("input",Z(me.value,{ref:b,key:"input-tag-input",class:`${m}-input`,style:D,placeholder:0===ee.value.length?e.placeholder:void 0,disabled:S.value,readonly:e.readonly||e.disabledInput,onInput:U,onKeydown:ie,onFocus:ue,onBlur:re,onCompositionstart:q,onCompositionupdate:q,onCompositionend:q}),null)]}),ne.value&&de(v,{class:`${m}-clear-btn`,onClick:te,onMousedown:e=>e.stopPropagation()},{default:()=>[de(c,null,null)]}),(a.suffix||Boolean(w.value))&&de("span",{class:`${m}-suffix`},[null==(l=a.suffix)?void 0:l.call(a),Boolean(w.value)&&de(B,{type:w.value},null)])])}}},methods:{focus(){var e;null==(e=this.inputRef)||e.focus()},blur(){var e;null==(e=this.inputRef)||e.blur()}},render(){return this.render()}});const Rl=Object.assign(zl,{install:(e,l)=>{m(e,l);const a=b(l);e.component(a+zl.name,zl)}});var Nl=_({name:"SelectView",props:{modelValue:{type:Array,required:!0},inputValue:String,placeholder:String,disabled:{type:Boolean,default:!1},error:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},opened:{type:Boolean,default:!1},size:{type:String},bordered:{type:Boolean,default:!0},multiple:{type:Boolean,default:!1},allowClear:{type:Boolean,default:!1},allowCreate:{type:Boolean,default:!1},allowSearch:{type:Boolean,default:e=>h(e.modelValue)},maxTagCount:{type:Number,default:0},tagNowrap:{type:Boolean,default:!1},retainInputValue:{type:Boolean,default:!1}},emits:["remove","clear","focus","blur"],setup(e,{emit:l,slots:a}){const{size:o,disabled:n,error:u}=F(e),r=t("select-view"),{feedback:i,eventHandlers:s,mergedDisabled:d,mergedSize:p,mergedError:m}=be({size:o,disabled:n,error:u}),{mergedSize:b}=ye(p),{opened:y}=F(e),g=z(),h=E((()=>{var e;return null==(e=g.value)?void 0:e.inputRef})),C=E((()=>0===e.modelValue.length)),w=E((()=>e.allowSearch||e.allowCreate)),x=E((()=>e.allowClear&&!e.disabled&&!C.value)),V=e=>{var a,t;l("focus",e),null==(t=null==(a=s.value)?void 0:a.onFocus)||t.call(a,e)},$=e=>{var a,t;l("blur",e),null==(t=null==(a=s.value)?void 0:a.onBlur)||t.call(a,e)},k=e=>{l("remove",e)},I=e=>{l("clear",e)},j=()=>{return de(re,null,[x.value&&de(v,{class:`${r}-clear-btn`,onClick:I,onMousedown:e=>e.stopPropagation()},{default:()=>[de(c,null,null)]}),de("span",{class:`${r}-icon`},[e.loading?null!=(t=null==(l=a["loading-icon"])?void 0:l.call(a))?t:de(f,null,null):e.allowSearch&&e.opened?null!=(n=null==(o=a["search-icon"])?void 0:o.call(a))?n:de(O,null,null):a["arrow-icon"]?a["arrow-icon"]():de(S,{class:`${r}-arrow-icon`},null)]),Boolean(i.value)&&de(B,{type:i.value},null)]);var l,t,o,n};N(y,(e=>{!e&&h.value&&h.value.isSameNode(document.activeElement)&&h.value.blur()}));const P=E((()=>[`${r}-${e.multiple?"multiple":"single"}`,{[`${r}-opened`]:e.opened,[`${r}-borderless`]:!e.bordered}]));return{inputRef:h,handleFocus:V,handleBlur:$,render:()=>e.multiple?de(Rl,{ref:g,baseCls:r,class:P.value,modelValue:e.modelValue,inputValue:e.inputValue,focused:e.opened,placeholder:e.placeholder,disabled:d.value,size:b.value,error:m.value,maxTagCount:e.maxTagCount,disabledInput:!e.allowSearch&&!e.allowCreate,tagNowrap:e.tagNowrap,retainInputValue:!0,uninjectFormItemContext:!0,onRemove:k,onFocus:V,onBlur:$},{prefix:a.prefix,suffix:j,tag:a.label}):de(ml,{ref:g,baseCls:r,class:P.value,modelValue:e.modelValue[0],inputValue:e.inputValue,focused:e.opened,placeholder:e.placeholder,disabled:d.value,size:b.value,error:m.value,enabledInput:w.value,uninjectFormItemContext:!0,onFocus:V,onBlur:$},{default:a.label,prefix:a.prefix,suffix:j})}},methods:{focus(){this.inputRef&&this.inputRef.focus()},blur(){this.inputRef&&this.inputRef.blur()}},render(){return this.render()}});var El=a(_({name:"Optgroup",props:{label:{type:String}},setup:()=>({prefixCls:t("select-group")})}),[["render",function(e,l,a,t,o,n){return T(),L(re,null,[U("li",{class:M(`${e.prefixCls}-title`)},[D(e.$slots,"label",{},(()=>[le(ae(e.label),1)]))],2),D(e.$slots,"default")],64)}]]);const _l="undefined"==typeof window?global:window;function Fl(e,l){let a=0;return(...t)=>{a&&_l.clearTimeout(a),a=_l.setTimeout((()=>{a=0,e(...t)}),l)}}var Kl=Object.defineProperty,Al=Object.defineProperties,Tl=Object.getOwnPropertyDescriptors,Ll=Object.getOwnPropertySymbols,Ml=Object.prototype.hasOwnProperty,Dl=Object.prototype.propertyIsEnumerable,Hl=(e,l,a)=>l in e?Kl(e,l,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[l]=a,Gl=(e,l)=>{for(var a in l||(l={}))Ml.call(l,a)&&Hl(e,a,l[a]);if(Ll)for(var a of Ll(l))Dl.call(l,a)&&Hl(e,a,l[a]);return e};const ql={value:"value",label:"label",disabled:"disabled",tagProps:"tagProps",render:"render"};var Wl=_({name:"Select",components:{Trigger:j,SelectView:Nl},inheritAttrs:!1,props:{multiple:{type:Boolean,default:!1},modelValue:{type:[String,Number,Boolean,Object,Array],default:void 0},defaultValue:{type:[String,Number,Boolean,Object,Array],default:e=>y(e.multiple)?"":[]},inputValue:{type:String},defaultInputValue:{type:String,default:""},size:{type:String},placeholder:String,loading:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},error:{type:Boolean,default:!1},allowClear:{type:Boolean,default:!1},allowSearch:{type:[Boolean,Object],default:e=>Boolean(e.multiple)},allowCreate:{type:Boolean,default:!1},maxTagCount:{type:Number,default:0},popupContainer:{type:[String,Object]},bordered:{type:Boolean,default:!0},defaultActiveFirstOption:{type:Boolean,default:!0},popupVisible:{type:Boolean,default:void 0},defaultPopupVisible:{type:Boolean,default:!1},unmountOnClose:{type:Boolean,default:!1},filterOption:{type:[Boolean,Function],default:!0},options:{type:Array,default:()=>[]},virtualListProps:{type:Object},triggerProps:{type:Object},formatLabel:{type:Function},fallbackOption:{type:[Boolean,Function],default:!0},showExtraOptions:{type:Boolean,default:!0},valueKey:{type:String,default:"value"},searchDelay:{type:Number,default:500},limit:{type:Number,default:0},fieldNames:{type:Object},scrollbar:{type:[Boolean,Object],default:!0},showHeaderOnEmpty:{type:Boolean,default:!1},showFooterOnEmpty:{type:Boolean,default:!1},tagNowrap:{type:Boolean,default:!1}},emits:{"update:modelValue":e=>!0,"update:inputValue":e=>!0,"update:popupVisible":e=>!0,change:e=>!0,inputValueChange:e=>!0,popupVisibleChange:e=>!0,clear:e=>!0,remove:e=>!0,search:e=>!0,dropdownScroll:e=>!0,dropdownReachBottom:e=>!0,exceedLimit:(e,l)=>!0},setup(e,{slots:a,emit:o,attrs:d}){const{size:p,disabled:v,error:c,options:f,filterOption:m,valueKey:b,multiple:O,popupVisible:S,defaultPopupVisible:w,showExtraOptions:$,modelValue:k,fieldNames:B,loading:I,defaultActiveFirstOption:P}=F(e),R=t("select"),{mergedSize:_,mergedDisabled:K,mergedError:A,eventHandlers:T}=be({size:p,disabled:v,error:c}),L=E((()=>e.virtualListProps?"div":"li")),M=E((()=>n(e.allowSearch)&&Boolean(e.allowSearch.retainInputValue)));E((()=>{if(i(e.formatLabel))return l=>{const a=Ce.get(l.value);return e.formatLabel(a)}}));const D=z(),H=z({}),G=z(),{computedPopupVisible:q,handlePopupVisibleChange:W}=Se({popupVisible:S,defaultPopupVisible:w,emit:o}),J=z(e.defaultValue),Q=E((()=>{var a;const t=null!=(a=e.modelValue)?a:J.value;return(h(t)?t:t||u(t)||r(t)||l(t)?[t]:[]).map((l=>({value:l,key:He(l,e.valueKey)})))}));N(k,(e=>{(y(e)||g(e))&&(J.value=O.value?[]:e)}));const U=E((()=>Q.value.map((e=>e.key)))),X=E((()=>Gl(Gl({},ql),null==B?void 0:B.value))),ee=z(),le=e=>{ee.value=(e=>{const l={};return e.forEach((e=>{l[e]=Ce.get(e)})),l})(e)},ae=z([]),te=E((()=>ae.value.map((l=>{var a;let t=(o=l.value,i(e.fallbackOption)?e.fallbackOption(o):{[X.value.value]:o,[X.value.label]:String(n(o)?o[null==b?void 0:b.value]:o)});var o;const u=null==(a=ee.value)?void 0:a[l.key];return y(u)||C(u)||(t=Gl(Gl({},t),u)),t}))));oe((()=>{ce((()=>{var l;const a=(()=>{const l=[],a=[];if(e.allowCreate||e.fallbackOption)for(const e of Q.value)if(!a.includes(e.key)&&""!==e.value){const t=Ce.get(e.key);t&&"extraOptions"!==t.origin||(l.push(e),a.push(e.key))}if(e.allowCreate&&re.value){const e=He(re.value);if(!a.includes(e)){const a=Ce.get(e);a&&"extraOptions"!==a.origin||l.push({value:re.value,key:e})}}return l})();if(a.length!==ae.value.length)ae.value=a;else if(a.length>0)for(let e=0;e<a.length;e++)if(a[e].key!==(null==(l=ae.value[e])?void 0:l.key)){ae.value=a;break}}))}));const ue=z(""),re=E((()=>{var l;return null!=(l=e.inputValue)?l:ue.value}));N(q,(e=>{e||M.value||!re.value||se("")}));const ie=l=>{var a,t;const n=(l=>{var a,t;return e.multiple?l.map((e=>{var l,a;return null!=(a=null==(l=Ce.get(e))?void 0:l.value)?a:""})):null!=(t=null==(a=Ce.get(l[0]))?void 0:a.value)?t:(e=>e.has("__arco__option__string-"))(Ce)?void 0:""})(l);J.value=n,o("update:modelValue",n),o("change",n),null==(t=null==(a=T.value)?void 0:a.onChange)||t.call(a),le(l)},se=e=>{ue.value=e,o("update:inputValue",e),o("inputValueChange",e)},pe=Fl((e=>{o("search",e)}),e.searchDelay),ve=l=>{l!==re.value&&(q.value||W(!0),se(l),e.allowSearch&&pe(l))},me=e=>{const l=Ce.get(e),a=U.value.filter((l=>l!==e));ie(a),o("remove",null==l?void 0:l.value)},ye=e=>{null==e||e.stopPropagation();const l=U.value.filter((e=>{var l;return null==(l=Ce.get(e))?void 0:l.disabled}));ie(l),se(""),o("clear",e)},ge=e=>{o("dropdownScroll",e)},he=e=>{o("dropdownReachBottom",e)},{validOptions:Oe,optionInfoMap:Ce,validOptionInfos:we,enabledOptionKeys:xe,handleKeyDown:Ve}=(({multiple:e,options:l,extraOptions:a,inputValue:t,filterOption:o,showExtraOptions:n,component:u,valueKey:r,fieldNames:i,loading:d,popupVisible:p,valueKeys:v,dropdownRef:c,optionRefs:f,virtualListRef:m,onSelect:b,onPopupVisibleChange:y,enterToOpen:g=!0,defaultActiveFirstOption:h})=>{const{validOptions:O,optionInfoMap:S,validOptionInfos:C,enabledOptionKeys:w,getNextSlotOptionIndex:$,addSlotOptionInfo:k,removeSlotOptionInfo:B}=ul({options:l,extraOptions:a,inputValue:t,filterOption:o,showExtraOptions:n,valueKey:r,fieldNames:i}),I=z();N(w,(e=>{I.value&&e.includes(I.value)||(I.value=e[0])}));const j=e=>{I.value=e},P=e=>{const l=w.value.length;if(0===l)return;if(!I.value)return"down"===e?w.value[0]:w.value[l-1];const a=(l+w.value.indexOf(I.value)+("up"===e?-1:1))%l;return w.value[a]},R=e=>{var l,a;(null==m?void 0:m.value)&&m.value.scrollTo({key:e});const t=S.get(e),o=null==(l=null==c?void 0:c.value)?void 0:l.wrapperRef,n=null!=(a=null==f?void 0:f.value[e])?a:null==t?void 0:t.ref;if(!o||!n)return;if(o.scrollHeight===o.offsetHeight)return;const u=s(n,o),r=o.scrollTop;u.top<0?o.scrollTo(0,r+u.top):u.bottom<0&&o.scrollTo(0,r-u.bottom)};N(p,(e=>{var l;if(e){const e=v.value[v.value.length-1];let a=null==(l=null==h?void 0:h.value)||l?w.value[0]:void 0;w.value.includes(e)&&(a=e),a!==I.value&&(I.value=a),oe((()=>{I.value&&R(I.value)}))}}));const E=x(new Map([[V.ENTER,e=>{(null==d?void 0:d.value)||e.isComposing||(p.value?I.value&&(b(I.value,e),e.preventDefault()):g&&(y(!0),e.preventDefault()))}],[V.ESC,e=>{p.value&&(y(!1),e.preventDefault())}],[V.ARROW_DOWN,e=>{if(p.value){const l=P("down");l&&(I.value=l,R(l)),e.preventDefault()}}],[V.ARROW_UP,e=>{if(p.value){const l=P("up");l&&(I.value=l,R(l)),e.preventDefault()}}]]));return ne(ze,Y({multiple:e,valueKey:r,inputValue:t,filterOption:o,component:u,valueKeys:v,activeKey:I,setActiveKey:j,onSelect:b,getNextSlotOptionIndex:$,addSlotOptionInfo:k,removeSlotOptionInfo:B})),{validOptions:O,optionInfoMap:S,validOptionInfos:C,enabledOptionKeys:w,activeKey:I,setActiveKey:j,addSlotOptionInfo:k,removeSlotOptionInfo:B,getNextActiveKey:P,scrollIntoView:R,handleKeyDown:E}})({multiple:O,options:f,extraOptions:te,inputValue:re,filterOption:m,showExtraOptions:$,component:L,valueKey:b,fieldNames:B,loading:I,popupVisible:q,valueKeys:U,dropdownRef:D,optionRefs:H,virtualListRef:G,defaultActiveFirstOption:P,onSelect:(l,a)=>{if(e.multiple){if(U.value.includes(l)){const e=U.value.filter((e=>e!==l));ie(e)}else if(xe.value.includes(l))if(e.limit>0&&U.value.length>=e.limit){const e=Ce.get(l);o("exceedLimit",null==e?void 0:e.value,a)}else{const e=U.value.concat(l);ie(e)}M.value||se("")}else{if(l!==U.value[0]&&ie([l]),M.value){const e=Ce.get(l);e&&se(e.label)}W(!1)}},onPopupVisibleChange:W}),$e=E((()=>{var e;const l=[];for(const o of Q.value){const u=Ce.get(o.key);u&&l.push((a=Gl({},u),t={value:o.key,label:null!=(e=null==u?void 0:u.label)?e:String(n(o.value)?o.value[null==b?void 0:b.value]:o.value),closable:!(null==u?void 0:u.disabled),tagProps:null==u?void 0:u.tagProps},Al(a,Tl(t))))}var a,t;return l})),ke=e=>{if(i(a.option)){const l=a.option;return()=>l({data:e.raw})}return i(e.render)?e.render:()=>e.label},Be=e=>{if(Me(e)){let a;return de(El,{key:e.key,label:e.label},"function"==typeof(l=a=e.options.map((e=>Be(e))))||"[object Object]"===Object.prototype.toString.call(l)&&!fe(l)?a:{default:()=>[a]})}var l;return Je(e,{inputValue:re.value,filterOption:null==m?void 0:m.value})?de(Ue,{ref:l=>{(null==l?void 0:l.$el)&&(H.value[e.key]=l.$el)},key:e.key,value:e.value,label:e.label,disabled:e.disabled,internal:!0},{default:ke(e)}):null},Ie=()=>de(Pe,{ref:D,loading:e.loading,empty:0===we.value.length,virtualList:Boolean(e.virtualListProps),scrollbar:e.scrollbar,showHeaderOnEmpty:e.showHeaderOnEmpty,showFooterOnEmpty:e.showFooterOnEmpty,onScroll:ge,onReachBottom:he},{default:()=>{var e,l;return[...null!=(l=null==(e=a.default)?void 0:e.call(a))?l:[],...Oe.value.map(Be)]},"virtual-list":()=>de(fl,Z(e.virtualListProps,{ref:G,data:Oe.value}),{item:({item:e})=>Be(e)}),empty:a.empty,header:a.header,footer:a.footer}),je=({data:l})=>{var t,o,n,u;if((a.label||i(e.formatLabel))&&l){const u=Ce.get(l.value);if(null==u?void 0:u.raw)return null!=(n=null==(t=a.label)?void 0:t.call(a,{data:u.raw}))?n:null==(o=e.formatLabel)?void 0:o.call(e,u.raw)}return null!=(u=null==l?void 0:l.label)?u:""};return()=>de(j,Z({trigger:"click",position:"bl",popupOffset:4,animationName:"slide-dynamic-origin",hideEmpty:!0,preventFocus:!0,autoFitPopupWidth:!0,autoFitTransformOrigin:!0,disabled:K.value,popupVisible:q.value,unmountOnClose:e.unmountOnClose,clickToClose:!(e.allowSearch||e.allowCreate),popupContainer:e.popupContainer,onPopupVisibleChange:W},e.triggerProps),{default:()=>{var l,t;return[null!=(t=null==(l=a.trigger)?void 0:l.call(a))?t:de(Nl,Z({class:R,modelValue:$e.value,inputValue:re.value,multiple:e.multiple,disabled:K.value,error:A.value,loading:e.loading,allowClear:e.allowClear,allowCreate:e.allowCreate,allowSearch:Boolean(e.allowSearch),opened:q.value,maxTagCount:e.maxTagCount,placeholder:e.placeholder,bordered:e.bordered,size:_.value,tagNowrap:e.tagNowrap,onInputValueChange:ve,onRemove:me,onClear:ye,onKeydown:Ve},d),{label:je,prefix:a.prefix,"arrow-icon":a["arrow-icon"],"loading-icon":a["loading-icon"],"search-icon":a["search-icon"]})]},content:Ie})}});const Jl=Object.assign(Wl,{Option:Ue,OptGroup:El,install:(e,l)=>{m(e,l);const a=b(l);e.component(a+Wl.name,Wl),e.component(a+Ue.name,Ue),e.component(a+El.name,El)}}),Ql=Symbol("RadioGroup");var Ul=_({name:"Radio",components:{IconHover:v},props:{modelValue:{type:[String,Number,Boolean],default:void 0},defaultChecked:{type:Boolean,default:!1},value:{type:[String,Number,Boolean],default:!0},type:{type:String,default:"radio"},disabled:{type:Boolean,default:!1},uninjectGroupContext:{type:Boolean,default:!1}},emits:{"update:modelValue":e=>!0,change:(e,l)=>!0},setup(e,{emit:l,slots:a}){const o=t("radio"),{modelValue:n}=F(e),u=e.uninjectGroupContext?void 0:K(Ql,void 0),{mergedDisabled:r,eventHandlers:i}=be({disabled:me(e,"disabled")}),s=z(null),d=z(e.defaultChecked),p=E((()=>"ArcoRadioGroup"===(null==u?void 0:u.name))),v=E((()=>{var l;return null!=(l=null==u?void 0:u.type)?l:e.type})),c=E((()=>(null==u?void 0:u.disabled)||r.value)),f=E((()=>{var l,a;return p.value?(null==u?void 0:u.value)===(null==(l=e.value)||l):y(e.modelValue)?d.value:e.modelValue===(null==(a=e.value)||a)}));N(n,(e=>{(y(e)||g(e))&&(d.value=!1)})),N(f,((e,l)=>{e!==l&&(d.value=e,s.value&&(s.value.checked=e))}));const m=e=>{var l,a;null==(a=null==(l=i.value)?void 0:l.onFocus)||a.call(l,e)},b=e=>{var l,a;null==(a=null==(l=i.value)?void 0:l.onBlur)||a.call(l,e)},h=e=>{e.stopPropagation()},O=a=>{var t,o,n,r,v;d.value=!0,p.value?null==u||u.handleChange(null==(t=e.value)||t,a):(l("update:modelValue",null==(o=e.value)||o),l("change",null==(n=e.value)||n,a),null==(v=null==(r=i.value)?void 0:r.onChange)||v.call(r,a)),oe((()=>{s.value&&s.value.checked!==f.value&&(s.value.checked=f.value)}))},S=E((()=>[`${"button"===v.value?`${o}-button`:o}`,{[`${o}-checked`]:f.value,[`${o}-disabled`]:c.value}]));return()=>{var l,t,n,r;return de("label",{class:S.value},[de("input",{ref:s,type:"radio",checked:f.value,value:e.value,class:`${o}-target`,disabled:c.value,onClick:h,onChange:O,onFocus:m,onBlur:b},null),"radio"===v.value?null!=(r=null==(n=null!=(t=a.radio)?t:null==(l=null==u?void 0:u.slots)?void 0:l.radio)?void 0:n({checked:f.value,disabled:c.value}))?r:de(re,null,[de(A("icon-hover"),{class:`${o}-icon-hover`,disabled:c.value||f.value},{default:()=>[de("span",{class:`${o}-icon`},null)]}),a.default&&de("span",{class:`${o}-label`},[a.default()])]):de("span",{class:`${o}-button-content`},[a.default&&a.default()])])}}}),Zl=_({name:"RadioGroup",props:{modelValue:{type:[String,Number,Boolean],default:void 0},defaultValue:{type:[String,Number,Boolean],default:""},type:{type:String,default:"radio"},size:{type:String},options:{type:Array},direction:{type:String,default:"horizontal"},disabled:{type:Boolean,default:!1}},emits:{"update:modelValue":e=>!0,change:(e,l)=>!0},setup(e,{emit:l,slots:a}){const o=t("radio-group"),{size:n,type:s,disabled:d,modelValue:p}=F(e),{mergedDisabled:v,mergedSize:c,eventHandlers:f}=be({size:n,disabled:d}),{mergedSize:m}=ye(c),b=z(e.defaultValue),h=E((()=>{var l;return null!=(l=e.modelValue)?l:b.value})),O=E((()=>{var l;return(null!=(l=e.options)?l:[]).map((e=>r(e)||u(e)?{label:e,value:e}:e))}));ne(Ql,Y({name:"ArcoRadioGroup",value:h,size:m,type:s,disabled:v,slots:a,handleChange:(e,a)=>{var t,o;b.value=e,l("update:modelValue",e),l("change",e,a),null==(o=null==(t=f.value)?void 0:t.onChange)||o.call(t,a)}})),N(h,(e=>{b.value!==e&&(b.value=e)})),N(p,(e=>{(y(e)||g(e))&&(b.value="")}));const S=E((()=>[`${o}${"button"===e.type?"-button":""}`,`${o}-size-${m.value}`,`${o}-direction-${e.direction}`,{[`${o}-disabled`]:v.value}]));return()=>{var e;return de("span",{class:S.value},[O.value.length>0?O.value.map((e=>de(Ul,{key:e.value,value:e.value,disabled:e.disabled,modelValue:h.value===e.value},{default:()=>[a.label?a.label({data:e}):i(e.label)?e.label():e.label]}))):null==(e=a.default)?void 0:e.call(a)])}}});const Xl=Object.assign(Ul,{Group:Zl,install:(e,l)=>{m(e,l);const a=b(l);e.component(a+Ul.name,Ul),e.component(a+Zl.name,Zl)}});const Yl=_({name:"Link",components:{IconLink:w,IconLoading:f},props:{href:String,status:{type:String,default:"normal"},hoverable:{type:Boolean,default:!0},icon:Boolean,loading:Boolean,disabled:Boolean},emits:{click:e=>!0},setup(e,{slots:l,emit:a}){const o=t("link"),n=function(e,l,a){return E((()=>Boolean(e[a]||l[a])))}(e,l,"icon");return{cls:E((()=>[o,`${o}-status-${e.status}`,{[`${o}-disabled`]:e.disabled,[`${o}-loading`]:e.loading,[`${o}-hoverless`]:!e.hoverable,[`${o}-with-icon`]:e.loading||n.value}])),prefixCls:o,showIcon:n,handleClick:l=>{e.disabled||e.loading?l.preventDefault():a("click",l)}}}}),ea=["href"];var la=a(Yl,[["render",function(e,l,a,t,o,n){const u=A("icon-loading"),r=A("icon-link");return T(),L("a",{href:e.disabled?void 0:e.href,class:M(e.cls),onClick:l[0]||(l[0]=(...l)=>e.handleClick&&e.handleClick(...l))},[e.loading||e.showIcon?(T(),L("span",{key:0,class:M(`${e.prefixCls}-icon`)},[e.loading?(T(),G(u,{key:0})):D(e.$slots,"icon",{key:1},(()=>[de(r)]))],2)):H("v-if",!0),D(e.$slots,"default")],10,ea)}]]);const aa=Object.assign(la,{install:(e,l)=>{m(e,l);const a=b(l);e.component(a+la.name,la)}});export{aa as L,Xl as R,Jl as S,xl as T,fl as V,je as a,Zl as b,Fl as d,Qe as i,xe as u};

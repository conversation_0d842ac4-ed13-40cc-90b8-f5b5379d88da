import"./index-DOhy6BH_.js";import"./index-BEo1tUsK.js";import{F as e,a}from"./index-DVDXfQhn.js";import{B as o,S as r}from"./index-DGtjsHgS.js";import{C as s}from"./index-DQjhgQFu.js";import{I as i,f as l}from"./index-DDFSMqsG.js";/* empty css              */import{I as n}from"./index-CuYx5vtf.js";import{r as d,ak as t,B as m,d as u,ab as p,f as c,j as f,k as g,q as v,A as _,z as h,u as b,y as j,I as x}from"./vue-D-10XvVk.js";import{D as w,a4 as y}from"./index-D-8JbLQk.js";import{r as V}from"./apiCommon-DcubqwY_.js";import{u as k}from"./useLoading-D5mh7tTu.js";import{I as U}from"./index-DfEXMvnc.js";import{_ as C}from"./_plugin-vue_export-helper-BCo6x5W8.js";import{P as I}from"./index-CE4IsVo2.js";import"./pick-Ccd8Sfcm.js";import"./ResizeObserver.es-CzGuHLZU.js";import"./render-function-CAXdZVZM.js";const q=w(),z={class:"login-form-wrapper"},L=["innerHTML"],D={class:"login-form-password-actions"},F=C(u({__name:"form",setup(u){const w=p(),{loading:C,codeUrl:I,loginForm:F,loginConfig:B,getCode:H,login:M}=(()=>{const{loading:e,setLoading:a}=k(),o=d(""),r=t("login-storage",{rememberUsername:!0,username:void 0}),s=m({username:r.value.username,password:void 0,code:void 0,uuid:""}),i=async()=>{const{data:e}=await V();o.value=e.svg,s.uuid=e.uuid};return{loading:e,codeUrl:o,loginForm:s,loginConfig:r,getCode:i,login:async()=>{a(!0);try{await q.login(s),r.value.username=r.value.rememberUsername?s.username:void 0}catch(e){throw a(!1),i(),e}}}})();H();const R=async e=>{if(e instanceof Event&&"function"==typeof e.preventDefault)return void e.preventDefault();const{errors:a,values:o}=e;if(!C.value&&!a)try{await M(),w.replace({name:y})}catch(r){}};return(d,t)=>{const m=c("icon-user"),u=i,p=e,w=c("icon-lock"),y=l,V=c("icon-safe"),k=c("icon-loading"),q=n,M=U,S=s,T=o,$=r,A=a;return f(),g("div",z,[t[9]||(t[9]=v("div",{class:"login-form-title"},"登录系统 · 平台端",-1)),_(A,{ref:"loginRef",model:b(F),class:"login-form",layout:"vertical",onSubmit:R},{default:h((()=>[_(p,{"show-colon":"",field:"username",rules:[{required:!0,message:"账号不能为空"}],"validate-trigger":["change","blur"],"hide-label":""},{default:h((()=>[_(u,{modelValue:b(F).username,"onUpdate:modelValue":t[0]||(t[0]=e=>b(F).username=e),placeholder:"请输入账号"},{prefix:h((()=>[_(m)])),_:1},8,["modelValue"])])),_:1}),t[7]||(t[7]=v("input",{type:"text",class:"fake-input"},null,-1)),t[8]||(t[8]=v("input",{type:"password",class:"fake-input"},null,-1)),_(p,{"show-colon":"",field:"password",rules:[{required:!0,message:"密码不能为空"}],"validate-trigger":["change","blur"],"hide-label":""},{default:h((()=>[_(y,{modelValue:b(F).password,"onUpdate:modelValue":t[1]||(t[1]=e=>b(F).password=e),placeholder:"请输入密码","allow-clear":""},{prefix:h((()=>[_(w)])),_:1},8,["modelValue"])])),_:1}),_(p,{"show-colon":"",field:"code",rules:[{required:!0,message:"验证码不能为空"}],"validate-trigger":["change","blur"],"hide-label":""},{default:h((()=>[_(M,{"hide-button":"",modelValue:b(F).code,"onUpdate:modelValue":t[3]||(t[3]=e=>b(F).code=e),placeholder:"请输入验证码"},{prefix:h((()=>[_(V)])),append:h((()=>[b(I)?(f(),g("div",{key:1,innerHTML:b(I),class:"cursor-pointer",onClick:t[2]||(t[2]=(...e)=>b(H)&&b(H)(...e))},null,8,L)):(f(),j(q,{key:0,height:"30",width:"80",preview:!1,src:`${b(I)}`},{"error-icon":h((()=>[_(k,{size:10})])),_:1},8,["src"]))])),_:1},8,["modelValue"])])),_:1}),_($,{size:16,direction:"vertical"},{default:h((()=>[v("div",D,[_(S,{modelValue:b(B).rememberUsername,"onUpdate:modelValue":t[4]||(t[4]=e=>b(B).rememberUsername=e)},{default:h((()=>t[5]||(t[5]=[x("记住账号")]))),_:1},8,["modelValue"])]),_(T,{type:"primary","html-type":"submit",long:"",loading:b(C)},{default:h((()=>t[6]||(t[6]=[x("登录")]))),_:1},8,["loading"])])),_:1})])),_:1},8,["model"])])}}}),[["__scopeId","data-v-f0e49136"]]),B={class:"login-page"},H={class:"login-container"},M={class:"login-form"},R={class:"footer"},S=C(u({__name:"index",setup:e=>(e,a)=>(f(),g("div",B,[v("div",H,[v("div",M,[_(F)])]),v("div",R,[_(I)])]))}),[["__scopeId","data-v-62bbe3af"]]);export{S as default};

import{_ as e,I as a,a as l,g as t,i as o,C as r,h as n,c as u,ao as s,s as i,f as v}from"./index-D-8JbLQk.js";import{R as d}from"./resize-observer-Dtogi-DJ.js";import{l as p,o as c}from"./index-DDFSMqsG.js";import{p as m}from"./pick-Ccd8Sfcm.js";import{u as h}from"./index-DGtjsHgS.js";import{d as f,t as g,r as x,c as b,w,o as y,f as C,j as z,k as S,l as L,O as R,J as j,p as B,A as I,z as $,q as O,v as V,m as A,n as F}from"./vue-D-10XvVk.js";const M=["border-width","box-sizing","font-family","font-weight","font-size","font-variant","letter-spacing","line-height","padding-top","padding-bottom","padding-left","padding-right","text-indent","text-rendering","text-transform","white-space","overflow-wrap","width"];var T=Object.defineProperty,k=Object.getOwnPropertySymbols,H=Object.prototype.hasOwnProperty,N=Object.prototype.propertyIsEnumerable,D=(e,a,l)=>a in e?T(e,a,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[a]=l,P=(e,a)=>{for(var l in a||(a={}))H.call(a,l)&&D(e,l,a[l]);if(k)for(var l of k(a))N.call(a,l)&&D(e,l,a[l]);return e};const W=f({name:"Textarea",components:{ResizeObserver:d,IconHover:a,IconClose:l},inheritAttrs:!1,props:{modelValue:String,defaultValue:{type:String,default:""},placeholder:String,disabled:{type:Boolean,default:!1},error:{type:Boolean,default:!1},maxLength:{type:[Number,Object],default:0},showWordLimit:{type:Boolean,default:!1},allowClear:{type:Boolean,default:!1},autoSize:{type:[Boolean,Object],default:!1},wordLength:{type:Function},wordSlice:{type:Function},textareaAttrs:{type:Object}},emits:{"update:modelValue":e=>!0,input:(e,a)=>!0,change:(e,a)=>!0,clear:e=>!0,focus:e=>!0,blur:e=>!0},setup(e,{emit:a,attrs:l}){const{disabled:i,error:v,modelValue:d}=g(e),f=t("textarea"),{mergedDisabled:C,mergedError:z,eventHandlers:S}=h({disabled:i,error:v}),L=x(),R=x(),j=x(),B=x(),I=x(e.defaultValue),$=b((()=>{var e;return null!=(e=d.value)?e:I.value})),[O,V]=p(L);w(d,(e=>{(o(e)||r(e))&&(I.value="")}));const A=b((()=>n(e.maxLength)&&Boolean(e.maxLength.errorOnly))),T=b((()=>n(e.maxLength)?e.maxLength.length:e.maxLength)),k=a=>{var l;return u(e.wordLength)?e.wordLength(a):null!=(l=a.length)?l:0},H=b((()=>k($.value))),N=b((()=>z.value||Boolean(T.value&&A.value&&H.value>T.value))),D=x(!1),W=x(!1),E=b((()=>e.allowClear&&!C.value&&$.value)),_=x(!1),q=x(""),J=()=>{O(),F((()=>{L.value&&$.value!==L.value.value&&(L.value.value=$.value,V())}))},G=(l,t=!0)=>{var o,r;T.value&&!A.value&&k(l)>T.value&&(l=null!=(r=null==(o=e.wordSlice)?void 0:o.call(e,l,T.value))?r:l.slice(0,T.value)),I.value=l,t&&a("update:modelValue",l),J()};let K=$.value;const Q=(e,l)=>{var t,o;e!==K&&(K=e,a("change",e,l),null==(o=null==(t=S.value)?void 0:t.onChange)||o.call(t,l))};w(d,(e=>{e!==$.value&&G(null!=e?e:"",!1)}));const U=e=>m(l,s),X=U(),Y=b((()=>{const a=P(P({},X),e.textareaAttrs);return N.value&&(a["aria-invalid"]=!0),a})),Z=b((()=>[`${f}-wrapper`,{[`${f}-focus`]:W.value,[`${f}-disabled`]:C.value,[`${f}-error`]:N.value,[`${f}-scroll`]:D.value}]));let ee;const ae=x(0),le=x(0),te=b((()=>n(e.autoSize)&&e.autoSize.minRows?e.autoSize.minRows*ae.value+le.value:0)),oe=b((()=>n(e.autoSize)&&e.autoSize.maxRows?e.autoSize.maxRows*ae.value+le.value:0)),re=()=>{const e=(e=>{const a={};return M.forEach((l=>{a[l]=e.getPropertyValue(l)})),a})(ee);ae.value=Number.parseInt(e["line-height"]||0,10),le.value=2*Number.parseInt(e["border-width"]||0,10)+Number.parseInt(e["padding-top"]||0,10)+Number.parseInt(e["padding-bottom"]||0,10),B.value=e,F((()=>{var e;const a=null==(e=j.value)?void 0:e.offsetHeight;let l=null!=a?a:0,t="hidden";te.value&&l<te.value&&(l=te.value),oe.value&&l>oe.value&&(l=oe.value,t="auto"),R.value={height:`${l}px`,resize:"none",overflow:t}}))};y((()=>{L.value&&(ee=window.getComputedStyle(L.value),e.autoSize&&re()),ne()}));const ne=()=>{L.value&&(L.value.scrollHeight>L.value.offsetHeight?D.value||(D.value=!0):D.value&&(D.value=!1))};return w($,(()=>{e.autoSize&&j.value&&re(),ne()})),{prefixCls:f,wrapperCls:Z,textareaRef:L,textareaStyle:R,mirrorRef:j,mirrorStyle:B,computedValue:$,showClearBtn:E,valueLength:H,computedMaxLength:T,mergedDisabled:C,mergeTextareaAttrs:Y,getWrapperAttrs:e=>c(l,s),getTextareaAttrs:U,handleInput:e=>{var l,t;const{value:o}=e.target;if(_.value)q.value=o;else{if(T.value&&!A.value&&$.value.length>=T.value&&k(o)>T.value&&"insertText"===e.inputType)return void J();a("input",o,e),G(o),null==(t=null==(l=S.value)?void 0:l.onInput)||t.call(l,e)}},handleFocus:e=>{var l,t;W.value=!0,K=$.value,a("focus",e),null==(t=null==(l=S.value)?void 0:l.onFocus)||t.call(l,e)},handleBlur:e=>{var l,t;W.value=!1,a("blur",e),null==(t=null==(l=S.value)?void 0:l.onBlur)||t.call(l,e),Q($.value,e)},handleComposition:e=>{var l,t;const{value:o}=e.target;if("compositionend"===e.type){if(_.value=!1,q.value="",T.value&&!A.value&&$.value.length>=T.value&&k(o)>T.value)return void J();a("input",o,e),G(o),null==(t=null==(l=S.value)?void 0:l.onInput)||t.call(l,e)}else _.value=!0},handleClear:e=>{G(""),Q("",e),a("clear",e)},handleResize:()=>{e.autoSize&&j.value&&re(),ne()},handleMousedown:e=>{L.value&&e.target!==L.value&&(e.preventDefault(),L.value.focus())}}},methods:{focus(){var e;null==(e=this.$refs.textareaRef)||e.focus()},blur(){var e;null==(e=this.$refs.textareaRef)||e.blur()}}}),E=["disabled","value","placeholder"];var _=e(W,[["render",function(e,a,l,t,o,r){const n=C("resize-observer"),u=C("icon-close"),s=C("icon-hover");return z(),S("div",V(e.getWrapperAttrs(e.$attrs),{class:e.wrapperCls,onMousedown:a[7]||(a[7]=(...a)=>e.handleMousedown&&e.handleMousedown(...a))}),[e.autoSize?(z(),S("div",{key:0,ref:"mirrorRef",class:L(`${e.prefixCls}-mirror`),style:R(e.mirrorStyle)},j(`${e.computedValue}\n`),7)):B("v-if",!0),I(n,{onResize:e.handleResize},{default:$((()=>[O("textarea",V({ref:"textareaRef"},e.mergeTextareaAttrs,{disabled:e.mergedDisabled,class:e.prefixCls,style:e.textareaStyle,value:e.computedValue,placeholder:e.placeholder,onInput:a[0]||(a[0]=(...a)=>e.handleInput&&e.handleInput(...a)),onFocus:a[1]||(a[1]=(...a)=>e.handleFocus&&e.handleFocus(...a)),onBlur:a[2]||(a[2]=(...a)=>e.handleBlur&&e.handleBlur(...a)),onCompositionstart:a[3]||(a[3]=(...a)=>e.handleComposition&&e.handleComposition(...a)),onCompositionupdate:a[4]||(a[4]=(...a)=>e.handleComposition&&e.handleComposition(...a)),onCompositionend:a[5]||(a[5]=(...a)=>e.handleComposition&&e.handleComposition(...a))}),null,16,E)])),_:1},8,["onResize"]),A(e.$slots,"suffix"),e.computedMaxLength&&e.showWordLimit?(z(),S("div",{key:1,class:L(`${e.prefixCls}-word-limit`)},j(e.valueLength)+"/"+j(e.computedMaxLength),3)):B("v-if",!0),e.showClearBtn?(z(),S("div",{key:2,class:L(`${e.prefixCls}-clear-btn`),onClick:a[6]||(a[6]=(...a)=>e.handleClear&&e.handleClear(...a))},[I(s,null,{default:$((()=>[I(u)])),_:1})],2)):B("v-if",!0)],16)}]]);const q=Object.assign(_,{install:(e,a)=>{i(e,a);const l=v(a);e.component(l+_.name,_)}});export{q as T};

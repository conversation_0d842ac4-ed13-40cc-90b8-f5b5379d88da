import{O as t,Q as a,R as e,S as o,T as r,N as d}from"./index-D-8JbLQk.js";const m=a=>t({url:"/admin/platform/store/list",method:"post",data:a}),s=a=>t({url:"/admin/platform/store/add",method:"put",data:a}),l=a=>t({url:`/admin/platform/store/detail/${a}`,method:"get"}),n=(a,e)=>t({url:`/admin/platform/store/edit/${a}`,method:"put",data:e}),p=a=>t({url:`/admin/platform/store/del/${a}`,method:"delete"}),i=(a,e)=>t({url:`/admin/platform/store/approve/${a}`,method:"put",data:e}),u=()=>new Promise(((t,m)=>{a({url:e+"/admin/platform/store/exports",method:"post",responseType:"blob",headers:{Authorization:"Bearer "+o()}}).then((a=>{"application/vnd.openxmlformats"===a.data.type?(r(a,"application/vnd.openxmlformats;charset=utf-8","店铺信息.xlsx"),t(!0)):(d.warning({title:"下载出错",content:"请稍后重试",duration:3e3}),m())})).catch((()=>{m()}))})),h=()=>t({url:"/admin/platform/store/recommendList",method:"get"}),f=a=>t({url:"/admin/platform/store/addRecommend",method:"put",data:a}),c=a=>t({url:`/admin/platform/store/delRecommend/${a}`,method:"delete"}),x=(a,e)=>t({url:`/admin/platform/store/setRecommendSortIndex/${a}`,method:"put",data:{recommendSortIndex:e}});export{f as a,c as b,x as c,m as d,s as e,l as f,n as g,p as h,i,u as j,h as r};

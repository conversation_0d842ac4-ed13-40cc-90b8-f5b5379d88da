import{u as e}from"./index-DOhy6BH_.js";import{B as l,u as t}from"./index-DGtjsHgS.js";import"./index-DD6vSYIM.js";import{a as o,I as i}from"./index-CuYx5vtf.js";import{S as n}from"./index-CdWxsKz_.js";import{r}from"./apiUpload-DpATemHF.js";import{_ as s,aj as a,g as u,h as d,aI as c,aJ as p,s as v,f,aK as m,c as y,ae as g,X as h,V as w,aL as b,aM as k,aN as x,I as $,aO as C,aP as B,aQ as S,aR as I,aS as j,aT as T,ad as L,aq as O,e as P}from"./index-D-8JbLQk.js";import{i as U,a as z,e as F}from"./index-DDFSMqsG.js";import{d as W,c as R,f as N,j as E,k as _,q,l as D,O as A,m as G,I as K,J as M,y as V,p as H,F as J,L as X,t as Q,D as Z,z as Y,C as ee,E as le,r as te,A as oe,v as ie,i as ne,T as re,w as se,a3 as ae,B as ue,al as de,am as ce,u as pe}from"./vue-D-10XvVk.js";import{_ as ve}from"./_plugin-vue_export-helper-BCo6x5W8.js";var fe=Object.defineProperty,me=Object.getOwnPropertySymbols,ye=Object.prototype.hasOwnProperty,ge=Object.prototype.propertyIsEnumerable,he=(e,l,t)=>l in e?fe(e,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[l]=t;const we={small:3,medium:4,large:8},be=W({name:"ProgressLine",components:{IconExclamationCircleFill:a},props:{percent:{type:Number,default:0},animation:{type:Boolean,default:!1},size:{type:String,default:"medium"},strokeWidth:{type:Number,default:4},width:{type:[Number,String],default:"100%"},color:{type:[String,Object],default:void 0},trackColor:String,formatText:{type:Function,default:void 0},status:{type:String},showText:Boolean},setup(e){const l=u("progress-line"),t=R((()=>4!==e.strokeWidth?e.strokeWidth:we[e.size])),o=R((()=>`${U.times(e.percent,100)}%`));return{prefixCls:l,style:R((()=>({width:e.width,height:`${t.value}px`,backgroundColor:e.trackColor}))),barStyle:R((()=>((e,l)=>{for(var t in l||(l={}))ye.call(l,t)&&he(e,t,l[t]);if(me)for(var t of me(l))ge.call(l,t)&&he(e,t,l[t]);return e})({width:100*e.percent+"%"},(e=>{if(e)return d(e)?{backgroundImage:`linear-gradient(to right, ${Object.keys(e).map((l=>`${e[l]} ${l}`)).join(",")})`}:{backgroundColor:e}})(e.color)))),text:o}}}),ke=["aria-valuenow"];var xe=s(be,[["render",function(e,l,t,o,i,n){const r=N("icon-exclamation-circle-fill");return E(),_("div",{role:"progressbar","aria-valuemin":"0","aria-valuemax":"100","aria-valuenow":e.percent,class:D(`${e.prefixCls}-wrapper`)},[q("div",{class:D(e.prefixCls),style:A(e.style)},[q("div",{class:D(`${e.prefixCls}-bar-buffer`)},null,2),q("div",{class:D([`${e.prefixCls}-bar`]),style:A(e.barStyle)},null,6)],6),e.showText?(E(),_("div",{key:0,class:D(`${e.prefixCls}-text`)},[G(e.$slots,"text",{percent:e.percent},(()=>[K(M(e.text)+" ",1),"danger"===e.status?(E(),V(r,{key:0})):H("v-if",!0)]))],2)):H("v-if",!0)],10,ke)}]]);let $e=0;const Ce={mini:16,small:48,medium:64,large:80},Be={mini:4,small:3,medium:4,large:4},Se=W({name:"ProgressCircle",components:{IconExclamation:c,IconCheck:p},props:{percent:{type:Number,default:0},type:{type:String},size:{type:String,default:"medium"},strokeWidth:{type:Number},width:{type:Number,default:void 0},color:{type:[String,Object],default:void 0},trackColor:String,status:{type:String,default:void 0},showText:{type:Boolean,default:!0},pathStrokeWidth:{type:Number}},setup(e){const l=u("progress-circle"),t=d(e.color),o=R((()=>{var l;return null!=(l=e.width)?l:Ce[e.size]})),i=R((()=>{var l;return null!=(l=e.strokeWidth)?l:"mini"===e.size?o.value/2:Be[e.size]})),n=R((()=>{var l;return null!=(l=e.pathStrokeWidth)?l:"mini"===e.size?i.value:Math.max(2,i.value-2)})),r=R((()=>(o.value-i.value)/2)),s=R((()=>2*Math.PI*r.value)),a=R((()=>o.value/2)),c=R((()=>($e+=1,`${l}-linear-gradient-${$e}`))),p=R((()=>`${U.times(e.percent,100)}%`));return{prefixCls:l,isLinearGradient:t,radius:r,text:p,perimeter:s,center:a,mergedWidth:o,mergedStrokeWidth:i,mergedPathStrokeWidth:n,linearGradientId:c}}}),Ie=["aria-valuenow"],je=["viewBox"],Te={key:0},Le=["id"],Oe=["offset","stop-color"],Pe=["cx","cy","r","stroke-width"],Ue=["cx","cy","r","stroke-width"];var ze=s(Se,[["render",function(e,l,t,o,i,n){const r=N("icon-check"),s=N("icon-exclamation");return E(),_("div",{role:"progressbar","aria-valuemin":"0","aria-valuemax":"100","aria-valuenow":e.percent,class:D(`${e.prefixCls}-wrapper`),style:A({width:`${e.mergedWidth}px`,height:`${e.mergedWidth}px`})},["circle"===e.type&&"mini"===e.size&&"success"===e.status?(E(),V(r,{key:0,style:A({fontSize:e.mergedWidth-2,color:e.color})},null,8,["style"])):(E(),_("svg",{key:1,viewBox:`0 0 ${e.mergedWidth} ${e.mergedWidth}`,class:D(`${e.prefixCls}-svg`)},[e.isLinearGradient?(E(),_("defs",Te,[q("linearGradient",{id:e.linearGradientId,x1:"0",y1:"1",x2:"0",y2:"0"},[(E(!0),_(J,null,X(Object.keys(e.color),(l=>(E(),_("stop",{key:l,offset:l,"stop-color":e.color[l]},null,8,Oe)))),128))],8,Le)])):H("v-if",!0),q("circle",{class:D(`${e.prefixCls}-bg`),fill:"none",cx:e.center,cy:e.center,r:e.radius,"stroke-width":e.mergedPathStrokeWidth,style:A({stroke:e.trackColor})},null,14,Pe),q("circle",{class:D(`${e.prefixCls}-bar`),fill:"none",cx:e.center,cy:e.center,r:e.radius,"stroke-width":e.mergedStrokeWidth,style:A({stroke:e.isLinearGradient?`url(#${e.linearGradientId})`:e.color,strokeDasharray:e.perimeter,strokeDashoffset:(e.percent>=1?0:1-e.percent)*e.perimeter})},null,14,Ue)],10,je)),e.showText&&"mini"!==e.size?(E(),_("div",{key:2,class:D(`${e.prefixCls}-text`)},[G(e.$slots,"text",{percent:e.percent},(()=>["danger"===e.status?(E(),V(s,{key:0})):"success"===e.status?(E(),V(r,{key:1})):(E(),_(J,{key:2},[K(M(e.text),1)],2112))]))],2)):H("v-if",!0)],14,Ie)}]]);const Fe=W({name:"ProgressSteps",components:{IconExclamationCircleFill:a},props:{steps:{type:Number,default:0},percent:{type:Number,default:0},size:{type:String},color:{type:[String,Object],default:void 0},trackColor:String,strokeWidth:{type:Number},status:{type:String,default:void 0},showText:{type:Boolean,default:!0}},setup(e){const l=u("progress-steps"),t=R((()=>{var l;return(null!=(l=e.strokeWidth)?l:"small"===e.size)?8:4}));return{prefixCls:l,stepList:R((()=>[...Array(e.steps)].map(((l,t)=>e.percent>0&&e.percent>1/e.steps*t)))),mergedStrokeWidth:t,text:R((()=>`${U.times(e.percent,100)}%`))}}}),We=["aria-valuenow"];var Re=s(W({name:"Progress",components:{ProgressLine:xe,ProgressCircle:ze,ProgressSteps:s(Fe,[["render",function(e,l,t,o,i,n){const r=N("icon-exclamation-circle-fill");return E(),_("div",{role:"progressbar","aria-valuemin":"0","aria-valuemax":"100","aria-valuenow":e.percent,class:D(`${e.prefixCls}-wrapper`)},[q("div",{class:D(e.prefixCls),style:A({height:`${e.mergedStrokeWidth}px`})},[(E(!0),_(J,null,X(e.stepList,((l,t)=>(E(),_("div",{key:t,class:D([`${e.prefixCls}-item`,{[`${e.prefixCls}-item-active`]:l}]),style:A({backgroundColor:l?e.color:e.trackColor})},null,6)))),128))],6),e.showText?(E(),_("div",{key:0,class:D(`${e.prefixCls}-text`)},[G(e.$slots,"text",{percent:e.percent},(()=>[K(M(e.text)+" ",1),"danger"===e.status?(E(),V(r,{key:0})):H("v-if",!0)]))],2)):H("v-if",!0)],10,We)}]])},props:{type:{type:String,default:"line"},size:{type:String},percent:{type:Number,default:0},steps:{type:Number,default:0},animation:{type:Boolean,default:!1},strokeWidth:{type:Number},width:{type:[Number,String]},color:{type:[String,Object]},trackColor:String,bufferColor:{type:[String,Object]},showText:{type:Boolean,default:!0},status:{type:String}},setup(l){const t=u("progress"),{size:o}=Q(l),i=R((()=>l.steps>0?"steps":l.type)),n=R((()=>l.status||(l.percent>=1?"success":"normal"))),{mergedSize:r}=e(o);return{cls:R((()=>[t,`${t}-type-${i.value}`,`${t}-size-${r.value}`,`${t}-status-${n.value}`])),computedStatus:n,mergedSize:r}}}),[["render",function(e,l,t,o,i,n){const r=N("progress-steps"),s=N("progress-line"),a=N("progress-circle");return E(),_("div",{class:D(e.cls)},[e.steps>0?(E(),V(r,{key:0,"stroke-width":e.strokeWidth,percent:e.percent,color:e.color,"track-color":e.trackColor,width:e.width,steps:e.steps,size:e.mergedSize,"show-text":e.showText},Z({_:2},[e.$slots.text?{name:"text",fn:Y((l=>[G(e.$slots,"text",ee(le(l)))]))}:void 0]),1032,["stroke-width","percent","color","track-color","width","steps","size","show-text"])):"line"===e.type&&"mini"!==e.mergedSize?(E(),V(s,{key:1,"stroke-width":e.strokeWidth,animation:e.animation,percent:e.percent,color:e.color,"track-color":e.trackColor,size:e.mergedSize,"buffer-color":e.bufferColor,width:e.width,"show-text":e.showText,status:e.computedStatus},Z({_:2},[e.$slots.text?{name:"text",fn:Y((l=>[G(e.$slots,"text",ee(le(l)))]))}:void 0]),1032,["stroke-width","animation","percent","color","track-color","size","buffer-color","width","show-text","status"])):(E(),V(a,{key:2,type:e.type,"stroke-width":"line"===e.type?e.strokeWidth||4:e.strokeWidth,"path-stroke-width":"line"===e.type?e.strokeWidth||4:e.strokeWidth,width:e.width,percent:e.percent,color:e.color,"track-color":e.trackColor,size:e.mergedSize,"show-text":e.showText,status:e.computedStatus},Z({_:2},[e.$slots.text?{name:"text",fn:Y((l=>[G(e.$slots,"text",ee(le(l)))]))}:void 0]),1032,["type","stroke-width","path-stroke-width","width","percent","color","track-color","size","show-text","status"]))],2)}]]);const Ne=Object.assign(Re,{install:(e,l)=>{v(e,l);const t=f(l);e.component(t+Re.name,Re)}}),Ee=e=>{const l=e.responseText||e.response;if(!l)return;const t=e.getResponseHeader("Content-Type");if(t&&t.includes("json"))try{return JSON.parse(l)}catch{return l}return l},_e=(e,l)=>y(e)?e(l):e,qe=(e,l)=>{if(l&&e){const t=g(l)?l:l.split(",").map((e=>e.trim())).filter((e=>e)),o=(e.name.indexOf(".")>-1?`.${e.name.split(".").pop()}`:"").toLowerCase();return t.some((l=>{const t=l&&l.toLowerCase(),i=(e.type||"").toLowerCase(),n=i.split("/")[0];if(t===i||`${n}${o.replace(".","/")}`===t)return!0;if(/^\*(\/\*)?$/.test(t))return!0;if(/\/\*/.test(t))return i.replace(/\/.*$/,"")===t.replace(/\/.*$/,"");if(/\..*/.test(t)){let e=[t];return".jpg"!==t&&".jpeg"!==t||(e=[".jpg",".jpeg"]),e.indexOf(o)>-1}return!1}))}return!!e},De=(e,l)=>{if(!e)return[];const t=Array.from(e);return l?t.filter((e=>qe(e,l))):t};var Ae=W({name:"UploadButton",props:{disabled:{type:Boolean,default:!1},directory:{type:Boolean,default:!1},accept:String,listType:{type:String},tip:String,draggable:{type:Boolean,default:!1},multiple:{type:Boolean,default:!1},uploadFiles:{type:Function,required:!0},hide:Boolean,onButtonClick:{type:Function}},setup(e,{slots:t}){const o=u("upload"),{t:i}=z(),n=te(!1),r=te(null),s=te(null),a=te(0),d=e=>{"subtract"===e?a.value-=1:"add"===e?a.value+=1:"reset"===e&&(a.value=0)},c=l=>{if(!e.disabled){if(y(e.onButtonClick)){const t=e.onButtonClick(l);if(h(t))return void t.then((l=>{e.uploadFiles(De(l))}))}r.value&&r.value.click()}},p=l=>{const t=l.target;t.files&&e.uploadFiles(De(t.files)),t.value=""},v=l=>{var t,o;if(l.preventDefault(),n.value=!1,d("reset"),!e.disabled)if(e.directory&&(null==(t=l.dataTransfer)?void 0:t.items))((e,l,t)=>{const o=[];let i=0;const n=()=>{!i&&t(o)},r=e=>{if(i+=1,null==e?void 0:e.isFile)e.file((t=>{i-=1,qe(t,l)&&(Object.defineProperty(t,"webkitRelativePath",{value:e.fullPath.replace(/^\//,"")}),o.push(t)),n()}));else if(null==e?void 0:e.isDirectory){const l=e.createReader();let t=!1;const o=()=>{l.readEntries((e=>{t||(i-=1,t=!0),0===e.length?n():(o(),e.forEach(r))}))};o()}else i-=1,n()};[].slice.call(e).forEach((e=>e.webkitGetAsEntry&&r(e.webkitGetAsEntry())))})(l.dataTransfer.items,e.accept,(l=>{e.uploadFiles(l)}));else{const t=De(null==(o=l.dataTransfer)?void 0:o.files,e.accept);e.uploadFiles(e.multiple?t:t.slice(0,1))}},f=e=>{e.preventDefault(),d("subtract"),0===a.value&&(n.value=!1,d("reset"))},m=l=>{l.preventDefault(),e.disabled||n.value||(n.value=!0)},g=R((()=>[o,{[`${o}-type-picture-card`]:"picture-card"===e.listType,[`${o}-draggable`]:e.draggable,[`${o}-disabled`]:e.disabled,[`${o}-hide`]:e.hide}]));return()=>oe("span",{ref:s,class:g.value,onClick:c,onDragenter:()=>{d("add")},onDrop:v,onDragover:m,onDragleave:f},[oe("input",ie({ref:r,type:"file",style:{display:"none"},disabled:e.disabled,accept:e.accept,multiple:e.multiple},e.directory?{webkitdirectory:"webkitdirectory"}:{},{onChange:p}),null),t.default?oe("span",null,[t.default()]):"picture-card"===e.listType?oe("div",{class:`${o}-picture-card`},[oe("div",{class:`${o}-picture-card-text`},[oe(w,null,null)]),e.tip&&oe("div",{class:`${o}-tip`},[e.tip])]):e.draggable?oe("div",{class:[`${o}-drag`,{[`${o}-drag-active`]:n.value}]},[oe("div",null,[oe(w,null,null)]),oe("div",{class:`${o}-drag-text`},[n.value?i("upload.dragHover"):i("upload.drag")]),e.tip&&oe("div",{class:`${o}-tip`},[e.tip])]):oe(l,{type:"primary",disabled:e.disabled},{default:()=>[i("upload.buttonText")],icon:()=>oe(b,null,null)})])}});const Ge=Symbol("ArcoUpload");var Ke=W({name:"UploadProgress",props:{file:{type:Object,required:!0},listType:{type:String,required:!0}},setup(e){const l=u("upload-progress"),{t:t}=z(),o=ne(Ge,void 0),i=()=>{var l;if(["init","uploading"].includes(null!=(l=e.file.status)?l:"")){const l=(e=>{switch(e){case"done":return"success";case"error":return"danger";default:return"normal"}})(e.file.status);return oe(Ne,{type:"circle",size:"mini",showText:!1,status:l,percent:e.file.percent},null)}return null};return()=>{return oe("span",{class:l},[i(),"error"===e.file.status?oe("span",{class:[null==o?void 0:o.iconCls,`${null==o?void 0:o.iconCls}-upload`],onClick:()=>null==o?void 0:o.onUpload(e.file)},[(null==o?void 0:o.showRetryButton)&&(null!=(u=null==(r=null==o?void 0:(n=o.slots)["retry-icon"])?void 0:r.call(n))?u:null==(a=null==(s=null==o?void 0:o.customIcon)?void 0:s.retryIcon)?void 0:a.call(s))||"picture-card"===e.listType?oe(b,null,null):t("upload.retry")]):"done"===e.file.status?oe("span",{class:[null==o?void 0:o.iconCls,`${null==o?void 0:o.iconCls}-success`]},[null!=(y=null!=(m=null==(c=null==o?void 0:(d=o.slots)["success-icon"])?void 0:c.call(d))?m:null==(f=null==(v=null==o?void 0:o.customIcon)?void 0:v.successIcon)?void 0:f.call(v))?y:oe(p,null,null)]):"init"===e.file.status?oe(F,{content:t("upload.start")},{default:()=>{var l,t,i,n,r,s;return[oe("span",{class:[null==o?void 0:o.iconCls,`${null==o?void 0:o.iconCls}-start`],onClick:()=>null==o?void 0:o.onUpload(e.file)},[null!=(s=null!=(r=null==(t=null==o?void 0:(l=o.slots)["start-icon"])?void 0:t.call(l))?r:null==(n=null==(i=null==o?void 0:o.customIcon)?void 0:i.startIcon)?void 0:n.call(i))?s:oe(k,null,null)])]}}):(null==o?void 0:o.showCancelButton)&&oe(F,{content:t("upload.cancel")},{default:()=>{var l,t,i,n,r,s;return[oe("span",{class:[null==o?void 0:o.iconCls,`${null==o?void 0:o.iconCls}-cancel`],onClick:()=>null==o?void 0:o.onAbort(e.file)},[null!=(s=null!=(r=null==(t=null==o?void 0:(l=o.slots)["cancel-icon"])?void 0:t.call(l))?r:null==(n=null==(i=null==o?void 0:o.customIcon)?void 0:i.cancelIcon)?void 0:n.call(i))?s:oe(x,null,null)])]}})]);var n,r,s,a,u,d,c,v,f,m,y}}}),Me=W({name:"UploadListItem",props:{file:{type:Object,required:!0},listType:{type:String,required:!0}},setup(e){const l=`${u("upload-list")}-item`,{t:t}=z(),o=ne(Ge,void 0),i=()=>{var l,t;let o="";if(e.file.file&&e.file.file.type)o=e.file.file.type;else{const i=null!=(t=null==(l=e.file.name)?void 0:l.split(".")[1])?t:"";["png","jpg","jpeg","bmp","gif","webp"].includes(i)?o="image":["mp4","m2v","mkv","m4v","mov"].includes(i)?o="video":["mp3","wav","wmv","m4a","acc","flac"].includes(i)&&(o="audio")}return o.includes("image")?oe(B,null,null):o.includes("pdf")?oe(S,null,null):o.includes("audio")?oe(I,null,null):o.includes("video")?oe(j,null,null):oe(T,null,null)};return()=>{var n,r,s,u,d,c,p,v,f,m,y,g,h,w,b,k,x,B,S,I,j,T,L;return oe("div",{class:[l,`${l}-${e.file.status}`]},[oe("div",{class:`${l}-content`},["picture"===(null==o?void 0:o.listType)&&oe("span",{class:`${l}-thumbnail`},[null!=(s=null==(r=null==o?void 0:(n=o.slots).image)?void 0:r.call(n,{fileItem:e.file}))?s:oe("img",ie({src:e.file.url,alt:e.file.name},(null==o?void 0:o.imageLoading)?{loading:o.imageLoading}:void 0),null)]),oe("div",{class:`${l}-name`},["text"===(null==o?void 0:o.listType)&&oe("span",{class:`${l}-file-icon`},[null!=(f=null!=(v=null==(d=null==o?void 0:(u=o.slots)["file-icon"])?void 0:d.call(u,{fileItem:e.file}))?v:null==(p=null==(c=null==o?void 0:o.customIcon)?void 0:c.fileIcon)?void 0:p.call(c,e.file))?f:i()]),(null==o?void 0:o.showLink)&&e.file.url?oe("a",ie({class:`${l}-name-link`,target:"_blank",href:e.file.url},(null==o?void 0:o.download)?{download:e.file.name}:void 0),[null!=(b=null!=(w=null==(y=null==o?void 0:(m=o.slots)["file-name"])?void 0:y.call(m,{fileItem:e.file}))?w:null==(h=null==(g=null==o?void 0:o.customIcon)?void 0:g.fileName)?void 0:h.call(g,e.file))?b:e.file.name]):oe("span",{class:`${l}-name-text`,onClick:()=>null==o?void 0:o.onPreview(e.file)},[null!=(j=null!=(I=null==(x=null==o?void 0:(k=o.slots)["file-name"])?void 0:x.call(k,{fileItem:e.file}))?I:null==(S=null==(B=null==o?void 0:o.customIcon)?void 0:B.fileName)?void 0:S.call(B,e.file))?j:e.file.name]),"error"===e.file.status&&oe(F,{content:t("upload.error")},{default:()=>{var e,l,t,i,n,r;return[oe("span",{class:[null==o?void 0:o.iconCls,`${null==o?void 0:o.iconCls}-error`]},[null!=(r=null!=(n=null==(l=null==o?void 0:(e=o.slots)["error-icon"])?void 0:l.call(e))?n:null==(i=null==(t=null==o?void 0:o.customIcon)?void 0:t.errorIcon)?void 0:i.call(t))?r:oe(a,null,null)])]}})]),oe(Ke,{file:e.file,listType:e.listType},null)]),(null==o?void 0:o.showRemoveButton)&&oe("span",{class:`${l}-operation`},[oe($,{onClick:()=>{var l;return null==(l=null==o?void 0:o.onRemove)?void 0:l.call(o,e.file)}},{default:()=>{var e,l,t,i,n,r;return[oe("span",{class:[null==o?void 0:o.iconCls,`${null==o?void 0:o.iconCls}-remove`]},[null!=(r=null!=(n=null==(l=null==o?void 0:(e=o.slots)["remove-icon"])?void 0:l.call(e))?n:null==(i=null==(t=null==o?void 0:o.customIcon)?void 0:t.removeIcon)?void 0:i.call(t))?r:oe(C,null,null)])]}})]),null==(L=null==o?void 0:(T=o.slots)["extra-button"])?void 0:L.call(T,{fileItem:e.file})])}}}),Ve=W({name:"UploadPictureItem",props:{file:{type:Object,required:!0},disabled:{type:Boolean,default:!1}},setup(e){const l=`${u("upload-list")}-picture`,t=R((()=>[l,{[`${l}-status-error`]:"error"===e.file.status}])),o=ne(Ge,void 0);return()=>{return oe("span",{class:t.value},["uploading"===e.file.status?oe(Ke,{file:e.file,listType:"picture-card"},null):oe(J,null,[null!=(r=null==(n=null==o?void 0:(i=o.slots).image)?void 0:n.call(i,{fileItem:e.file}))?r:oe("img",ie({src:e.file.url,alt:e.file.name},(null==o?void 0:o.imageLoading)?{loading:o.imageLoading}:void 0),null),oe("div",{class:`${l}-mask`},["error"===e.file.status&&(null==o?void 0:o.showCancelButton)&&oe("div",{class:`${l}-error-tip`},[oe("span",{class:[null==o?void 0:o.iconCls,`${null==o?void 0:o.iconCls}-error`]},[null!=(p=null!=(c=null==(a=null==o?void 0:(s=o.slots)["error-icon"])?void 0:a.call(s))?c:null==(d=null==(u=null==o?void 0:o.customIcon)?void 0:u.errorIcon)?void 0:d.call(u))?p:oe(L,null,null)])]),oe("div",{class:`${l}-operation`},["error"!==e.file.status&&(null==o?void 0:o.showPreviewButton)&&oe("span",{class:[null==o?void 0:o.iconCls,`${null==o?void 0:o.iconCls}-preview`],onClick:()=>null==o?void 0:o.onPreview(e.file)},[null!=(h=null!=(g=null==(f=null==o?void 0:(v=o.slots)["preview-icon"])?void 0:f.call(v))?g:null==(y=null==(m=null==o?void 0:o.customIcon)?void 0:m.previewIcon)?void 0:y.call(m))?h:oe(O,null,null)]),["init","error"].includes(e.file.status)&&(null==o?void 0:o.showRetryButton)&&oe("span",{class:[null==o?void 0:o.iconCls,`${null==o?void 0:o.iconCls}-upload`],onClick:()=>null==o?void 0:o.onUpload(e.file)},[null!=(S=null!=(B=null==(k=null==o?void 0:(w=o.slots)["retry-icon"])?void 0:k.call(w))?B:null==($=null==(x=null==o?void 0:o.customIcon)?void 0:x.retryIcon)?void 0:$.call(x))?S:oe(b,null,null)]),!(null==o?void 0:o.disabled)&&(null==o?void 0:o.showRemoveButton)&&oe("span",{class:[null==o?void 0:o.iconCls,`${null==o?void 0:o.iconCls}-remove`],onClick:()=>null==o?void 0:o.onRemove(e.file)},[null!=(z=null!=(U=null==(j=null==o?void 0:(I=o.slots)["remove-icon"])?void 0:j.call(I))?U:null==(P=null==(T=null==o?void 0:o.customIcon)?void 0:T.removeIcon)?void 0:P.call(T))?z:oe(C,null,null)]),null==(W=null==o?void 0:(F=o.slots)["extra-button"])?void 0:W.call(F,e.file)])])])]);var i,n,r,s,a,u,d,c,p,v,f,m,y,g,h,w,k,x,$,B,S,I,j,T,P,U,z,F,W}}}),He=W({name:"UploadList",components:{UploadListItem:Me,UploadPictureItem:Ve},props:{fileList:{type:Array,required:!0},listType:{type:String,required:!0}},setup(e,{slots:l}){const t=u("upload"),o=R((()=>[`${t}-list`,`${t}-list-type-${e.listType}`]));return()=>oe(re,{tag:"div",class:o.value},{default:()=>{var t;return[...e.fileList.map(((t,o)=>((t,o)=>y(l["upload-item"])?l["upload-item"]({fileItem:t,index:o}):"picture-card"===e.listType?oe(Ve,{file:t,key:`item-${o}`},null):oe(Me,{file:t,listType:e.listType,key:`item-${o}`},null))(t,o))),"picture-card"===e.listType&&(null==(t=l["upload-button"])?void 0:t.call(l))]}})}}),Je=Object.defineProperty,Xe=Object.defineProperties,Qe=Object.getOwnPropertyDescriptors,Ze=Object.getOwnPropertySymbols,Ye=Object.prototype.hasOwnProperty,el=Object.prototype.propertyIsEnumerable,ll=(e,l,t)=>l in e?Je(e,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[l]=t,tl=W({name:"Upload",props:{fileList:{type:Array,default:void 0},defaultFileList:{type:Array,default:()=>[]},accept:String,action:String,disabled:{type:Boolean,default:!1},multiple:{type:Boolean,default:!1},directory:{type:Boolean,default:!1},draggable:{type:Boolean,default:!1},tip:String,headers:{type:Object},data:{type:[Object,Function]},name:{type:[String,Function]},withCredentials:{type:Boolean,default:!1},customRequest:{type:Function},limit:{type:Number,default:0},autoUpload:{type:Boolean,default:!0},showFileList:{type:Boolean,default:!0},showRemoveButton:{type:Boolean,default:!0},showRetryButton:{type:Boolean,default:!0},showCancelButton:{type:Boolean,default:!0},showUploadButton:{type:[Boolean,Object],default:!0},showPreviewButton:{type:Boolean,default:!0},download:{type:Boolean,default:!1},showLink:{type:Boolean,default:!0},imageLoading:{type:String},listType:{type:String,default:"text"},responseUrlKey:{type:[String,Function]},customIcon:{type:Object},imagePreview:{type:Boolean,default:!1},onBeforeUpload:{type:Function},onBeforeRemove:{type:Function},onButtonClick:{type:Function}},emits:{"update:fileList":e=>!0,exceedLimit:(e,l)=>!0,change:(e,l)=>!0,progress:(e,l)=>!0,preview:e=>!0,success:e=>!0,error:e=>!0},setup(e,{emit:l,slots:i}){const{fileList:n,disabled:r,listType:s,customIcon:a,showRetryButton:c,showCancelButton:p,showRemoveButton:v,showPreviewButton:f,imageLoading:g,download:h,showLink:w}=Q(e),b=u("upload"),{mergedDisabled:k,eventHandlers:x}=t({disabled:r}),$=te([]),C=new Map,B=new Map,S=R((()=>e.limit>0&&$.value.length>=e.limit)),I=e=>{C.clear();const l=null==e?void 0:e.map(((e,l)=>{var t,o,i;const n=null!=(t=e.status)?t:"done",r=ue((s=((e,l)=>{for(var t in l||(l={}))Ye.call(l,t)&&ll(e,t,l[t]);if(Ze)for(var t of Ze(l))el.call(l,t)&&ll(e,t,l[t]);return e})({},e),a={uid:null!=(o=e.uid)?o:`${Date.now()}${l}`,status:n,percent:null!=(i=e.percent)?i:["error","init"].indexOf(n)>-1?0:1},Xe(s,Qe(a))));var s,a;return C.set(r.uid,r),r}));$.value=null!=l?l:[]};I(e.defaultFileList),se(n,(e=>{e&&I(e)}),{immediate:!0,deep:!0});const j=e=>{var t,o;l("update:fileList",$.value),l("change",$.value,e),null==(o=null==(t=x.value)?void 0:t.onChange)||o.call(t)},T=t=>{const o={fileItem:t,action:e.action,name:e.name,data:e.data,headers:e.headers,withCredentials:e.withCredentials,onProgress:(e,o)=>{const i=C.get(t.uid);i&&(i.status="uploading",i.percent=e,l("progress",i,o),j(i))},onSuccess:o=>{const i=C.get(t.uid);i&&(i.status="done",i.percent=1,i.response=o,e.responseUrlKey&&(y(e.responseUrlKey)?i.url=e.responseUrlKey(i):o[e.responseUrlKey]&&(i.url=o[e.responseUrlKey])),B.delete(i.uid),l("success",i),j(i))},onError:e=>{const o=C.get(t.uid);o&&(o.status="error",o.percent=0,o.response=e,B.delete(o.uid),l("error",o),j(o))}};t.status="uploading",t.percent=0;const i=y(e.customRequest)?e.customRequest(o):(({fileItem:e,action:l,name:t,data:o,headers:i={},withCredentials:n=!1,onProgress:r=m,onSuccess:s=m,onError:a=m})=>{const u=_e(t,e)||"file",d=_e(o,e),c=new XMLHttpRequest;n&&(c.withCredentials=!0),c.upload.onprogress=e=>{const l=e.total>0?U.round(e.loaded/e.total,2):0;r(l,e)},c.onerror=function(e){a(e)},c.onload=()=>{c.status<200||c.status>=300?a(Ee(c)):s(Ee(c))};const p=new FormData;if(d)for(const v of Object.keys(d))p.append(v,d[v]);e.file&&p.append(u,e.file),c.open("post",null!=l?l:"",!0);for(const v of Object.keys(i))c.setRequestHeader(v,i[v]);return c.send(p),{abort(){c.abort()}}})(o);B.set(t.uid,i),j(t)},L=e=>{var l;const t=B.get(e.uid);if(t){null==(l=t.abort)||l.call(t),B.delete(e.uid);const o=C.get(e.uid);o&&(o.status="error",o.percent=0,j(o))}},O=async(l,t)=>{const o=`${Date.now()}-${t}`,i=(e=>{var l;return null==(l=e.type)?void 0:l.includes("image")})(l)?URL.createObjectURL(l):void 0,n=ue({uid:o,file:l,url:i,name:l.name,status:"init",percent:0});C.set(o,n),$.value=[...$.value,n],j(n),e.autoUpload&&T(n)},z=t=>{if(e.limit>0&&$.value.length+t.length>e.limit)l("exceedLimit",$.value,t);else for(let l=0;l<t.length;l++){const o=t[l];y(e.onBeforeUpload)?Promise.resolve(e.onBeforeUpload(o)).then((e=>{e&&O(P(e)?o:e,l)})).catch((e=>{console.error(e)})):O(o,l)}},F=e=>{$.value=$.value.filter((l=>l.uid!==e.uid)),j(e)};ae(Ge,ue({disabled:k,listType:s,iconCls:`${b}-icon`,showRemoveButton:v,showRetryButton:c,showCancelButton:p,showPreviewButton:f,showLink:w,imageLoading:g,download:h,customIcon:a,slots:i,onUpload:T,onAbort:L,onRemove:l=>{y(e.onBeforeRemove)?Promise.resolve(e.onBeforeRemove(l)).then((e=>{e&&F(l)})).catch((e=>{console.error(e)})):F(l)},onPreview:t=>{if(e.imagePreview&&t.url){const e=A.value.indexOf(t.url);e>-1&&(_.value=e,E.value=!0)}l("preview",t)}}));const W=R((()=>e.accept?e.accept:"picture"===e.listType||"picture-card"===e.listType?"image/*":void 0)),N=()=>{const l=oe(Ae,{key:"arco-upload-button",disabled:k.value,draggable:e.draggable,listType:e.listType,uploadFiles:z,multiple:e.multiple,directory:e.directory,tip:e.tip,hide:!e.showUploadButton||S.value&&!(d(e.showUploadButton)&&e.showUploadButton.showOnExceedLimit),accept:W.value,onButtonClick:e.onButtonClick},{default:i["upload-button"]});return e.tip&&"picture-card"!==e.listType&&!e.draggable?oe("span",null,[l,oe("div",{class:`${b}-tip`},[e.tip])]):l},E=te(!1),_=te(0),q=e=>{_.value=e},D=e=>{E.value=e},A=R((()=>$.value.filter((e=>Boolean(e.url))).map((e=>e.url))));return{prefixCls:b,render:()=>e.showFileList?oe("div",{class:[`${b}-wrapper`,`${b}-wrapper-type-${e.listType}`]},[e.imagePreview&&A.value.length>0&&oe(o,{srcList:A.value,visible:E.value,current:_.value,onChange:q,onVisibleChange:D},null),"picture-card"!==e.listType&&e.showUploadButton&&N(),oe(He,{fileList:$.value,listType:e.listType},{"upload-button":N,"upload-item":i["upload-item"]})]):e.showUploadButton&&N(),innerSubmit:e=>{if(e){const l=C.get(e.uid);l&&T(l)}else for(const l of $.value)"init"===l.status&&T(l)},innerAbort:L,innerUpdateFile:(e,l)=>{for(const t of $.value)if(t.uid===e){t.file=l,j(t);break}},innerUpload:z}},methods:{submit(e){return this.innerSubmit(e)},abort(e){return this.innerAbort(e)},updateFile(e,l){return this.innerUpdateFile(e,l)},upload(e){return this.innerUpload(e)}},render(){return this.render()}});const ol=Object.assign(tl,{install:(e,l)=>{v(e,l);const t=f(l);e.component(t+tl.name,tl)}}),il={class:"upload-wrapper"},nl={key:0,class:"image-wrapper"},rl={class:"wrapper__mark"},sl={class:"upload-inner"},al={class:"mt-2 text-center"},ul={class:"upload-inner"},dl={class:"mt-2 text-center"},cl=ve(W({__name:"index",props:de({accept:{},title:{},disabled:{type:Boolean}},{modelValue:{},modelModifiers:{}}),emits:de(["uploadSuccess","uploadError"],["update:modelValue"]),setup(e,{emit:l}){const t=l,o=ce(e,"modelValue"),s=te(!1),a=e=>{u(e)},u=async e=>{const{onSuccess:l,onError:i,fileItem:n}=e;try{s.value=!0;const e=await r(n.file);o.value=e,s.value=!1,l(e),t("uploadSuccess",{url:e})}catch(a){t("uploadError"),s.value=!1,i({error:a})}},d=te(!1);return(e,l)=>{const t=i,r=N("icon-eye"),u=N("icon-delete"),c=N("icon-upload"),p=n,v=ol;return E(),_("div",il,[o.value?(E(),_("div",nl,[oe(t,{fit:"contain",src:o.value,"preview-visible":pe(d),onPreviewVisibleChange:l[0]||(l[0]=e=>d.value=!1)},null,8,["src","preview-visible"]),q("div",rl,[oe(r,{size:20,onClick:l[1]||(l[1]=e=>d.value=!0)}),e.disabled?H("",!0):(E(),V(u,{key:0,size:20,onClick:l[2]||(l[2]=e=>o.value=void 0)}))])])):pe(s)?(E(),V(p,{key:1,loading:pe(s),class:"!w-[120px]"},{default:Y((()=>[q("div",sl,[oe(c,{size:20}),q("span",al,M(e.title),1)])])),_:1},8,["loading"])):(E(),V(v,{key:2,accept:e.accept,limit:1,"show-file-list":!1,"custom-request":a},{"upload-button":Y((()=>[q("div",ul,[oe(c,{size:20}),q("span",dl,M(e.title),1)])])),_:1},8,["accept"]))])}}}),[["__scopeId","data-v-0ac15357"]]);export{ol as U,cl as _};

import{u as e}from"./index-DOhy6BH_.js";import{L as t,P as l}from"./index-CE4IsVo2.js";import{d as n,t as o,c as a,A as s,i,r,f as u,j as c,k as p,l as d,m,p as v,z as f,q as g,O as h,a3 as y,B as b,y as k,D as C,I as x,J as w,F as $,v as S,L as P,a4 as _,e as M,o as V,G as B,w as I,g as O,a5 as j,a6 as z,Z as N,a7 as D,Q as T,a8 as R,u as K,K as A,h as H,a9 as E,aa as L,ab as F,ac as W,ad as U,ae as G,a as q,af as Y,ag as X,ah as Z,b as J}from"./vue-D-10XvVk.js";import{B as Q,a as ee,u as te,S as le}from"./index-DGtjsHgS.js";import{D as ne}from"./index-DSEUEyxe.js";import{g as oe,s as ae,f as se,_ as ie,k as re,o as ue,p as ce,q as pe,r as de,l as me,t as ve,u as fe,v as ge,w as he,x as ye,i as be,y as ke,z as Ce,h as xe,A as we,B as $e,C as Se,c as Pe,N as _e,D as Me,E as Ve,F as Be,G as Ie,H as Oe,J as je}from"./index-D-8JbLQk.js";import{T as ze,b as Ne,o as De,d as Te,e as Re,f as Ke}from"./index-DDFSMqsG.js";/* empty css              */import{I as Ae}from"./index-DfEXMvnc.js";import{u as He,_ as Ee}from"./index.vue_vue_type_script_setup_true_lang-DVPVBV8s.js";import{_ as Le}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css              */import{r as Fe}from"./index-BEo1tUsK.js";import{s as We,F as Ue,a as Ge}from"./index-DVDXfQhn.js";import{u as qe}from"./useLoading-D5mh7tTu.js";import{M as Ye}from"./index-O7pr3qsq.js";import{S as Xe,E as Ze,u as Je}from"./index-DD6vSYIM.js";import{A as Qe}from"./index-CUtvFEc_.js";import{R as et}from"./render-function-CAXdZVZM.js";import{i as tt}from"./ResizeObserver.es-CzGuHLZU.js";import{R as lt}from"./resize-observer-Dtogi-DJ.js";import"./pick-Ccd8Sfcm.js";import"./use-index-D_ozg7PK.js";import"./index-CHOaln3D.js";var nt=Object.defineProperty,ot=Object.getOwnPropertySymbols,at=Object.prototype.hasOwnProperty,st=Object.prototype.propertyIsEnumerable,it=(e,t,l)=>t in e?nt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,rt=(e,t)=>{for(var l in t||(t={}))at.call(t,l)&&it(e,l,t[l]);if(ot)for(var l of ot(t))st.call(t,l)&&it(e,l,t[l]);return e};const ut=["red","orangered","orange","gold","lime","green","cyan","arcoblue","purple","pinkpurple","magenta","gray"],ct=["normal","processing","success","warning","danger"];var pt=n({name:"Badge",props:{text:{type:String},dot:{type:Boolean},dotStyle:{type:Object},maxCount:{type:Number,default:99},offset:{type:Array,default:()=>[]},color:{type:String},status:{type:String,validator:e=>ct.includes(e)},count:{type:Number}},setup(e,{slots:t}){const{status:l,color:n,dotStyle:i,offset:r,text:u,dot:c,maxCount:p,count:d}=o(e),m=oe("badge"),v=dt(m,null==l?void 0:l.value,null==t?void 0:t.default),f=a((()=>{const e=rt({},(null==i?void 0:i.value)||{}),[t,l]=(null==r?void 0:r.value)||[];t&&(e.marginRight=-t+"px"),l&&(e.marginTop=`${l}px`);const o=!(null==n?void 0:n.value)||ut.includes(null==n?void 0:n.value)?{}:{backgroundColor:n.value};return{mergedStyle:rt(rt({},o),e),computedDotStyle:e,computedColorStyle:o}})),g=()=>{const e=null==u?void 0:u.value,o=null==n?void 0:n.value,a=null==l?void 0:l.value,i=null==c?void 0:c.value,r=Number(null==d?void 0:d.value),v=null!=(null==d?void 0:d.value),{computedDotStyle:g,mergedStyle:h}=f.value;return t.content?s("span",{class:`${m}-custom-dot`,style:g},[t.content()]):!e||o||a?a||o&&!v?s("span",{class:`${m}-status-wrapper`},[s("span",{class:[`${m}-status-dot`,{[`${m}-status-${a}`]:a,[`${m}-color-${o}`]:o}],style:h},null),e&&s("span",{class:`${m}-status-text`},[e])]):(i||o)&&r>0?s("span",{class:[`${m}-dot`,{[`${m}-color-${o}`]:o}],style:h},null):0===r?null:s("span",{class:`${m}-number`,style:h},[s("span",null,[p.value&&r>p.value?`${p.value}+`:r])]):s("span",{class:`${m}-text`,style:g},[e])};return()=>s("span",{class:v.value},[t.default&&t.default(),g()])}});const dt=(e,t,l)=>a((()=>[e,{[`${e}-status`]:t,[`${e}-no-children`]:!l}])),mt=Object.assign(pt,{install:(e,t)=>{ae(e,t);const l=se(t);e.component(l+pt.name,pt)}}),vt=Symbol("ArcoBreadcrumb"),ft=Symbol("ArcoDropdown");var gt=ie(n({name:"DropdownPanel",components:{Scrollbar:Xe,Empty:Ze},props:{loading:{type:Boolean,default:!1},isEmpty:{type:Boolean,default:!1},bottomOffset:{type:Number,default:0},onScroll:{type:[Function,Array]},onReachBottom:{type:[Function,Array]}},emits:["scroll","reachBottom"],setup(e,{emit:t,slots:l}){const n=oe("dropdown"),o=i(ft,{}),s=r(),u=a((()=>re(o.popupMaxHeight)?{maxHeight:`${o.popupMaxHeight}px`}:o.popupMaxHeight?void 0:{maxHeight:"none",overflowY:"hidden"})),c=a((()=>[n,{[`${n}-has-footer`]:Boolean(l.footer)}]));return{prefixCls:n,cls:c,style:u,wrapperRef:s,handleScroll:l=>{const{scrollTop:n,scrollHeight:o,offsetHeight:a}=l.target;o-(n+a)<=e.bottomOffset&&t("reachBottom",l),t("scroll",l)}}}}),[["render",function(e,t,l,n,o,a){const i=u("empty"),r=u("Scrollbar");return c(),p("div",{class:d(e.cls)},[e.isEmpty?(c(),p("div",{key:0,class:d(`${e.prefixCls}-empty`)},[m(e.$slots,"empty",{},(()=>[s(i)]))],2)):v("v-if",!0),s(r,{ref:"wrapperRef",class:d(`${e.prefixCls}-list-wrapper`),style:h(e.style),onScroll:e.handleScroll},{default:f((()=>[g("ul",{class:d(`${e.prefixCls}-list`)},[m(e.$slots,"default")],2)])),_:3},8,["class","style","onScroll"]),e.$slots.footer&&!e.isEmpty?(c(),p("div",{key:1,class:d(`${e.prefixCls}-footer`)},[m(e.$slots,"footer")],2)):v("v-if",!0)],2)}]]);var ht=ie(n({name:"Dropdown",components:{Trigger:ze,DropdownPanel:gt},props:{popupVisible:{type:Boolean,default:void 0},defaultPopupVisible:{type:Boolean,default:!1},trigger:{type:[String,Array],default:"click"},position:{type:String,default:"bottom"},popupContainer:{type:[String,Object]},popupMaxHeight:{type:[Boolean,Number],default:!0},hideOnSelect:{type:Boolean,default:!0}},emits:{"update:popupVisible":e=>!0,popupVisibleChange:e=>!0,select:(e,t)=>!0},setup(e,{emit:t}){const{defaultPopupVisible:l,popupVisible:n,popupMaxHeight:a}=o(e),s=oe("dropdown"),{computedPopupVisible:i,handlePopupVisibleChange:r}=Je({defaultPopupVisible:l,popupVisible:n,emit:t});return y(ft,b({popupMaxHeight:a,onOptionClick:(l,n)=>{t("select",l,n),e.hideOnSelect&&r(!1)}})),{prefixCls:s,computedPopupVisible:i,handlePopupVisibleChange:r}}}),[["render",function(e,t,l,n,o,a){const i=u("DropdownPanel"),r=u("Trigger");return c(),k(r,{"popup-visible":e.computedPopupVisible,"animation-name":"slide-dynamic-origin","auto-fit-transform-origin":"",trigger:e.trigger,position:e.position,"popup-offset":4,"popup-container":e.popupContainer,"opened-class":`${e.prefixCls}-open`,onPopupVisibleChange:e.handlePopupVisibleChange},{content:f((()=>[s(i,null,C({default:f((()=>[m(e.$slots,"content")])),_:2},[e.$slots.footer?{name:"footer",fn:f((()=>[m(e.$slots,"footer")]))}:void 0]),1024)])),default:f((()=>[m(e.$slots,"default")])),_:3},8,["popup-visible","trigger","position","popup-container","opened-class","onPopupVisibleChange"])}]]);var yt=ie(n({name:"Doption",props:{value:{type:[String,Number,Object]},disabled:{type:Boolean,default:!1},active:Boolean,uninjectContext:Boolean},emits:{click:e=>!0},setup(e,{emit:t}){const l=oe("dropdown-option"),n=r(),o=a((()=>{var t,l,o;return null!=(o=null!=(l=e.value)?l:null==(t=n.value)?void 0:t.textContent)?o:void 0})),s=e.uninjectContext?void 0:i(ft,void 0),u=a((()=>[l,{[`${l}-disabled`]:e.disabled,[`${l}-active`]:e.active}]));return{prefixCls:l,cls:u,liRef:n,handleClick:l=>{e.disabled||(t("click",l),null==s||s.onOptionClick(o.value,l))}}}}),[["render",function(e,t,l,n,o,a){return c(),p("li",{ref:"liRef",class:d([e.cls,{[`${e.prefixCls}-has-suffix`]:Boolean(e.$slots.suffix)}]),onClick:t[0]||(t[0]=(...t)=>e.handleClick&&e.handleClick(...t))},[e.$slots.icon?(c(),p("span",{key:0,class:d(`${e.prefixCls}-icon`)},[m(e.$slots,"icon")],2)):v("v-if",!0),g("span",{class:d(`${e.prefixCls}-content`)},[m(e.$slots,"default")],2),e.$slots.suffix?(c(),p("span",{key:1,class:d(`${e.prefixCls}-suffix`)},[m(e.$slots,"suffix")],2)):v("v-if",!0)],2)}]]);var bt=ie(n({name:"Dgroup",props:{title:String},setup:()=>({prefixCls:oe("dropdown-group")})}),[["render",function(e,t,l,n,o,a){return c(),p($,null,[g("li",{class:d(`${e.prefixCls}-title`)},[m(e.$slots,"title",{},(()=>[x(w(e.title),1)]))],2),m(e.$slots,"default")],64)}]]);var kt=ie(n({name:"Dsubmenu",components:{Trigger:ze,DropdownPanel:gt,DropdownOption:yt,IconRight:ue},props:{value:{type:[String,Number]},disabled:{type:Boolean,default:!1},trigger:{type:[String,Array],default:"click"},position:{type:String,default:"rt"},popupVisible:{type:Boolean,default:void 0},defaultPopupVisible:{type:Boolean,default:!1},optionProps:{type:Object}},emits:{"update:popupVisible":e=>!0,popupVisibleChange:e=>!0},setup(e,{emit:t}){const{defaultPopupVisible:l,popupVisible:n}=o(e),a=oe("dropdown"),{computedPopupVisible:s,handlePopupVisibleChange:i}=Je({defaultPopupVisible:l,popupVisible:n,emit:t});return{prefixCls:a,computedPopupVisible:s,handlePopupVisibleChange:i}}}),[["render",function(e,t,l,n,o,a){const i=u("IconRight"),r=u("dropdown-option"),p=u("dropdown-panel"),v=u("Trigger");return c(),k(v,{"popup-visible":e.computedPopupVisible,trigger:e.trigger,position:e.position,disabled:e.disabled,"popup-offset":4,onPopupVisibleChange:e.handlePopupVisibleChange},{content:f((()=>[s(p,{class:d(`${e.prefixCls}-submenu`)},C({default:f((()=>[m(e.$slots,"content")])),_:2},[e.$slots.footer?{name:"footer",fn:f((()=>[m(e.$slots,"footer")]))}:void 0]),1032,["class"])])),default:f((()=>[s(r,S(e.optionProps,{active:e.computedPopupVisible,"uninject-context":""}),C({suffix:f((()=>[m(e.$slots,"suffix",{},(()=>[s(i)]))])),default:f((()=>[m(e.$slots,"default")])),_:2},[e.$slots.icon?{name:"icon",fn:f((()=>[m(e.$slots,"icon")]))}:void 0]),1040,["active"])])),_:3},8,["popup-visible","trigger","position","disabled","onPopupVisibleChange"])}]]);var Ct=ie(n({name:"DropdownButton",components:{IconMore:ce,Button:Q,ButtonGroup:ee,Dropdown:ht},props:{popupVisible:{type:Boolean,default:void 0},defaultPopupVisible:{type:Boolean,default:!1},trigger:{type:[String,Array],default:"click"},position:{type:String,default:"br"},popupContainer:{type:[String,Object]},disabled:{type:Boolean,default:!1},type:{type:String},size:{type:String},buttonProps:{type:Object},hideOnSelect:{type:Boolean,default:!0}},emits:{"update:popupVisible":e=>!0,popupVisibleChange:e=>!0,click:e=>!0,select:(e,t)=>!0},setup(e,{emit:t}){const{defaultPopupVisible:l,popupVisible:n}=o(e),a=oe("dropdown"),{computedPopupVisible:s,handlePopupVisibleChange:i}=Je({defaultPopupVisible:l,popupVisible:n,emit:t});return{prefixCls:a,computedPopupVisible:s,handleClick:e=>{t("click",e)},handleSelect:(e,l)=>{t("select",e,l)},handlePopupVisibleChange:i}}}),[["render",function(e,t,l,n,o,a){const i=u("Button"),r=u("IconMore"),p=u("Dropdown"),d=u("ButtonGroup");return c(),k(d,null,{default:f((()=>[s(i,S({size:e.size,type:e.type,disabled:e.disabled},e.buttonProps,{onClick:e.handleClick}),{default:f((()=>[m(e.$slots,"default")])),_:3},16,["size","type","disabled","onClick"]),s(p,{"popup-visible":e.computedPopupVisible,trigger:e.trigger,position:e.position,"popup-container":e.popupContainer,"hide-on-select":e.hideOnSelect,onSelect:e.handleSelect,onPopupVisibleChange:e.handlePopupVisibleChange},{content:f((()=>[m(e.$slots,"content")])),default:f((()=>[s(i,{size:e.size,type:e.type,disabled:e.disabled},{icon:f((()=>[m(e.$slots,"icon",{popupVisible:e.computedPopupVisible},(()=>[s(r)]))])),_:3},8,["size","type","disabled"])])),_:3},8,["popup-visible","trigger","position","popup-container","hide-on-select","onSelect","onPopupVisibleChange"])])),_:3})}]]);const xt=Object.assign(ht,{Option:yt,Group:bt,Submenu:kt,Button:Ct,install:(e,t)=>{ae(e,t);const l=se(t);e.component(l+ht.name,ht),e.component(l+yt.name,yt),e.component(l+bt.name,bt),e.component(l+kt.name,kt),e.component(l+Ct.name,Ct)}});var wt=n({name:"BreadcrumbItem",inheritAttrs:!1,props:{separator:{type:[String,Number]},droplist:{type:Array},dropdownProps:{type:Object},index:{type:Number,default:0}},setup(e,{slots:t,attrs:l}){const n=oe("breadcrumb-item"),o=i(vt,void 0),u=r(!1),c=a((()=>!(o&&o.needHide&&e.index>1&&e.index<=o.total-o.maxCount))),p=a((()=>!(!o||!o.needHide)&&1===e.index)),d=a((()=>!o||e.index<o.total-1)),m=e=>{u.value=e},v=()=>{var l,a,i,r,u,c,p;if(!d.value)return null;const m=null!=(p=null!=(c=null!=(u=null!=(a=null==(l=t.separator)?void 0:l.call(t))?a:e.separator)?u:null==(r=null==o?void 0:(i=o.slots).separator)?void 0:r.call(i))?c:null==o?void 0:o.separator)?p:s(pe,null,null);return s("div",{"aria-hidden":"true",class:`${n}-separator`},[m])},f=()=>{var a,i,r,c;return s("div",S({role:"listitem",class:[n,{[`${n}-with-dropdown`]:e.droplist||t.droplist}]},p.value?{"aria-label":"ellipses of breadcrumb items"}:void 0,l),[p.value?null!=(r=null==(i=null==o?void 0:(a=o.slots)["more-icon"])?void 0:i.call(a))?r:s(ce,null,null):null==(c=t.default)?void 0:c.call(t),(e.droplist||t.droplist)&&s("span",{"aria-hidden":!0,class:[`${n}-dropdown-icon`,{[`${n}-dropdown-icon-active`]:u.value}]},[s(de,null,null)])])},g=()=>{var l,n,o;return null!=(o=null==(l=t.droplist)?void 0:l.call(t))?o:null==(n=e.droplist)?void 0:n.map((e=>s(yt,{value:e.path},{default:()=>[e.label]})))};return()=>c.value?s($,null,[t.droplist||e.droplist?s(xt,S({popupVisible:u.value,onPopupVisibleChange:m},e.dropdownProps),{default:()=>[f()],content:g}):f(),v()]):null}}),$t=n({name:"Breadcrumb",props:{maxCount:{type:Number,default:0},routes:{type:Array},separator:{type:[String,Number]},customUrl:{type:Function}},setup(e,{slots:t}){const{maxCount:l,separator:n,routes:i}=o(e),u=oe("breadcrumb"),c=r(0),p=a((()=>l.value>0&&c.value>l.value+1));y(vt,b({total:c,maxCount:l,separator:n,needHide:p,slots:t}));const d=(t,l,n)=>{var o,a;if(l.indexOf(t)===l.length-1)return s("span",null,[t.label]);const i=null!=(a=null==(o=e.customUrl)?void 0:o.call(e,n))?a:`#/${n.join("/").replace(/^\//,"")}`;return s("a",{href:i},[t.label])},m=()=>{var e;if(!(null==(e=i.value)?void 0:e.length))return null;c.value!==i.value.length&&(c.value=i.value.length);const l=[];return i.value.map(((e,n,o)=>{l.push((e.path||"").replace(/^\//,""));const a=[...l];return s(wt,{key:e.path||e.label,index:n,droplist:e.children},{default:()=>{var l,n;return[null!=(n=null==(l=t["item-render"])?void 0:l.call(t,{route:e,routes:o,paths:a}))?n:d(e,o,a)]}})}))},v=()=>{var e,l;const n=me(null!=(l=null==(e=t.default)?void 0:e.call(t))?l:[]);return c.value!==n.length&&(c.value=n.length),n.map(((e,t)=>{var l;return e.props=S(null!=(l=e.props)?l:{},{index:t}),e}))};return()=>s("div",{role:"list",class:u},[t.default?v():m()])}});const St=Object.assign($t,{Item:wt,install:(e,t)=>{ae(e,t);const l=se(t);e.component(l+$t.name,$t),e.component(l+wt.name,wt)}}),Pt=Symbol("LayoutSiderInjectionKey"),_t=Symbol("SiderInjectionKey");var Mt=ie(n({name:"Layout",props:{hasSider:{type:Boolean}},setup(e){const t=r([]),l=oe("layout"),n=a((()=>[l,{[`${l}-has-sider`]:e.hasSider||t.value.length}]));return y(Pt,{onSiderMount:e=>t.value.push(e),onSiderUnMount:e=>{t.value=t.value.filter((t=>t!==e))}}),{classNames:n}}}),[["render",function(e,t,l,n,o,a){return c(),p("section",{class:d(e.classNames)},[m(e.$slots,"default")],2)}]]);var Vt=ie(n({name:"LayoutHeader",setup:()=>({classNames:[oe("layout-header")]})}),[["render",function(e,t,l,n,o,a){return c(),p("header",{class:d(e.classNames)},[m(e.$slots,"default")],2)}]]);var Bt=ie(n({name:"LayoutContent",setup:()=>({classNames:[oe("layout-content")]})}),[["render",function(e,t,l,n,o,a){return c(),p("main",{class:d(e.classNames)},[m(e.$slots,"default")],2)}]]);var It=ie(n({name:"ResizeTrigger",components:{ResizeObserver:lt,IconDragDot:ve,IconDragDotVertical:fe},props:{prefixCls:{type:String,required:!0},direction:{type:String,default:"horizontal"}},emits:["resize"],setup(e,{emit:t}){const{direction:l,prefixCls:n}=o(e),s=a((()=>"horizontal"===(null==l?void 0:l.value)));return{classNames:a((()=>[n.value,{[`${n.value}-horizontal`]:s.value,[`${n.value}-vertical`]:!s.value}])),onResize:e=>{t("resize",e)},isHorizontal:s}}}),[["render",function(e,t,l,n,o,a){const s=u("IconDragDot"),i=u("IconDragDotVertical"),r=u("ResizeObserver");return c(),k(r,{onResize:e.onResize},{default:f((()=>[g("div",{class:d(e.classNames)},[v(" @slot 自定义内容 "),m(e.$slots,"default",{},(()=>[g("div",{class:d(`${e.prefixCls}-icon-wrapper`)},[v(" @slot 自定义 icon "),m(e.$slots,"icon",{},(()=>[e.isHorizontal?(c(),k(s,{key:0,class:d(`${e.prefixCls}-icon`)},null,8,["class"])):(c(),k(i,{key:1,class:d(`${e.prefixCls}-icon`)},null,8,["class"]))]))],2)]))],2)])),_:3},8,["onResize"])}]]),Ot=Object.defineProperty,jt=Object.getOwnPropertySymbols,zt=Object.prototype.hasOwnProperty,Nt=Object.prototype.propertyIsEnumerable,Dt=(e,t,l)=>t in e?Ot(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,Tt=(e,t)=>{for(var l in t||(t={}))zt.call(t,l)&&Dt(e,l,t[l]);if(jt)for(var l of jt(t))Nt.call(t,l)&&Dt(e,l,t[l]);return e};const Rt="left",Kt="right",At="top",Ht="bottom",Et=[Rt,Kt,At,Ht];function Lt(e,t){if(0===e)return 0;const l=e-t;return l<=0?0:l}function Ft(e){return[At,Ht].indexOf(e)>-1}var Wt=ie(n({name:"ResizeBox",components:{ResizeTrigger:It},inheritAttrs:!1,props:{width:{type:Number},height:{type:Number},component:{type:String,default:"div"},directions:{type:Array,default:()=>["right"]}},emits:{"update:width":e=>!0,"update:height":e=>!0,movingStart:e=>!0,moving:(e,t)=>!0,movingEnd:e=>!0},setup(e,{emit:t}){const{height:l,width:n,directions:s}=o(e),[i,u]=Ne(null,b({value:n})),[c,p]=Ne(null,b({value:l})),d=r(),m=b({}),v=oe("resizebox"),f=a((()=>[v])),g=a((()=>Tt(Tt(Tt({},re(i.value)?{width:`${i.value}px`}:{}),re(c.value)?{height:`${c.value}px`}:{}),m))),h=a((()=>s.value.filter((e=>Et.includes(e))))),y={direction:"",startPageX:0,startPageY:0,startWidth:0,startHeight:0,moving:!1,padding:{left:0,right:0,top:0,bottom:0}};function k(e){if(!y.moving)return;const{startPageX:l,startPageY:n,startWidth:o,startHeight:a,direction:s}=y;let i=o,r=a;const c=e.pageX-l,d=e.pageY-n;switch(s){case Rt:i=o-c,u(i),t("update:width",i);break;case Kt:i=o+c,u(i),t("update:width",i);break;case At:r=a-d,p(r),t("update:height",r);break;case Ht:r=a+d,p(r),t("update:height",r)}t("moving",{width:i,height:r},e)}function C(e){y.moving=!1,he(window,"mousemove",k),he(window,"mouseup",C),he(window,"contextmenu",C),document.body.style.cursor="default",t("movingEnd",e)}return{prefixCls:v,classNames:f,styles:g,wrapperRef:d,onMoveStart:function(e,l){var n,o;t("movingStart",l),y.moving=!0,y.startPageX=l.pageX,y.startPageY=l.pageY,y.direction=e;const{top:a,left:s,right:i,bottom:r}=y.padding;y.startWidth=Lt((null==(n=d.value)?void 0:n.clientWidth)||0,s+i),y.startHeight=Lt((null==(o=d.value)?void 0:o.clientHeight)||0,a+r),ge(window,"mousemove",k),ge(window,"mouseup",C),ge(window,"contextmenu",C),document.body.style.cursor=Ft(e)?"row-resize":"col-resize"},isHorizontal:Ft,allowDirections:h,onTiggerResize:function(e,t){const{width:l,height:n}=t.contentRect,o=Ft(e)?n:l;y.padding[e]=o,m[`padding-${e}`]=`${o}px`}}}}),[["render",function(e,t,l,n,o,a){const s=u("ResizeTrigger");return c(),k(_(e.component),S({ref:"wrapperRef",class:e.classNames},e.$attrs,{style:e.styles}),{default:f((()=>[m(e.$slots,"default"),(c(!0),p($,null,P(e.allowDirections,(t=>(c(),k(s,{key:t,"prefix-cls":`${e.prefixCls}-trigger`,class:d(`${e.prefixCls}-direction-${t}`),direction:e.isHorizontal(t)?"horizontal":"vertical",onMousedown:l=>{e.onMoveStart(t,l)},onResize:l=>{e.onTiggerResize(t,l)}},C({default:f((()=>[e.$slots["resize-trigger"]?m(e.$slots,"resize-trigger",{key:0,direction:t}):v("v-if",!0)])),_:2},[e.$slots["resize-trigger-icon"]?{name:"icon",fn:f((()=>[m(e.$slots,"resize-trigger-icon",{direction:t})]))}:void 0]),1032,["prefix-cls","class","direction","onMousedown","onResize"])))),128))])),_:3},16,["class","style"])}]]);const Ut=Object.assign(Wt,{install:(e,t)=>{ae(e,t);const l=se(t);e.component(l+Wt.name,Wt)}});function Gt(e,t){const l=a((()=>M(e)?e.value:e));let n="";V((()=>{n=Fe.subscribe(((e,n)=>{l.value&&(n&&n!==l.value||t(!!e[l.value]))}))})),B((()=>{n&&Fe.unsubscribe(n)}))}const qt=(()=>{let e=0;return(t="")=>(e+=1,`${t}${e}`)})();var Yt=n({name:"LayoutSider",components:{IconLeft:ye,IconRight:ue,ResizeBox:Ut},props:{theme:{type:String,default:"light"},collapsed:{type:Boolean,default:void 0},defaultCollapsed:{type:Boolean},collapsible:{type:Boolean},width:{type:Number,default:200},collapsedWidth:{type:Number,default:48},reverseArrow:{type:Boolean},breakpoint:{type:String},resizeDirections:{type:Array,default:void 0},hideTrigger:{type:Boolean}},emits:["collapse","update:collapsed","breakpoint"],setup(e,{emit:t}){const{theme:l,collapsed:n,defaultCollapsed:s,collapsible:r,hideTrigger:u,breakpoint:c,collapsedWidth:p,resizeDirections:d}=o(e),[m,v]=Ne(s.value,b({value:n})),f=a((()=>d.value?"ResizeBox":"div")),g=a((()=>r.value&&!u.value)),h=oe("layout-sider"),k=a((()=>[h,{[`${h}-light`]:"light"===l.value,[`${h}-has-trigger`]:g.value,[`${h}-collapsed`]:n.value}])),C=a((()=>{const{width:t,collapsedWidth:l}=e,n=m.value?l:t;return re(n)?`${n}px`:String(n)})),x=a((()=>[`${h}-trigger`,{[`${h}-trigger-light`]:"light"===l.value}]));Gt(c,(e=>{const l=!e;l!==m.value&&(v(l),t("update:collapsed",l),t("collapse",l,"responsive"),t("breakpoint",l))}));const w=qt("__arco_layout_sider"),$=i(Pt,void 0);return V((()=>{var e;null==(e=null==$?void 0:$.onSiderMount)||e.call($,w)})),B((()=>{var e;null==(e=null==$?void 0:$.onSiderUnMount)||e.call($,w)})),y(_t,b({theme:l,collapsed:m,collapsedWidth:p})),{componentTag:f,prefixCls:h,classNames:k,triggerClassNames:x,localCollapsed:m,siderWidth:C,showTrigger:g,toggleTrigger:()=>{const e=!m.value;v(e),t("update:collapsed",e),t("collapse",e,"clickTrigger")}}}});const Xt={key:0},Zt={key:1};var Jt=ie(Yt,[["render",function(e,t,l,n,o,a){const s=u("IconLeft"),i=u("IconRight");return c(),k(_(e.componentTag),S({class:e.classNames,style:{width:e.siderWidth}},e.resizeDirections?{directions:e.resizeDirections}:{}),{default:f((()=>[g("div",{class:d(`${e.prefixCls}-children`)},[m(e.$slots,"default")],2),e.showTrigger?(c(),p("div",{key:0,class:d(e.triggerClassNames),style:h({width:e.siderWidth}),onClick:t[0]||(t[0]=(...t)=>e.toggleTrigger&&e.toggleTrigger(...t))},[m(e.$slots,"trigger",{collapsed:e.localCollapsed},(()=>[e.reverseArrow?(c(),p("div",Zt,[e.localCollapsed?(c(),k(s,{key:0})):(c(),k(i,{key:1}))])):(c(),p("div",Xt,[e.localCollapsed?(c(),k(i,{key:1})):(c(),k(s,{key:0}))]))]))],6)):v("v-if",!0)])),_:3},16,["class","style"])}]]);const Qt=Object.assign(Mt,{Header:Vt,Content:Bt,Footer:t,Sider:Jt,install:(e,l)=>{ae(e,l);const n=se(l);e.component(n+Mt.name,Mt),e.component(n+Vt.name,Vt),e.component(n+Bt.name,Bt),e.component(n+t.name,t),e.component(n+Jt.name,Jt)}}),el=Symbol("MenuInjectionKey"),tl=Symbol("LevelInjectionKey"),ll=Symbol("DataCollectorInjectionKey");function nl(e){const t=a((()=>M(e)?e.value:e));y(tl,b({level:t}))}function ol(e){const{provideNextLevel:t}=e||{},l=i(tl),n=a((()=>(null==l?void 0:l.level)||1));if(t){nl(a((()=>n.value+1)))}return{level:n}}function al(e,t){const l=[],n=e=>{e.forEach((e=>{t(e)&&l.push(e.key),e.children&&n(e.children)}))};return n(e),l}function sl(e=!1){return e?void 0:i(ll)}function il(e){const{key:t,type:l}=e,n=r([]),o=sl("menu"===l);return y(ll,{collectSubMenu(e,t,a=!1){const s={key:e,children:t};if(a){const l=n.value.find((t=>t.key===e));l?l.children=t:n.value.push(s)}else n.value=[...n.value,s];a&&("popupMenu"===l?null==o||o.reportMenuData(n.value):"subMenu"!==l||be(e)||null==o||o.collectSubMenu(e,n.value,!0))},removeSubMenu(e){n.value=n.value.filter((t=>t.key!==e))},collectMenuItem(e){n.value.push({key:e})},removeMenuItem(e){n.value=n.value.filter((t=>t.key!==e))},reportMenuData(e){n.value=e,"subMenu"!==l||be(t)||null==o||o.collectSubMenu(t,n.value,!0)}}),"subMenu"!==l||be(t)?"popupMenu"===l&&V((()=>{null==o||o.reportMenuData(n.value)})):(V((()=>{null==o||o.collectSubMenu(t,n.value)})),B((()=>{null==o||o.removeSubMenu(t)}))),{menuData:n,subMenuKeys:a((()=>al(n.value,(e=>!!e.children)))),menuItemKeys:a((()=>al(n.value,(e=>!e.children))))}}var rl=ie(n({name:"BaseMenu",components:{IconMenuFold:ke,IconMenuUnfold:Ce},inheritAttrs:!1,props:{style:{type:Object},theme:{type:String,default:"light"},mode:{type:String,default:"vertical"},levelIndent:{type:Number},autoOpen:{type:Boolean},collapsed:{type:Boolean,default:void 0},defaultCollapsed:{type:Boolean},collapsedWidth:{type:Number},accordion:{type:Boolean},autoScrollIntoView:{type:Boolean},showCollapseButton:{type:Boolean},selectedKeys:{type:Array},defaultSelectedKeys:{type:Array,default:()=>[]},openKeys:{type:Array},defaultOpenKeys:{type:Array,default:()=>[]},scrollConfig:{type:Object},triggerProps:{type:Object},tooltipProps:{type:Object},autoOpenSelected:{type:Boolean},breakpoint:{type:String},popupMaxHeight:{type:[Boolean,Number],default:!0},prefixCls:{type:String},inTrigger:{type:Boolean},siderCollapsed:{type:Boolean},isRoot:{type:Boolean}},emits:["update:collapsed","update:selectedKeys","update:openKeys","collapse","menu-item-click","sub-menu-click"],setup(e,{emit:t,slots:l}){const{style:n,mode:s,theme:i,levelIndent:u,accordion:c,showCollapseButton:p,scrollConfig:d,autoScrollIntoView:m,collapsedWidth:v,autoOpen:f,collapsed:g,defaultCollapsed:h,selectedKeys:k,defaultSelectedKeys:C,openKeys:x,defaultOpenKeys:w,triggerProps:$,tooltipProps:S,autoOpenSelected:P,breakpoint:_,popupMaxHeight:M,prefixCls:B,inTrigger:O,siderCollapsed:j,isRoot:z}=o(e),{subMenuKeys:N,menuData:D}=il({type:z.value?"menu":"popupMenu"}),[T,R]=Ne(C.value,b({value:k})),{openKeys:K,setOpenKeys:A,open:H}=function(e){const{modelValue:t,defaultValue:l,autoOpen:n,autoOpenSelected:s,subMenuKeys:i,selectedKeys:u,menuData:c,accordion:p}=o(e),d=r(be(t.value)?be(l.value)?[]:l.value:t.value),m=e=>{d.value=e};I(t,(()=>{be(t.value)&&m([])}));let v=[];V((()=>{v=[...i.value];let e=[];if(n.value&&(e=p.value?i.value.slice(0,1):[...i.value]),s.value){const t=u.value.map((e=>function(e,t){const l=[],n=e=>{for(let o=0;o<e.length;o++){const a=e[o];if(a.key===t)return!0;if(a.children){if(l.push(a.key),n(a.children))return!0;l.pop()}}return!1};return n(e),l}(c.value,e)));!t.length||n.value&&!p.value||(e=p.value?t[0]:[...new Set([].concat(...t))])}e.length&&m(e)}));let f=!1;I(i,((e,t=[])=>{if(f||(o=v,(l=e).length!==o.length||l.toString()!==o.toString())){const l=g.value.filter((t=>e.includes(t)));if(n.value){const n=e.filter((e=>!t.includes(e)));l.push(...n)}m(p.value?l.slice(0,1):l)}var l,o;f=!0}));const g=a((()=>t.value||d.value));return{openKeys:g,localOpenKeys:d,setOpenKeys:m,open(e,t){let l=[];return l=g.value.indexOf(e)>-1?p.value&&1===t?[]:g.value.filter((t=>t!==e)):p.value&&1===t?[e]:g.value.concat([e]),m(l),l}}}(b({modelValue:x,defaultValue:w,autoOpen:f,autoOpenSelected:P,selectedKeys:T,subMenuKeys:N,menuData:D,accordion:c})),[E,L]=Ne(h.value,b({value:g})),F=a((()=>j.value||E.value||"popButton"===s.value)),W=a((()=>["horizontal","popButton"].indexOf(s.value)<0&&!O.value&&p.value)),U=(e,l)=>{e!==E.value&&(L(e),t("update:collapsed",e),t("collapse",e,l))};Gt(_,(e=>{U(!e,"responsive")}));const G=a((()=>(null==B?void 0:B.value)||oe("menu"))),q=a((()=>[G.value,`${G.value}-${null==i?void 0:i.value}`,{[`${G.value}-horizontal`]:"horizontal"===s.value,[`${G.value}-vertical`]:"horizontal"!==s.value,[`${G.value}-collapsed`]:F.value,[`${G.value}-pop`]:"pop"===s.value||F.value,[`${G.value}-pop-button`]:"popButton"===s.value}])),Y=a((()=>{const e=re(v.value)?`${v.value}px`:void 0,t=xe(n.value)?n.value:void 0,l=F.value?e:null==t?void 0:t.width;return[t?De(t,["width"]):n.value,{width:l}]})),X=Te(l,"expand-icon-down"),Z=Te(l,"expand-icon-right"),J=b({theme:i,mode:s,levelIndent:u,autoScrollIntoView:m,selectedKeys:T,openKeys:K,prefixCls:G,scrollConfig:d,inTrigger:O,collapsed:F,triggerProps:$,tooltipProps:S,popupMaxHeight:M,expandIconDown:X,expandIconRight:Z,onMenuItemClick:e=>{R([e]),t("update:selectedKeys",[e]),t("menu-item-click",e)},onSubMenuClick:(e,l)=>{const n=H(e,l);A(n),t("update:openKeys",n),t("sub-menu-click",e,n)}});return y(el,J),nl(1),{computedPrefixCls:G,classNames:q,computedStyle:Y,computedCollapsed:F,computedHasCollapseButton:W,onCollapseBtnClick:()=>{U(!E.value,"clickTrigger")}}}}),[["render",function(e,t,l,n,o,a){const s=u("IconMenuUnfold"),i=u("IconMenuFold");return c(),p("div",S({class:e.classNames},e.$attrs,{style:e.computedStyle}),[g("div",{class:d(`${e.computedPrefixCls}-inner`)},[m(e.$slots,"default")],2),e.computedHasCollapseButton?(c(),p("div",{key:0,class:d(`${e.computedPrefixCls}-collapse-button`),onClick:t[0]||(t[0]=(...t)=>e.onCollapseBtnClick&&e.onCollapseBtnClick(...t))},[m(e.$slots,"collapse-icon",{collapsed:e.computedCollapsed},(()=>[e.computedCollapsed?(c(),k(s,{key:0})):(c(),k(i,{key:1}))]))],2)):v("v-if",!0)],16)}]]);function ul(e,t){if(!e||!t)return null;let l=t;"float"===l&&(l="cssFloat");try{if(document.defaultView){const t=document.defaultView.getComputedStyle(e,"");return e.style[l]||t?t[l]:""}}catch(n){return e.style[l]}return null}function cl(){return i(el)||{}}const pl=(()=>{let e=0;return(t="")=>(e+=1,`${t}${e}`)})();function dl(){const e=O();return{key:a((()=>(null==e?void 0:e.vnode.key)||pl("__arco_menu")))}}var ml=ie(n({name:"MenuIndent",props:{level:{type:Number,default:1}},setup(){const e=oe("menu"),t=cl();return{prefixCls:e,levelIndent:j(t,"levelIndent")}}}),[["render",function(e,t,l,n,o,a){return e.level>1?(c(),p("span",{key:0,class:d(`${e.prefixCls}-indent-list`)},[(c(!0),p($,null,P(e.level-1,(t=>(c(),p("span",{key:t,class:d(`${e.prefixCls}-indent`),style:h(`width: ${e.levelIndent}px`)},null,6)))),128))],2)):v("v-if",!0)}]]);const vl=n({name:"ExpandTransition",setup:()=>({onBeforeEnter(e){e.style.height="0"},onEnter(e){e.style.height=`${e.scrollHeight}px`},onAfterEnter(e){e.style.height=""},onBeforeLeave(e){e.style.height=`${e.scrollHeight}px`},onLeave(e){e.style.height="0"},onAfterLeave(e){e.style.height=""}})});var fl=ie(n({name:"SubMenuInline",components:{MenuIndent:ml,ExpandTransition:ie(vl,[["render",function(e,t,l,n,o,a){return c(),k(z,{onBeforeEnter:e.onBeforeEnter,onEnter:e.onEnter,onAfterEnter:e.onAfterEnter,onBeforeLeave:e.onBeforeLeave,onLeave:e.onLeave,onAfterLeave:e.onAfterLeave},{default:f((()=>[m(e.$slots,"default")])),_:3},8,["onBeforeEnter","onEnter","onAfterEnter","onBeforeLeave","onLeave","onAfterLeave"])}]])},props:{title:{type:String},isChildrenSelected:{type:Boolean}},setup(e){const{key:t}=dl(),{level:l}=ol({provideNextLevel:!0}),n=cl(),o=a((()=>n.prefixCls)),s=a((()=>`${o.value}-inline`)),i=a((()=>[s.value])),r=a((()=>e.isChildrenSelected)),u=a((()=>(n.openKeys||[]).indexOf(t.value)>-1));return{prefixCls:s,menuPrefixCls:o,classNames:i,level:l,isSelected:r,isOpen:u,onHeaderClick:()=>{n.onSubMenuClick&&n.onSubMenuClick(t.value,l.value)}}}}),[["render",function(e,t,l,n,o,a){const i=u("MenuIndent"),r=u("ExpandTransition");return c(),p("div",{class:d(e.classNames)},[g("div",{class:d([`${e.prefixCls}-header`,{[`${e.menuPrefixCls}-selected`]:e.isSelected,[`${e.menuPrefixCls}-has-icon`]:e.$slots.icon}]),onClick:t[0]||(t[0]=(...t)=>e.onHeaderClick&&e.onHeaderClick(...t))},[s(i,{level:e.level},null,8,["level"]),e.$slots.icon?(c(),p($,{key:0},[g("span",{class:d(`${e.menuPrefixCls}-icon`)},[m(e.$slots,"icon")],2),g("span",{class:d(`${e.menuPrefixCls}-title`)},[m(e.$slots,"title",{},(()=>[x(w(e.title),1)]))],2)],64)):m(e.$slots,"title",{key:1},(()=>[x(w(e.title),1)])),g("span",{class:d([`${e.menuPrefixCls}-icon-suffix`,{"is-open":e.isOpen}])},[m(e.$slots,"expand-icon-down")],2)],2),s(r,null,{default:f((()=>[N(g("div",{class:d(`${e.prefixCls}-content`)},[m(e.$slots,"default")],2),[[D,e.isOpen]])])),_:3})],2)}]]);var gl=ie(n({name:"SubMenuPop",components:{Menu:rl,Trigger:ze,MenuIndent:ml,RenderFunction:et},inheritAttrs:!1,props:{title:{type:String},selectable:{type:Boolean},isChildrenSelected:{type:Boolean},popupMaxHeight:{type:[Boolean,Number],default:void 0}},setup(e){const{key:t}=dl(),{level:l}=ol(),{selectable:n,isChildrenSelected:s,popupMaxHeight:i}=o(e),u=cl(),{onSubMenuClick:c,onMenuItemClick:p}=u,d=a((()=>u.prefixCls)),m=a((()=>u.mode)),v=a((()=>u.selectedKeys||[])),f=a((()=>`${d.value}-pop`)),g=a((()=>n.value&&v.value.includes(t.value)||s.value)),h=a((()=>[`${f.value}`,`${f.value}-header`,{[`${d.value}-selected`]:g.value}])),y=a((()=>"horizontal"===m.value&&!u.inTrigger)),b=r(!1),k=e=>{b.value=e},C=oe("trigger"),x=a((()=>{var e;return[`${f.value}-trigger`,{[`${f.value}-trigger-dark`]:"dark"===u.theme},null==(e=u.triggerProps)?void 0:e.class]})),w=a((()=>De(u.triggerProps||{},["class"])));return{menuPrefixCls:d,mode:m,level:l,classNames:h,isSelected:g,selectedKeys:v,needPopOnBottom:y,popVisible:b,triggerPrefixCls:C,triggerClassNames:x,triggerProps:w,menuContext:u,popupMenuStyles:a((()=>{var e;const t=null!=(e=i.value)?e:u.popupMaxHeight;return re(t)?{maxHeight:`${t}px`}:t?{}:{maxHeight:"unset"}})),onClick:()=>{c&&c(t.value,l.value),n.value&&p&&p(t.value)},onMenuItemClick:e=>{p&&p(e),k(!1)},onVisibleChange:e=>{k(e)}}}}),[["render",function(e,t,l,n,o,a){const i=u("MenuIndent"),r=u("RenderFunction"),y=u("Menu"),b=u("Trigger");return c(),k(b,S({trigger:"hover",class:e.triggerClassNames,position:e.needPopOnBottom?"bl":"rt","show-arrow":"","animation-class":"fade-in","mouse-enter-delay":50,"mouse-leave-delay":50,"popup-offset":4,"auto-fit-popup-min-width":!0,duration:100},e.triggerProps,{"unmount-on-close":!1,"popup-visible":e.popVisible,onPopupVisibleChange:e.onVisibleChange}),{content:f((()=>[s(y,{"in-trigger":"","prefix-cls":`${e.triggerPrefixCls}-menu`,"selected-keys":e.selectedKeys,theme:e.menuContext.theme,"trigger-props":e.menuContext.triggerProps,style:h(e.popupMenuStyles),onMenuItemClick:e.onMenuItemClick},C({default:f((()=>[m(e.$slots,"default")])),_:2},[e.menuContext.expandIconDown?{name:"expand-icon-down",fn:f((()=>[s(r,{"render-func":e.menuContext.expandIconDown},null,8,["render-func"])]))}:void 0,e.menuContext.expandIconRight?{name:"expand-icon-right",fn:f((()=>[s(r,{"render-func":e.menuContext.expandIconRight},null,8,["render-func"])]))}:void 0]),1032,["prefix-cls","selected-keys","theme","trigger-props","style","onMenuItemClick"])])),default:f((()=>[g("div",S({class:[e.classNames,{[`${e.menuPrefixCls}-has-icon`]:e.$slots.icon}],"aria-haspopup":"true"},e.$attrs,{onClick:t[0]||(t[0]=(...t)=>e.onClick&&e.onClick(...t))}),[v(" header "),s(i,{level:e.level},null,8,["level"]),e.$slots.icon?(c(),p($,{key:0},[g("span",{class:d(`${e.menuPrefixCls}-icon`)},[m(e.$slots,"icon")],2),g("span",{class:d(`${e.menuPrefixCls}-title`)},[m(e.$slots,"title",{},(()=>[x(w(e.title),1)]))],2)],64)):m(e.$slots,"title",{key:1},(()=>[x(w(e.title),1)])),v(" suffix "),g("span",{class:d(`${e.menuPrefixCls}-icon-suffix`)},[e.needPopOnBottom?m(e.$slots,"expand-icon-down",{key:0}):m(e.$slots,"expand-icon-right",{key:1})],2),e.isSelected&&"horizontal"===e.mode?(c(),p("div",{key:2,class:d(`${e.menuPrefixCls}-selected-label`)},null,2)):v("v-if",!0)],16)])),_:3},16,["class","position","popup-visible","onPopupVisibleChange"])}]]),hl=Object.defineProperty,yl=Object.defineProperties,bl=Object.getOwnPropertyDescriptors,kl=Object.getOwnPropertySymbols,Cl=Object.prototype.hasOwnProperty,xl=Object.prototype.propertyIsEnumerable,wl=(e,t,l)=>t in e?hl(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,$l=n({name:"SubMenu",props:{title:{type:String},selectable:{type:Boolean},popup:{type:[Boolean,Function],default:!1},popupMaxHeight:{type:[Boolean,Number],default:void 0}},setup(e,{attrs:t}){const{key:l}=dl(),{level:n}=ol(),{popup:s}=o(e),i=cl(),r=a((()=>{const{mode:e,collapsed:t,inTrigger:l}=i;return!!("function"==typeof s.value?s.value(n.value):s.value)||t||l||"vertical"!==e})),{subMenuKeys:u,menuItemKeys:c}=il({key:l.value,type:"subMenu"}),p=a((()=>{const e=i.selectedKeys||[],t=t=>{for(let l=0;l<e.length;l++){const n=e[l];if(t.includes(n))return!0}return!1};return t(u.value)||t(c.value)}));return{subMenuKeys:u,menuItemKeys:c,isChildrenSelected:p,props:e,attrs:t,computedKey:l,computedPopup:r,expandIconDown:j(i,"expandIconDown"),expandIconRight:j(i,"expandIconRight")}},render(){const{props:e,attrs:t,computedKey:l,computedPopup:n,expandIconDown:o,expandIconRight:a,isChildrenSelected:i}=this,r=(u=((e,t)=>{for(var l in t||(t={}))Cl.call(t,l)&&wl(e,l,t[l]);if(kl)for(var l of kl(t))xl.call(t,l)&&wl(e,l,t[l]);return e})({},this.$slots),c={"expand-icon-down":this.$slots["expand-icon-down"]||o||(()=>[s(de,null,null)]),"expand-icon-right":this.$slots["expand-icon-right"]||a||(()=>[s(ue,null,null)])},yl(u,bl(c)));var u,c;return n?s(gl,S({key:l,title:e.title,selectable:e.selectable,isChildrenSelected:i,popupMaxHeight:e.popupMaxHeight},t),r):s(fl,S({key:l,title:e.title,isChildrenSelected:i},t),r)}});function Sl(e){return e&&+e.getBoundingClientRect().width.toFixed(2)}function Pl(e){const t=Number(e.replace("px",""));return Number.isNaN(t)?0:t}var _l=n({name:"MenuOverflowWrap",setup(e,{slots:t}){const l=`${cl().prefixCls}-overflow`,n=`${l}-sub-menu`,o=`${l}-hidden-menu-item`,a=`${l}-sub-menu-mirror`,i=r(),u=r(null),c=r();function p(){const e=i.value,t=Sl(e),l=[].slice.call(e.children);let o=0,s=0,r=0;for(let i=0;i<l.length;i++){const e=l[i],c=e.className.split(" "),p=c.indexOf(n)>-1,d=c.indexOf(a)>-1;if(p)continue;const m=Sl(e)+Pl(ul(e,"marginLeft"))+Pl(ul(e,"marginRight"));if(d)r=m;else{if(s+=m,s+r+10>t)return void(u.value=o-1);o++}}u.value=null}return V((()=>{p(),c.value=new tt((e=>{e.forEach(p)})),i.value&&c.value.observe(i.value)})),B((()=>{c.value&&c.value.disconnect()})),()=>{const e=(e,t)=>{const{isMirror:l=!1,props:o={}}=t||{};return s($l,S({key:"__arco-menu-overflow-sub-menu"+(l?"-mirror":""),class:l?a:n},o),{title:()=>s("span",null,[x("...")]),default:()=>e})};return s("div",{class:`${l}-wrap`,ref:i},[(()=>{var l;const n=(null==(l=t.default)?void 0:l.call(t))||[],a=we(n);let s=null;return[e(null,{isMirror:!0}),...a.map(((t,l)=>{const n=T(t,null!==u.value&&l>u.value?{class:o}:{class:""});if(null!==u.value&&l===u.value+1){const t=a.slice(l).map((e=>T(e)));s=e(t)}return n})),s]})()])}}}),Ml=Object.defineProperty,Vl=Object.defineProperties,Bl=Object.getOwnPropertyDescriptors,Il=Object.getOwnPropertySymbols,Ol=Object.prototype.hasOwnProperty,jl=Object.prototype.propertyIsEnumerable,zl=(e,t,l)=>t in e?Ml(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,Nl=n({name:"Menu",components:{BaseMenu:rl},inheritAttrs:!1,props:{theme:{type:String},mode:{type:String,default:"vertical"}},setup(e,{attrs:t,slots:l}){const{theme:n,mode:r}=o(e),u=i(_t,void 0),c=a((()=>(null==u?void 0:u.collapsed)||!1)),p=a((()=>(null==n?void 0:n.value)||(null==u?void 0:u.theme)||"light"));return y(el,void 0),y(tl,void 0),()=>{return s(rl,S(e,t,{theme:p.value,inTrigger:!1,siderCollapsed:c.value,isRoot:!0}),(n=((e,t)=>{for(var l in t||(t={}))Ol.call(t,l)&&zl(e,l,t[l]);if(Il)for(var l of Il(t))jl.call(t,l)&&zl(e,l,t[l]);return e})({},l),o={default:"horizontal"===r.value&&l.default?()=>s(_l,null,{default:()=>{var e;return[null==(e=l.default)?void 0:e.call(l)]}}):l.default},Vl(n,Bl(o))));var n,o}}}),Dl=Object.defineProperty,Tl=Object.getOwnPropertySymbols,Rl=Object.prototype.hasOwnProperty,Kl=Object.prototype.propertyIsEnumerable,Al=(e,t,l)=>t in e?Dl(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,Hl=n({name:"MenuItem",inheritAttrs:!1,props:{disabled:{type:Boolean,default:!1}},emits:["click"],setup(e,{emit:t}){const{key:l}=dl(),{level:n}=ol(),o=cl(),s=r(),i=a((()=>(o.selectedKeys||[]).indexOf(l.value)>-1)),u=sl();function c(){o.autoScrollIntoView&&s.value&&i.value&&We(s.value,((e,t)=>{for(var l in t||(t={}))Rl.call(t,l)&&Al(e,l,t[l]);if(Tl)for(var l of Tl(t))Kl.call(t,l)&&Al(e,l,t[l]);return e})({behavior:"smooth",block:"nearest",scrollMode:"if-needed",boundary:document.documentElement},o.scrollConfig||{}))}let p;return V((()=>{null==u||u.collectMenuItem(l.value)})),B((()=>{null==u||u.removeMenuItem(l.value)})),V((()=>{p=setTimeout((()=>{c()}),500)})),B((()=>{clearTimeout(p)})),I([i],(()=>{c()})),{menuContext:o,level:n,isSelected:i,refItemElement:s,onClick(n){e.disabled||(o.onMenuItemClick&&o.onMenuItemClick(l.value),t("click",n))}}},render(){var e,t;const{level:l,menuContext:n,disabled:o,isSelected:a,onClick:i}=this,{prefixCls:r,collapsed:u,inTrigger:c,mode:p,tooltipProps:d}=n,m=u&&!c&&1===l,v="vertical"===p&&l>1,f=(null==(t=(e=this.$slots).default)?void 0:t.call(e))||[],g=v&&!c&&!u,h=this.$slots.icon&&this.$slots.icon(),y=[g&&s(ml,{level:l},null),h&&s("span",{class:`${r}-icon`},[h]),g||h?s("span",{class:[`${r}-item-inner`,{[`${r}-title`]:h}]},[f]):f].filter(Boolean),b=s("div",S({ref:"refItemElement",class:[`${r}-item`,{[`${r}-disabled`]:o,[`${r}-selected`]:a,[`${r}-has-icon`]:h}]},this.$attrs,{onClick:i}),[y,a&&"horizontal"===p&&s("div",{class:`${r}-selected-label`},null)]);if(m){const e=[`${r}-item-tooltip`,null==d?void 0:d.class];return s(Re,S({trigger:"hover",position:"right",class:e},De(d||{},["class"])),{default:()=>b,content:()=>f})}return b}});var El=ie(n({name:"MenuItemGroup",components:{MenuIndent:ml},props:{title:{type:String}},setup(){const{level:e}=ol();nl(a((()=>1===e.value?e.value+1:e.value)));const t=cl(),l=a((()=>t.prefixCls)),n=a((()=>[`${l.value}-group`]));return{prefixCls:l,classNames:n,level:e}}}),[["render",function(e,t,l,n,o,a){const i=u("MenuIndent");return c(),p("div",{class:d(e.classNames)},[g("div",{class:d(`${e.prefixCls}-group-title`)},[s(i,{level:e.level},null,8,["level"]),m(e.$slots,"title",{},(()=>[x(w(e.title),1)]))],2),m(e.$slots,"default")],2)}]]);const Ll=Object.assign(Nl,{Item:Hl,ItemGroup:El,SubMenu:$l,install:(e,t)=>{ae(e,t);const l=se(t);e.component(l+Nl.name,Nl),e.component(l+Hl.name,Hl),e.component(l+El.name,El),e.component(l+$l.name,$l)}}),Fl=n({name:"Switch",components:{IconLoading:$e},props:{modelValue:{type:[String,Number,Boolean],default:void 0},defaultChecked:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},type:{type:String,default:"circle"},size:{type:String},checkedValue:{type:[String,Number,Boolean],default:!0},uncheckedValue:{type:[String,Number,Boolean],default:!1},checkedColor:{type:String},uncheckedColor:{type:String},beforeChange:{type:Function},checkedText:{type:String},uncheckedText:{type:String}},emits:{"update:modelValue":e=>!0,change:(e,t)=>!0,focus:e=>!0,blur:e=>!0},setup(t,{emit:l}){const{disabled:n,size:s,modelValue:i}=o(t),u=oe("switch"),{mergedSize:c}=e(s),{mergedDisabled:p,mergedSize:d,eventHandlers:m}=te({disabled:n,size:c}),v=r(t.defaultChecked?t.checkedValue:t.uncheckedValue),f=a((()=>{var e;return(null!=(e=t.modelValue)?e:v.value)===t.checkedValue})),g=r(!1),h=a((()=>g.value||t.loading)),y=(e,n)=>{var o,a;v.value=e?t.checkedValue:t.uncheckedValue,l("update:modelValue",v.value),l("change",v.value,n),null==(a=null==(o=m.value)?void 0:o.onChange)||a.call(o,n)};I(i,(e=>{(be(e)||Se(e))&&(v.value=t.uncheckedValue)}));const b=a((()=>[u,`${u}-type-${t.type}`,{[`${u}-small`]:"small"===d.value||"mini"===d.value,[`${u}-checked`]:f.value,[`${u}-disabled`]:p.value,[`${u}-loading`]:h.value,[`${u}-custom-color`]:"line"===t.type&&(t.checkedColor||t.uncheckedColor)}])),k=a((()=>f.value&&t.checkedColor?"line"===t.type?{"--custom-color":t.checkedColor}:{backgroundColor:t.checkedColor}:!f.value&&t.uncheckedColor?"line"===t.type?{"--custom-color":t.uncheckedColor}:{backgroundColor:t.uncheckedColor}:void 0));return{prefixCls:u,cls:b,mergedDisabled:p,buttonStyle:k,computedCheck:f,computedLoading:h,handleClick:async e=>{if(h.value||p.value)return;const l=!f.value,n=l?t.checkedValue:t.uncheckedValue,o=t.beforeChange;if(Pe(o)){g.value=!0;try{const t=await o(n);(null==t||t)&&y(l,e)}finally{g.value=!1}}else y(l,e)},handleFocus:e=>{var t,n;l("focus",e),null==(n=null==(t=m.value)?void 0:t.onFocus)||n.call(t,e)},handleBlur:e=>{var t,n;l("blur",e),null==(n=null==(t=m.value)?void 0:t.onBlur)||n.call(t,e)}}}}),Wl=["aria-checked","disabled"];var Ul=ie(Fl,[["render",function(e,t,l,n,o,a){const s=u("icon-loading");return c(),p("button",{type:"button",role:"switch","aria-checked":e.computedCheck,class:d(e.cls),style:h(e.buttonStyle),disabled:e.mergedDisabled,onClick:t[0]||(t[0]=(...t)=>e.handleClick&&e.handleClick(...t)),onFocus:t[1]||(t[1]=(...t)=>e.handleFocus&&e.handleFocus(...t)),onBlur:t[2]||(t[2]=(...t)=>e.handleBlur&&e.handleBlur(...t))},[g("span",{class:d(`${e.prefixCls}-handle`)},[g("span",{class:d(`${e.prefixCls}-handle-icon`)},[e.computedLoading?(c(),k(s,{key:0})):(c(),p($,{key:1},[e.computedCheck?m(e.$slots,"checked-icon",{key:0}):m(e.$slots,"unchecked-icon",{key:1})],2112))],2)],2),v("  prettier-ignore  "),"line"!==e.type&&"small"!==e.size&&(e.$slots.checked||e.checkedText||e.$slots.unchecked||e.uncheckedText)?(c(),p($,{key:0},[g("span",{class:d(`${e.prefixCls}-text-holder`)},[e.computedCheck?m(e.$slots,"checked",{key:0},(()=>[x(w(e.checkedText),1)])):m(e.$slots,"unchecked",{key:1},(()=>[x(w(e.uncheckedText),1)]))],2),g("span",{class:d(`${e.prefixCls}-text`)},[e.computedCheck?m(e.$slots,"checked",{key:0},(()=>[x(w(e.checkedText),1)])):m(e.$slots,"unchecked",{key:1},(()=>[x(w(e.uncheckedText),1)]))],2)],64)):v("v-if",!0)],46,Wl)}]]);const Gl=Object.assign(Ul,{install:(e,t)=>{ae(e,t);const l=se(t);e.component(l+Ul.name,Ul)}}),ql=n({__name:"form-wrapper",props:{type:{},name:{},defaultValue:{type:[Boolean,Number,String]}},emits:["inputChange"],setup(e,{emit:t}){const l=e,n=t;r("#f00");const o=e=>{n("inputChange",{value:e,key:l.name})},a=e=>{console.log("e :>> ",e),n("inputChange",{value:l.defaultValue,key:l.name})};return(e,t)=>{const l=u("color-picker"),n=Ae,s=Gl;return"string"===e.type?(c(),k(l,{key:0,onFinish:a,"show-opacity":!1,"default-color":e.defaultValue,hex:e.defaultValue,"onUpdate:hex":t[0]||(t[0]=t=>e.defaultValue=t)},null,8,["default-color","hex"])):"number"===e.type?(c(),k(n,{key:1,style:{width:"100px"},"hide-button":"",size:"small","default-value":e.defaultValue,onChange:o},{append:f((()=>t[1]||(t[1]=[x("px")]))),_:1},8,["default-value"])):(c(),k(s,{key:2,"default-checked":e.defaultValue,size:"small",onChange:o},null,8,["default-checked"]))}}}),Yl={class:"block"},Xl=Le(n({__name:"index",setup(e){const{copy:t}=R(),l=He(),n=a((()=>l.showGlobalSettingsDrawer)),o=a((()=>[{name:"导航栏",key:"showNavBar",defaultVal:l.showNavBar},{name:"菜单栏",key:"showSideMenu",defaultVal:l.showSideMenu},{name:"顶部菜单栏",key:"showTopMenu",defaultVal:l.showTopMenu},{name:"面包屑",key:"showBreadcrumb",defaultVal:l.showBreadcrumb},{name:"底部",key:"showFooter",defaultVal:l.showFooter},{name:"多页签",key:"showTabBar",defaultVal:l.showTabBar},{name:"菜单宽度 (px)",key:"menuWidth",defaultVal:l.menuWidth,type:"number"}])),i=()=>{l.updateAppSettings({showGlobalSettingsDrawer:!1})},r=async({key:e,value:t})=>{"showTopMenu"===e&&l.updateAppSettings({menuCollapse:!1}),l.updateAppSettings({[e]:t})},u=async()=>{const e=JSON.stringify(l.$state,null,2);await t(e),_e.success({title:"成功提示",content:"'复制成功，请粘贴到 src/config/settings.json 文件中'",duration:1500})};return(e,t)=>{const l=ne;return c(),k(l,{width:300,visible:K(n),"unmount-on-close":"","cancel-text":"关闭","cancel-button-props":{type:"outline"},"ok-text":"复制配置",onCancel:i,onOk:u},{title:f((()=>t[0]||(t[0]=[x("页面配置")]))),default:f((()=>[g("div",Yl,[t[1]||(t[1]=g("h5",{class:"title"},"内容区域",-1)),(c(!0),p($,null,P(K(o),(e=>(c(),p("div",{key:e.name,class:"switch-wrapper"},[g("span",null,w(e.name),1),s(ql,{type:e.type||"switch",name:e.key,"default-value":e.defaultVal,onInputChange:r},null,8,["type","name","default-value"])])))),128))])])),_:1},8,["visible"])}}}),[["__scopeId","data-v-a6b19252"]]),Zl=n({__name:"form",setup(e,{expose:t}){const l=b({oldPassword:void 0,newPassword:void 0,confirmPassword:void 0}),n=A("formRef");return t({formRef:n,form:l}),(e,t)=>{const o=Ke,a=Ue,i=Ge;return c(),k(i,{ref_key:"formRef",ref:n,model:K(l),layout:"vertical"},{default:f((()=>[t[3]||(t[3]=g("input",{type:"text",class:"fake-input"},null,-1)),t[4]||(t[4]=g("input",{type:"password",class:"fake-input"},null,-1)),s(a,{"show-colon":"",label:"旧密码",field:"oldPassword",rules:{required:!0,message:`${e.$inputPlaceholder}旧密码`}},{default:f((()=>[s(o,{modelValue:K(l).oldPassword,"onUpdate:modelValue":t[0]||(t[0]=e=>K(l).oldPassword=e),placeholder:`${e.$inputPlaceholder}旧密码`},null,8,["modelValue","placeholder"])])),_:1},8,["rules"]),s(a,{"show-colon":"",field:"newPassword",label:"新密码",rules:[{required:!0,message:`${e.$inputPlaceholder}新密码`},{match:/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[\da-zA-Z!#$%^&@*]{8,16}$/,message:"新密码长度8-16位，同时含有数字、大、小写英文字母，不含有空格"}]},{default:f((()=>[s(o,{modelValue:K(l).newPassword,"onUpdate:modelValue":t[1]||(t[1]=e=>K(l).newPassword=e),placeholder:"新密码长度8-16位，同时含有数字、大、小写英文字母，不含有空格"},null,8,["modelValue"])])),_:1},8,["rules"]),s(a,{"show-colon":"",label:"确认密码",field:"confirmPassword",rules:{required:!0,message:`${e.$inputPlaceholder}确认密码`}},{default:f((()=>[s(o,{modelValue:K(l).confirmPassword,"onUpdate:modelValue":t[2]||(t[2]=e=>K(l).confirmPassword=e),placeholder:`${e.$inputPlaceholder}确认密码`},null,8,["modelValue","placeholder"])])),_:1},8,["rules"])])),_:1},8,["model"])}}}),Jl=Le(n({__name:"index",setup(e){const t=He(),l=Me(),n=Ve(),o=a((()=>t.showChangePasswordModal)),{loading:i,setLoading:u}=qe(),p=r(),d=async()=>{var e,o;try{if(await(null==(o=null==(e=p.value)?void 0:e.formRef)?void 0:o.validate()))return;if(p.value.form.newPassword===p.value.form.oldPassword)return void _e.error({title:"错误提示",content:"新密码不能与旧密码相同",duration:1500});if(p.value.form.newPassword!==p.value.form.confirmPassword)return void _e.error({title:"错误提示",content:"两次密码输入不一致",duration:1500});u(!0),await Be({oldPassword:p.value.form.oldPassword,newPassword:p.value.form.newPassword}),u(!1),t.updateAppSettings({showChangePasswordModal:!1}),Ye.success({title:"密码已修改",content:()=>H("div",{class:"text-center"},"请重新登录"),maskClosable:!1,escToClose:!1,cancelButtonProps:{type:"outline"},onOk:()=>{Ie(),l.logout(),n.clearMenuList(),location.href="/"}})}catch(a){u(!1)}};return(e,l)=>{const n=Ye;return c(),k(n,{visible:K(o),"title-align":"start",title:"修改密码","cancel-button-props":{type:"outline"},"unmount-on-close":"","ok-loading":K(i),"mask-closable":!1,"esc-to-close":!1,onOk:d,onCancel:l[0]||(l[0]=e=>K(t).updateAppSettings({showChangePasswordModal:!1}))},{default:f((()=>[s(Zl,{ref_key:"changePasswordRef",ref:p},null,512)])),_:1},8,["visible","ok-loading"])}}}),[["__scopeId","data-v-35c0c2e5"]]),Ql={class:"navbar"},en={class:"left-side"},tn=["src"],ln={class:"center-side"},nn={class:"right-side"},on={class:"select-none"},an=Le(n({__name:"index",props:{messageCount:{default:0}},setup(e){E((e=>({"5a34eaf0":K(d)})));const{isFullscreen:t,toggle:l}=L(),n=He(),o=Me(),i=Ve();F();const r=a((()=>n.appTheme)),d=a((()=>n.menuWidth+"px")),v=a((()=>o.getSystemName)),h=a((()=>o.getNickName)),y=a((()=>o.getUserRole)),b=W({selector:"body",attribute:"arco-theme",valueDark:"dark",valueLight:"light",storageKey:"app-theme",onChanged(e){n.toggleAppTheme(e)}}),C=U(b),x=()=>{Ye.info({title:"提示",content:()=>H("div",{class:"text-center"},"确定退出当前登录？"),maskClosable:!1,escToClose:!1,hideCancel:!1,cancelButtonProps:{type:"outline"},onOk:()=>{Ie(),o.logout(),i.clearMenuList(),location.href="/"}})};return(e,o)=>{const a=u("icon-notification"),i=Q,d=mt,b=Re,$=u("icon-moon-fill"),S=u("icon-sun-fill"),P=u("icon-fullscreen-exit"),_=u("icon-fullscreen"),M=u("icon-user"),V=Qe,B=le,I=u("icon-settings"),O=yt,j=u("icon-export"),z=xt;return c(),p("div",Ql,[g("div",en,[g("img",{alt:"logo",style:{"margin-left":"10px",height:"28px"},src:K("/assets/logo-Clm_Yd5o.png")},null,8,tn),g("span",null,w(K(v)),1)]),g("div",ln,[m(e.$slots,"default",{},void 0,!0)]),g("ul",nn,[g("li",null,[s(b,{content:"消息通知"},{default:f((()=>[s(d,{count:e.messageCount},{default:f((()=>[s(i,{class:"nav-btn",type:"outline",shape:"circle",onClick:o[0]||(o[0]=()=>{K(_e).warning({title:"提示",content:"当前无消息通知",duration:1500})})},{icon:f((()=>[s(a)])),_:1})])),_:1},8,["count"])])),_:1})]),g("li",null,[s(b,{content:"light"===K(r)?"点击切换为黑暗模式":"点击切换为亮色模式"},{default:f((()=>[s(i,{class:"nav-btn",type:"outline",shape:"circle",onClick:o[1]||(o[1]=e=>K(C)())},{icon:f((()=>["dark"===K(r)?(c(),k($,{key:0})):(c(),k(S,{key:1}))])),_:1})])),_:1},8,["content"])]),g("li",null,[s(b,{content:K(t)?"点击退出全屏模式":"点击进入全屏模式"},{default:f((()=>[s(i,{class:"nav-btn",type:"outline",shape:"circle",onClick:K(l)},{icon:f((()=>[K(t)?(c(),k(P,{key:0})):(c(),k(_,{key:1}))])),_:1},8,["onClick"])])),_:1},8,["content"])]),g("li",null,[s(z,{trigger:"click"},{content:f((()=>[s(O,null,{default:f((()=>[s(B,{onClick:o[2]||(o[2]=e=>K(n).updateAppSettings({showChangePasswordModal:!0}))},{default:f((()=>[s(I),o[3]||(o[3]=g("span",null,"修改密码",-1))])),_:1})])),_:1}),s(O,null,{default:f((()=>[s(B,{onClick:x},{default:f((()=>[s(j),o[4]||(o[4]=g("span",null,"退出登录",-1))])),_:1})])),_:1})])),default:f((()=>[s(B,{class:"cursor-pointer"},{default:f((()=>[s(V,{size:32,style:{backgroundColor:"rgb(var(--primary-6))"}},{default:f((()=>[s(M,{style:{color:"white"}})])),_:1}),g("span",on,w(K(h))+"("+w(K(y))+")",1)])),_:1})])),_:1})])])])}}}),[["__scopeId","data-v-8825aafd"]]),sn=n({__name:"index",setup(e){const t=G(),l=r([]);return q((()=>{l.value="Home"===t.name?[]:t.matched.map((e=>{var t;return(null==(t=e.meta)?void 0:t.title)??""})).filter((e=>""!==e))})),(e,t)=>{const n=wt,o=St;return c(),k(o,{class:"container-breadcrumb"},{default:f((()=>[s(n,null,{default:f((()=>t[0]||(t[0]=[x("首页")]))),_:1}),(c(!0),p($,null,P(K(l),(e=>(c(),k(n,{key:e},{default:f((()=>[x(w(e),1)])),_:2},1024)))),128))])),_:1})}}}),rn={class:"tab-bar-container"},un={class:"scroll-container",style:{width:"fit-content"}},cn=["onClick"],pn=["onClick"],dn=Le(n({__name:"index",setup(e){const t=G(),l=F(),n=Ve(),o=r();I(t,(e=>{var t;n.addVisitedMenu({path:e.path,name:e.name,title:(null==(t=e.meta)?void 0:t.title)??"",params:e.params,query:e.query})}),{immediate:!0});return(e,a)=>{const i=u("icon-tag"),r=le,m=u("router-link"),v=u("icon-close"),h=Xe;return c(),p("div",rn,[s(h,{ref_key:"scrollRef",ref:o,style:{"overflow-x":"auto"}},{default:f((()=>[g("div",un,[s(m,{to:"/home",custom:""},{default:f((({navigate:e,isActive:t})=>[g("span",{onClick:e,class:d(["tab-bar-item",{active:K(n).getSelectedKeys.includes("Home")}])},[s(r,null,{default:f((()=>[s(i),a[0]||(a[0]=g("span",null,"首页",-1))])),_:1})],10,cn)])),_:1}),(c(!0),p($,null,P(K(n).getVisitedMenus,((e,o)=>(c(),k(m,{key:o,to:e.path,custom:""},{default:f((({navigate:a,isActive:u})=>[g("span",{onClick:a,class:d(["tab-bar-item",{active:K(n).getSelectedKeys.includes(e.name)}])},[s(r,null,{default:f((()=>[s(i),g("span",null,w(e.title),1),s(v,{onClick:Y((a=>((e,o)=>{e.path===t.path&&(o===n.getVisitedMenus.length-1?n.getVisitedMenus[o-1]?l.push(n.getVisitedMenus[o-1].path):l.push("/home"):l.push(n.getVisitedMenus[o+1].path)),n.removeVisitedMenu(e.path)})(e,o)),["prevent","stop"])},null,8,["onClick"])])),_:2},1024)],10,pn)])),_:2},1032,["to"])))),128))])])),_:1},512)])}}}),[["__scopeId","data-v-cc8a7d2e"]]),mn=n({__name:"menuItem",props:{menuItems:{}},setup(e){const t=F();return(e,l)=>{const n=u("menu-item",!0),o=$l,a=Hl;return c(),p("span",null,[(c(!0),p($,null,P(e.menuItems,(e=>{var l;return c(),p($,{key:e.name},[(null==(l=e.children)?void 0:l.length)?(c(),k(o,{key:e.name},{title:f((()=>{var t;return[x(w(null==(t=e.meta)?void 0:t.title),1)]})),default:f((()=>[s(n,{menuItems:e.children},null,8,["menuItems"])])),_:2},1024)):(c(),k(a,{key:e.name,onClick:l=>((e,l)=>{l&&"function"==typeof l.preventDefault&&l.preventDefault(),Oe.test(e.path)?je(e.path):t.push({name:e.name})})(e,l)},{default:f((()=>{var t;return[x(w(null==(t=e.meta)?void 0:t.title),1)]})),_:2},1032,["onClick"]))],64)})),128))])}}}),vn=n({__name:"index",setup(e){F();const t=Ve(),l=a((()=>t.getSelectedKeys)),n=a((()=>t.getMenus));return(e,t)=>{const o=Ll;return c(),k(o,{style:{flex:1,height:"100%"},mode:"horizontal","selected-keys":K(l)},{default:f((()=>[s(mn,{menuItems:K(n)},null,8,["menuItems"])])),_:1},8,["selected-keys"])}}}),fn=Le(n({__name:"menuItem",props:{menuItems:{}},setup(e){const t=F(),l=G();return(e,n)=>{const o=Ee,a=u("menu-item",!0),i=$l,r=Hl;return c(!0),p($,null,P(e.menuItems,(e=>{var n;return c(),p("div",{key:e.name,class:"menu-item"},[(null==(n=e.children)?void 0:n.length)?(c(),k(i,{key:e.name},{icon:f((()=>{var t,l;return[(null==(t=e.meta)?void 0:t.icon)?(c(),k(o,{key:0,icon:null==(l=e.meta)?void 0:l.icon},null,8,["icon"])):v("",!0)]})),title:f((()=>{var t;return[x(w(null==(t=e.meta)?void 0:t.title),1)]})),default:f((()=>[s(a,{menuItems:e.children},null,8,["menuItems"])])),_:2},1024)):(c(),k(r,{key:e.name,onClick:n=>((e,n)=>{n&&"function"==typeof n.preventDefault&&n.preventDefault(),Oe.test(e.path)?je(e.path):l.name!==e.name&&t.push({name:e.name})})(e,n)},{icon:f((()=>{var t,l;return[(null==(t=e.meta)?void 0:t.icon)?(c(),k(o,{key:0,icon:null==(l=e.meta)?void 0:l.icon},null,8,["icon"])):v("",!0)]})),default:f((()=>{var t;return[x(" "+w(null==(t=e.meta)?void 0:t.title),1)]})),_:2},1032,["onClick"]))])})),128)}}}),[["__scopeId","data-v-624dadbe"]]),gn=n({__name:"index",setup(e){F();const t=He(),l=Ve(),n=a((()=>l.getSelectedKeys)),o=a((()=>l.getMenus)),i=e=>{t.updateAppSettings({menuCollapse:e})};return(e,l)=>{const a=Ll;return c(),k(a,{style:{width:"100%",height:"100%"},collapsed:K(t).appMenuCollapse,"show-collapse-button":!0,accordion:"",breakpoint:"xl","auto-open-selected":"","selected-keys":K(n),onCollapse:i},{default:f((()=>[s(fn,{menuItems:K(o)},null,8,["menuItems"])])),_:1},8,["collapsed","selected-keys"])}}});const hn=Le({},[["render",function(e,t){const l=u("router-view");return c(),k(l,null,{default:f((({Component:e,route:t})=>[s(z,{name:"forward-transform",mode:"out-in",appear:""},{default:f((()=>[(c(),k(_(e),{key:t.fullPath}))])),_:2},1024)])),_:1})}],["__scopeId","data-v-f2b727a9"]]);function yn(e){const t=He();const l=X((function(){if(!document.hidden){const e=document.body.getBoundingClientRect().width-1<992;t.toggleDevice(e?"mobile":"desktop")}}),100);V((()=>{l()})),Z((()=>{!function(e,t,l,n=!1){e.addEventListener&&"function"==typeof e.addEventListener&&e.addEventListener(t,l,n)}(window,"resize",l)})),J((()=>{!function(e,t,l,n=!1){e.removeEventListener&&"function"==typeof e.removeEventListener&&e.removeEventListener(t,l,n)}(window,"resize",l)}))}const bn={key:0,class:"layout-navbar"},kn={class:"menu-wrapper"},Cn=Le(n({__name:"index",setup(e){E((e=>({"51dcd828":K(m),"01f003a5":K(h)}))),yn(),G();const t=He(),n=a((()=>t.showNavBar)),o=a((()=>t.showSideMenu&&!t.showTopMenu)),i=a((()=>t.showBreadcrumb)),u=a((()=>t.showTopMenu&&!t.showSideMenu)),d=a((()=>t.showTabBar)),m=a((()=>t.showNavBar?"60px":"0px")),h=a((()=>o.value?t.menuCollapse?"48px":t.menuWidth+"px":"0px")),y=a((()=>t.showFooter)),b=r(0);return(e,t)=>{const a=Jt,r=Bt,m=Qt;return c(),p($,null,[s(m,{class:"layout"},{default:f((()=>[K(n)?(c(),p("div",bn,[s(an,{messageCount:K(b)},{default:f((()=>[K(i)?(c(),k(sn,{key:0})):v("",!0),K(u)?(c(),k(vn,{key:1})):v("",!0)])),_:1},8,["messageCount"])])):v("",!0),s(m,null,{default:f((()=>[K(o)?(c(),k(a,{key:0,class:"layout-sider",breakpoint:"xl",width:parseInt(K(h)),collapsible:"",collapsed:!1,"hide-trigger":""},{default:f((()=>[g("div",kn,[s(gn)])])),_:1},8,["width"])):v("",!0),s(m,{class:"layout-content"},{default:f((()=>[K(d)?(c(),k(dn,{key:0})):v("",!0),s(r,{class:"layout-content-inner"},{default:f((()=>[s(hn)])),_:1}),K(y)?(c(),k(l,{key:1})):v("",!0)])),_:1})])),_:1})])),_:1}),s(Xl),s(Jl)],64)}}}),[["__scopeId","data-v-2890cd5c"]]),xn=n({__name:"main",setup:e=>(e,t)=>(c(),k(Cn))});export{xn as default};

import{r as e,d as l,t as o,c as a,o as t,b as n,w as s,f as i,j as r,y as c,z as d,aj as u,Z as p,k as f,v,A as m,a6 as y,l as b,O as C,a7 as g,p as w,q as k,af as h,m as x,I as O,J as B,U as S,n as T}from"./vue-D-10XvVk.js";import{ag as $,ah as j,v as M,w as A,I as D,a as I,ai as P,K as z,aj as E,ak as R,g as _,W as L,al as V,k as F,c as N,X as H,e as W,am as K,_ as Z,an as q,Y as J,Z as U,s as X,f as Y}from"./index-D-8JbLQk.js";import{C as G,a as Q,h as ee,K as le,o as oe}from"./index-DDFSMqsG.js";import{B as ae}from"./index-DGtjsHgS.js";const te=l=>{const o=e(!1),a={overflow:"",width:"",boxSizing:""};return{setOverflowHidden:()=>{if(l.value){const e=l.value;if(!o.value&&"hidden"!==e.style.overflow){const l=$(e);(l>0||j(e))&&(a.overflow=e.style.overflow,a.width=e.style.width,a.boxSizing=e.style.boxSizing,e.style.overflow="hidden",e.style.width=e.offsetWidth-l+"px",e.style.boxSizing="border-box",o.value=!0)}}},resetOverflow:()=>{if(l.value&&o.value){const e=l.value;e.style.overflow=a.overflow,e.style.width=a.width,e.style.boxSizing=a.boxSizing,o.value=!1}}}},ne=({modalRef:l,wrapperRef:o,draggable:a,alignCenter:t})=>{const n=e(!1),s=e([0,0]),i=e([0,0]),r=e(),c=e([0,0]),d=e([0,0]),u=e=>{if(n.value){const l=e.x-s.value[0],o=e.y-s.value[1];let a=i.value[0]+l,t=i.value[1]+o;a<c.value[0]&&(a=c.value[0]),a>d.value[0]&&(a=d.value[0]),t<c.value[1]&&(t=c.value[1]),t>d.value[1]&&(t=d.value[1]),r.value=[a,t]}},p=()=>{n.value=!1,A(window,"mousemove",u),A(window,"mouseup",p)};return{position:r,handleMoveDown:e=>{a.value&&(e.preventDefault(),n.value=!0,(()=>{var e,a,n;if(o.value&&l.value){const{top:s,left:r}=o.value.getBoundingClientRect(),{clientWidth:u,clientHeight:p}=o.value,{top:f,left:v,width:m,height:y}=l.value.getBoundingClientRect(),b=t.value?0:null==(e=l.value)?void 0:e.offsetTop,C=v-r,g=f-s-b;C===(null==(a=i.value)?void 0:a[0])&&g===(null==(n=i.value)?void 0:n[1])||(i.value=[C,g]);const w=u>m?u-m:0,k=p>y?p-y-b:0;w===d.value[0]&&k===d.value[1]||(d.value=[w,k]),b&&(c.value=[0,0-b])}})(),s.value=[e.x,e.y],M(window,"mousemove",u),M(window,"mouseup",p),M(window,"contextmenu",p))}}};var se=Object.defineProperty,ie=Object.getOwnPropertySymbols,re=Object.prototype.hasOwnProperty,ce=Object.prototype.propertyIsEnumerable,de=(e,l,o)=>l in e?se(e,l,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[l]=o,ue=l({name:"Modal",components:{ClientOnly:G,ArcoButton:ae,IconHover:D,IconClose:I,IconInfoCircleFill:P,IconCheckCircleFill:z,IconExclamationCircleFill:E,IconCloseCircleFill:R},inheritAttrs:!1,props:{visible:{type:Boolean,default:void 0},defaultVisible:{type:Boolean,default:!1},width:{type:[Number,String]},top:{type:[Number,String]},mask:{type:Boolean,default:!0},title:{type:String},titleAlign:{type:String,default:"center"},alignCenter:{type:Boolean,default:!0},unmountOnClose:Boolean,maskClosable:{type:Boolean,default:!0},hideCancel:{type:Boolean,default:!1},simple:{type:Boolean,default:e=>e.notice},closable:{type:Boolean,default:!0},okText:String,cancelText:String,okLoading:{type:Boolean,default:!1},okButtonProps:{type:Object},cancelButtonProps:{type:Object},footer:{type:Boolean,default:!0},renderToBody:{type:Boolean,default:!0},popupContainer:{type:[String,Object],default:"body"},maskStyle:{type:Object},modalClass:{type:[String,Array]},modalStyle:{type:Object},onBeforeOk:{type:Function},onBeforeCancel:{type:Function},escToClose:{type:Boolean,default:!0},draggable:{type:Boolean,default:!1},fullscreen:{type:Boolean,default:!1},maskAnimationName:{type:String,default:e=>e.fullscreen?"fade-in-standard":"fade-modal"},modalAnimationName:{type:String,default:e=>e.fullscreen?"zoom-in":"zoom-modal"},bodyClass:{type:[String,Array]},bodyStyle:{type:[String,Object,Array]},messageType:{type:String},hideTitle:{type:Boolean,default:!1}},emits:{"update:visible":e=>!0,ok:e=>!0,cancel:e=>!0,open:()=>!0,close:()=>!0,beforeOpen:()=>!0,beforeClose:()=>!0},setup(l,{emit:i}){const{fullscreen:r,popupContainer:c,alignCenter:d}=o(l),u=_("modal"),{t:p}=Q(),f=e(),v=e(),m=e(l.defaultVisible),y=a((()=>{var e;return null!=(e=l.visible)?e:m.value})),b=e(!1),C=a((()=>l.okLoading||b.value)),g=a((()=>l.draggable&&!l.fullscreen)),{teleportContainer:w,containerRef:k}=ee({popupContainer:c,visible:y}),h=e(y.value),x=a((()=>l.okText||p("modal.okText"))),O=a((()=>l.cancelText||p("modal.cancelText"))),{zIndex:B,isLastDialog:S}=L("dialog",{visible:y});let T=!1;const $=e=>{l.escToClose&&e.key===le.ESC&&S()&&R(e)},j=()=>{l.escToClose&&!T&&(T=!0,M(document.documentElement,"keydown",$))},D=()=>{T=!1,A(document.documentElement,"keydown",$)};let I=0;const{position:P,handleMoveDown:z}=ne({wrapperRef:f,modalRef:v,draggable:g,alignCenter:d}),E=()=>{I++,b.value&&(b.value=!1),m.value=!1,i("update:visible",!1)},R=e=>{var o;let a=!0;N(l.onBeforeCancel)&&(a=null!=(o=l.onBeforeCancel())&&o),a&&(i("cancel",e),E())},Z=e(!1),{setOverflowHidden:q,resetOverflow:J}=te(k);t((()=>{k.value=V(l.popupContainer),y.value&&(q(),l.escToClose&&j())})),n((()=>{J(),D()})),s(y,(e=>{m.value!==e&&(m.value=e),e?(i("beforeOpen"),h.value=!0,Z.value=!1,q(),j()):(i("beforeClose"),D())})),s(r,(()=>{P.value&&(P.value=void 0)}));const U=a((()=>[`${u}-wrapper`,{[`${u}-wrapper-align-center`]:l.alignCenter&&!l.fullscreen,[`${u}-wrapper-moved`]:Boolean(P.value)}])),X=a((()=>[`${u}`,l.modalClass,{[`${u}-simple`]:l.simple,[`${u}-draggable`]:g.value,[`${u}-fullscreen`]:l.fullscreen}])),Y=a((()=>{var e;const o=((e,l)=>{for(var o in l||(l={}))re.call(l,o)&&de(e,o,l[o]);if(ie)for(var o of ie(l))ce.call(l,o)&&de(e,o,l[o]);return e})({},null!=(e=l.modalStyle)?e:{});return l.width&&!l.fullscreen&&(o.width=F(l.width)?`${l.width}px`:l.width),!l.alignCenter&&l.top&&(o.top=F(l.top)?`${l.top}px`:l.top),P.value&&(o.transform=`translate(${P.value[0]}px, ${P.value[1]}px)`),o}));return{prefixCls:u,mounted:h,computedVisible:y,containerRef:k,wrapperRef:f,mergedModalStyle:Y,okDisplayText:x,cancelDisplayText:O,zIndex:B,handleOk:async e=>{const o=I,a=await new Promise((async e=>{var o;if(N(l.onBeforeOk)){let t=l.onBeforeOk(((l=!0)=>e(l)));if(!H(t)&&W(t)||(b.value=!0),H(t))try{t=null==(o=await t)||o}catch(a){t=!1}W(t)&&e(t)}else e(!0)}));o===I&&(a?(i("ok",e),E()):b.value&&(b.value=!1))},handleCancel:R,handleMaskClick:e=>{l.mask&&l.maskClosable&&Z.value&&R(e)},handleMaskMouseDown:e=>{e.target===f.value&&(Z.value=!0)},handleOpen:()=>{y.value&&(!K(f.value,document.activeElement)&&document.activeElement instanceof HTMLElement&&document.activeElement.blur(),i("open"))},handleClose:()=>{y.value||(g.value&&(P.value=void 0),h.value=!1,J(),i("close"))},mergedOkLoading:C,modalRef:v,wrapperCls:U,modalCls:X,teleportContainer:w,handleMoveDown:z}}});var pe=Z(ue,[["render",function(e,l,o,a,t,n){const s=i("icon-info-circle-fill"),S=i("icon-check-circle-fill"),T=i("icon-exclamation-circle-fill"),$=i("icon-close-circle-fill"),j=i("icon-close"),M=i("icon-hover"),A=i("arco-button"),D=i("client-only");return r(),c(D,null,{default:d((()=>[(r(),c(u,{to:e.teleportContainer,disabled:!e.renderToBody},[!e.unmountOnClose||e.computedVisible||e.mounted?p((r(),f("div",v({key:0,class:`${e.prefixCls}-container`,style:{zIndex:e.zIndex}},e.$attrs),[m(y,{name:e.maskAnimationName,appear:""},{default:d((()=>[e.mask?p((r(),f("div",{key:0,ref:"maskRef",class:b(`${e.prefixCls}-mask`),style:C(e.maskStyle)},null,6)),[[g,e.computedVisible]]):w("v-if",!0)])),_:1},8,["name"]),k("div",{ref:"wrapperRef",class:b(e.wrapperCls),onClick:l[2]||(l[2]=h(((...l)=>e.handleMaskClick&&e.handleMaskClick(...l)),["self"])),onMousedown:l[3]||(l[3]=h(((...l)=>e.handleMaskMouseDown&&e.handleMaskMouseDown(...l)),["self"]))},[m(y,{name:e.modalAnimationName,appear:"",onAfterEnter:e.handleOpen,onAfterLeave:e.handleClose},{default:d((()=>[p(k("div",{ref:"modalRef",class:b(e.modalCls),style:C(e.mergedModalStyle)},[!e.hideTitle&&(e.$slots.title||e.title||e.closable)?(r(),f("div",{key:0,class:b(`${e.prefixCls}-header`),onMousedown:l[1]||(l[1]=(...l)=>e.handleMoveDown&&e.handleMoveDown(...l))},[e.$slots.title||e.title?(r(),f("div",{key:0,class:b([`${e.prefixCls}-title`,`${e.prefixCls}-title-align-${e.titleAlign}`])},[e.messageType?(r(),f("div",{key:0,class:b(`${e.prefixCls}-title-icon`)},["info"===e.messageType?(r(),c(s,{key:0})):w("v-if",!0),"success"===e.messageType?(r(),c(S,{key:1})):w("v-if",!0),"warning"===e.messageType?(r(),c(T,{key:2})):w("v-if",!0),"error"===e.messageType?(r(),c($,{key:3})):w("v-if",!0)],2)):w("v-if",!0),x(e.$slots,"title",{},(()=>[O(B(e.title),1)]))],2)):w("v-if",!0),!e.simple&&e.closable?(r(),f("div",{key:1,tabindex:"-1",role:"button","aria-label":"Close",class:b(`${e.prefixCls}-close-btn`),onClick:l[0]||(l[0]=(...l)=>e.handleCancel&&e.handleCancel(...l))},[m(M,null,{default:d((()=>[m(j)])),_:1})],2)):w("v-if",!0)],34)):w("v-if",!0),k("div",{class:b([`${e.prefixCls}-body`,e.bodyClass]),style:C(e.bodyStyle)},[x(e.$slots,"default")],6),e.footer?(r(),f("div",{key:1,class:b(`${e.prefixCls}-footer`)},[x(e.$slots,"footer",{},(()=>[e.hideCancel?w("v-if",!0):(r(),c(A,v({key:0},e.cancelButtonProps,{onClick:e.handleCancel}),{default:d((()=>[O(B(e.cancelDisplayText),1)])),_:1},16,["onClick"])),m(A,v({type:"primary"},e.okButtonProps,{loading:e.mergedOkLoading,onClick:e.handleOk}),{default:d((()=>[O(B(e.okDisplayText),1)])),_:1},16,["loading","onClick"])]))],2)):w("v-if",!0)],6),[[g,e.computedVisible]])])),_:3},8,["name","onAfterEnter","onAfterLeave"])],34)],16)),[[g,e.computedVisible||e.mounted]]):w("v-if",!0)],8,["to","disabled"]))])),_:3})}]]),fe=Object.defineProperty,ve=Object.defineProperties,me=Object.getOwnPropertyDescriptors,ye=Object.getOwnPropertySymbols,be=Object.prototype.hasOwnProperty,Ce=Object.prototype.propertyIsEnumerable,ge=(e,l,o)=>l in e?fe(e,l,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[l]=o,we=(e,l)=>{for(var o in l||(l={}))be.call(l,o)&&ge(e,o,l[o]);if(ye)for(var o of ye(l))Ce.call(l,o)&&ge(e,o,l[o]);return e};const ke=(e,l)=>{let o=J("modal");const a=m(pe,we(we(we({},{visible:!0,renderToBody:!1,unmountOnClose:!0,onOk:()=>{a.component&&(a.component.props.visible=!1),N(e.onOk)&&e.onOk()},onCancel:()=>{a.component&&(a.component.props.visible=!1),N(e.onCancel)&&e.onCancel()},onClose:async()=>{await T(),o&&(S(null,o),document.body.removeChild(o)),o=null,N(e.onClose)&&e.onClose()}}),oe(e,["content","title","footer","visible","unmountOnClose","onOk","onCancel","onClose"])),{footer:"boolean"==typeof e.footer?e.footer:void 0}),{default:U(e.content),title:U(e.title),footer:"boolean"!=typeof e.footer?U(e.footer):void 0});return(null!=l?l:xe._context)&&(a.appContext=null!=l?l:xe._context),S(a,o),document.body.appendChild(o),{close:()=>{a.component&&(a.component.props.visible=!1)},update:e=>{a.component&&Object.entries(e).forEach((([e,l])=>{a.component.props[e]=l}))}}},he=we({open:ke,confirm:(e,l)=>{const o=we({simple:!0,messageType:"warning"},e);return ke(o,l)}},q.reduce(((e,l)=>(e[l]=(e,o)=>{const a=we({simple:!0,hideCancel:!0,messageType:l},e);return ke(a,o)},e)),{})),xe=Object.assign(pe,(Oe=we({},he),ve(Oe,me({install:(e,l)=>{X(e,l);const o=Y(l);e.component(o+pe.name,pe);const a={};for(const t of Object.keys(he))a[t]=(l,o=e._context)=>he[t](l,o);e.config.globalProperties.$modal=a},_context:null}))));var Oe;export{xe as M,te as u};

import{_ as e,B as s,g as l,a5 as t,s as a,f as n,l as i,ae as o,k as r}from"./index-D-8JbLQk.js";import{u as d}from"./index-DOhy6BH_.js";import{i as u,c,a5 as p,d as m,t as f,f as y,j as g,k as v,l as $,y as b,m as h,p as k,a3 as z,B as S,an as C,A as B,F as x}from"./vue-D-10XvVk.js";const j=Symbol("ArcoFormItemContext"),w=Symbol("ArcoFormContext"),D=({size:e,disabled:s,error:l,uninject:t}={})=>{const a=t?{}:u(j,{}),n=c((()=>{var s;return null!=(s=null==e?void 0:e.value)?s:a.size})),i=c((()=>(null==s?void 0:s.value)||a.disabled)),o=c((()=>(null==l?void 0:l.value)||a.error)),r=p(a,"feedback"),d=p(a,"eventHandlers");return{formItemCtx:a,mergedSize:n,mergedDisabled:i,mergedError:o,feedback:r,eventHandlers:d}},A=Symbol("ArcoButtonGroup"),F=m({name:"Button",components:{IconLoading:s},props:{type:{type:String},shape:{type:String},status:{type:String},size:{type:String},long:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},disabled:{type:Boolean},htmlType:{type:String,default:"button"},autofocus:{type:Boolean,default:!1},href:String},emits:{click:e=>!0},setup(e,{emit:s}){const{size:a,disabled:n}=f(e),i=l("btn"),o=u(A,void 0),r=c((()=>{var e;return null!=(e=a.value)?e:null==o?void 0:o.size})),p=c((()=>Boolean(n.value||(null==o?void 0:o.disabled)))),{mergedSize:m,mergedDisabled:y}=D({size:r,disabled:p}),{mergedSize:g}=d(m),v=c((()=>{var s,l,a,n,r,d;return[i,`${i}-${null!=(l=null!=(s=e.type)?s:null==o?void 0:o.type)?l:"secondary"}`,`${i}-shape-${null!=(n=null!=(a=e.shape)?a:null==o?void 0:o.shape)?n:"square"}`,`${i}-size-${g.value}`,`${i}-status-${null!=(d=null!=(r=e.status)?r:null==o?void 0:o.status)?d:"normal"}`,{[`${i}-long`]:e.long,[`${i}-loading`]:e.loading,[`${i}-disabled`]:y.value,[`${i}-link`]:t(e.href)}]}));return{prefixCls:i,cls:v,mergedDisabled:y,handleClick:l=>{e.disabled||e.loading?l.preventDefault():s("click",l)}}}}),G=["href"],I=["type","disabled","autofocus"];var H=e(F,[["render",function(e,s,l,t,a,n){const i=y("icon-loading");return e.href?(g(),v("a",{key:0,class:$([e.cls,{[`${e.prefixCls}-only-icon`]:e.$slots.icon&&!e.$slots.default}]),href:e.mergedDisabled||e.loading?void 0:e.href,onClick:s[0]||(s[0]=(...s)=>e.handleClick&&e.handleClick(...s))},[e.loading||e.$slots.icon?(g(),v("span",{key:0,class:$(`${e.prefixCls}-icon`)},[e.loading?(g(),b(i,{key:0,spin:"true"})):h(e.$slots,"icon",{key:1})],2)):k("v-if",!0),h(e.$slots,"default")],10,G)):(g(),v("button",{key:1,class:$([e.cls,{[`${e.prefixCls}-only-icon`]:e.$slots.icon&&!e.$slots.default}]),type:e.htmlType,disabled:e.mergedDisabled,autofocus:e.autofocus,onClick:s[1]||(s[1]=(...s)=>e.handleClick&&e.handleClick(...s))},[e.loading||e.$slots.icon?(g(),v("span",{key:0,class:$(`${e.prefixCls}-icon`)},[e.loading?(g(),b(i,{key:0,spin:!0})):h(e.$slots,"icon",{key:1})],2)):k("v-if",!0),h(e.$slots,"default")],10,I))}]]);var O=e(m({name:"ButtonGroup",props:{type:{type:String},status:{type:String},shape:{type:String},size:{type:String},disabled:{type:Boolean}},setup(e){const{type:s,size:t,status:a,disabled:n,shape:i}=f(e),o=l("btn-group");return z(A,S({type:s,size:t,shape:i,status:a,disabled:n})),{prefixCls:o}}}),[["render",function(e,s,l,t,a,n){return g(),v("div",{class:$(e.prefixCls)},[h(e.$slots,"default")],2)}]]);const T=Object.assign(H,{Group:O,install:(e,s)=>{a(e,s);const l=n(s);e.component(l+H.name,H),e.component(l+O.name,O)}});var q=m({name:"Space",props:{align:{type:String},direction:{type:String,default:"horizontal"},size:{type:[Number,String,Array],default:"small"},wrap:{type:Boolean},fill:{type:Boolean}},setup(e,{slots:s}){const t=l("space"),a=c((()=>{var s;return null!=(s=e.align)?s:"horizontal"===e.direction?"center":""})),n=c((()=>[t,{[`${t}-${e.direction}`]:e.direction,[`${t}-align-${a.value}`]:a.value,[`${t}-wrap`]:e.wrap,[`${t}-fill`]:e.fill}]));function d(e){if(r(e))return e;switch(e){case"mini":return 4;case"small":default:return 8;case"medium":return 16;case"large":return 24}}const u=s=>{const l={},t=`${d(o(e.size)?e.size[0]:e.size)}px`,a=`${d(o(e.size)?e.size[1]:e.size)}px`;return s?e.wrap?{marginBottom:a}:{}:("horizontal"===e.direction&&(l.marginRight=t),("vertical"===e.direction||e.wrap)&&(l.marginBottom=a),l)};return()=>{var e;const l=i(null==(e=s.default)?void 0:e.call(s),!0).filter((e=>e.type!==C));return B("div",{class:n.value},[l.map(((e,a)=>{var n,i;const o=s.split&&a>0;return B(x,{key:null!=(n=e.key)?n:`item-${a}`},[o&&B("div",{class:`${t}-item-split`,style:u(!1)},[null==(i=s.split)?void 0:i.call(s)]),B("div",{class:`${t}-item`,style:u(a===l.length-1)},[e])])}))])}}});const E=Object.assign(q,{install:(e,s)=>{a(e,s);const l=n(s);e.component(l+q.name,q)}});export{T as B,E as S,O as a,j as b,w as f,D as u};

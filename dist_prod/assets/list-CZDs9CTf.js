const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/detail-BEEYEW2J.js","assets/index-DOhy6BH_.js","assets/index-D-8JbLQk.js","assets/vue-D-10XvVk.js","assets/index-DxPaQOvH.css","assets/index-DB09tZwb.css","assets/index-BEo1tUsK.js","assets/pick-Ccd8Sfcm.js","assets/index-6rnfXikd.css","assets/index-DVDXfQhn.js","assets/index-DGtjsHgS.js","assets/index-BJBnsrKF.css","assets/index-DDFSMqsG.js","assets/ResizeObserver.es-CzGuHLZU.js","assets/index-RZyF5P1Y.css","assets/index-8er2yjEK.css","assets/index-Cuq5XRs0.js","assets/resize-observer-Dtogi-DJ.js","assets/index-DD6vSYIM.js","assets/index-C0ni2jp2.css","assets/index-CdWxsKz_.js","assets/index-CX9L_GU1.css","assets/index-DQjhgQFu.js","assets/index-Db7LPRu1.css","assets/index-Dbgee0nK.css","assets/index-dpn1_5z1.js","assets/dayjs.min-Daes5FZc.js","assets/render-function-CAXdZVZM.js","assets/index-Cf9H8fsj.css","assets/index-HLwNT5T9.js","assets/index-CuYx5vtf.js","assets/index-BZi9bKOJ.css","assets/apiUpload-DpATemHF.js","assets/_plugin-vue_export-helper-BCo6x5W8.js","assets/index-IkV6U84s.css","assets/useCommon-BuUbRw8e.js","assets/apiCommon-DcubqwY_.js","assets/index-DiBSSeoD.js","assets/index-B4zgCFsq.css","assets/index-DkZuZOQi.js","assets/index-BcWAySBs.css","assets/detail-DhpFZqm3.js","assets/index-DIKBiUsz.js","assets/use-index-D_ozg7PK.js","assets/index-Cf1pvoHl.css","assets/index-O7pr3qsq.js","assets/useAddress-CutR4aE-.js","assets/hooks-BLzyyGa3.js","assets/useLoading-D5mh7tTu.js","assets/usePagination-Dd_EW2BO.js","assets/apiStore-vHDpafS2.js","assets/detail-mVd9Ns1A.css","assets/index-DfEXMvnc.js","assets/index-DdMaxvYa.js","assets/use-children-components-v8i8lsOx.js","assets/index-B5FzkxT_.css","assets/index-C7OqXj1S.js","assets/index-CUtvFEc_.js","assets/index-CHOaln3D.js","assets/index-komh9C6_.css","assets/hooks-Xhdb4ByR.js","assets/detail-B36dfyfG.css","assets/index-BlQqQ5bI.css","assets/index-CJ6Fn8S6.css"])))=>i.map(i=>d[i]);
import{O as e,m as a,N as t}from"./index-D-8JbLQk.js";import"./index-DOhy6BH_.js";import{C as i}from"./index-CdWxsKz_.js";import"./index-DDFSMqsG.js";import{T as o,L as n}from"./index-Cuq5XRs0.js";import"./index-DD6vSYIM.js";import"./index-DQjhgQFu.js";import{T as l,a as s,P as r}from"./index-DdMaxvYa.js";import{B as d,S as c}from"./index-DGtjsHgS.js";import{D as u}from"./index-DmW4RN1x.js";/* empty css              */import{_ as p}from"./index-HLwNT5T9.js";import{C as m,R as f}from"./index-BEo1tUsK.js";/* empty css              */import{B as g,r as y,c as v,d as h,K as x,o as w,f as k,j as _,k as b,A as C,z as j,u as S,e as I,q as T,y as z,p as P,I as B,J as O,af as Y,M as E,N as M,h as R}from"./vue-D-10XvVk.js";import{u as V}from"./useCommon-BuUbRw8e.js";import{u as D}from"./useLoading-D5mh7tTu.js";import{u as N}from"./usePagination-Dd_EW2BO.js";import{d as q}from"./dayjs.min-Daes5FZc.js";import{M as L}from"./index-O7pr3qsq.js";import{I as U}from"./index-DfEXMvnc.js";const A=a=>{const{loading:t,setLoading:i}=D(),{pagination:o}=N(),n=g({}),l=y(void 0),s=v((()=>l.value?c.value.find((e=>e.id===l.value)):null)),r=y([]),d=v((()=>r.value.length?c.value.filter((e=>r.value.includes(e.id))):[])),c=y([]),u=async()=>{i(!0),l.value=void 0,r.value=[];try{const{data:t}=await(a=>e({url:"/admin/platform/banner/list",method:"post",data:a}))({...n,position:a,pageNum:o.current,pageSize:o.pageSize});c.value=t.rows.map((e=>(e.createTime=q(e.createTime).format("YYYY-MM-DD HH:mm:ss"),e.expirationEndTime=q(e.expirationEndTime).format("YYYY-MM-DD HH:mm:ss"),e))),o.total=t.total,i(!1)}catch(t){c.value=[],o.total=0,i(!1)}},p=g({picturePath:void 0,expirationEndTime:void 0,linkType:void 0,storeCategoryId:void 0,storeSubCategoryId:void 0,storeId:void 0,activityId:void 0,linkContent:void 0,linkUrl:void 0});return{loading:t,queryParams:n,pagination:o,rows:c,selectedId:l,selectedRow:s,selectedIds:r,selectedRows:d,selectAll:e=>{r.value=e?c.value.map((e=>e.id)):[]},rowSelect:(e,a,t)=>{r.value.includes(t.id)?r.value.splice(r.value.indexOf(t.id),1):r.value.push(t.id)},rowClick:e=>{r.value.includes(e.id)?r.value.splice(r.value.indexOf(e.id),1):r.value.push(e.id)},query:u,reset:()=>{o.current=1,Object.assign(n,{}),u()},pageChange:async e=>{o.current=e,u()},pageSizeChange:async e=>{o.current=1,o.pageSize=e,u()},form:p,add:async t=>{try{await(a=>e({url:"/admin/platform/banner/add",method:"put",data:a}))({...t,position:a})}catch(i){throw i}},detail:async a=>{try{const{data:t}=await(a=>e({url:`/admin/platform/banner/detail/${a}`,method:"get"}))(a);Object.assign(p,t)}catch(t){throw t}},del:async a=>{try{await(a=>e({url:`/admin/platform/banner/del/${a}`,method:"delete"}))(a)}catch(t){throw t}},setSortIndex:async(a,t)=>{try{await((a,t)=>e({url:`/admin/platform/banner/setSortIndex/${a}`,method:"put",data:{sortIndex:t}}))(a,t)}catch(i){throw i}}}},H={class:"pt-[18px] h-full"},$={class:"h-full flex flex-col gap-[18px]"},J={key:0},K={key:1},F={key:2},G={key:3},Q={key:1},W=h({__name:"list",props:{position:{}},setup(e){const g=e,v=E((()=>a((()=>import("./detail-BEEYEW2J.js").then((e=>e.i))),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63])))),{bannerLinkTypeOptions:h}=V(),{loading:D,queryParams:N,pagination:q,rows:W,selectedId:X,selectedIds:Z,selectAll:ee,rowSelect:ae,rowClick:te,query:ie,reset:oe,pageChange:ne,pageSizeChange:le,add:se,del:re,setSortIndex:de}=A(g.position),ce=x("addRef"),ue=y(!1),pe=async e=>{var a,i;try{if(await(null==(i=null==(a=ce.value)?void 0:a.formRef)?void 0:i.validate()))throw new Error("校验失败");await se(M(ce.value.form)),t.success({title:"成功提示",content:"已添加Banner",duration:1500}),e(!0),ie()}catch(o){e(!1)}};return w((()=>{ne(1)})),(e,a)=>{const g=L,y=k("icon-plus"),x=d,w=c,E=m,M=f,V=l,N=p,A=o,Z=U,ee=k("icon-check"),ae=k("icon-close"),te=u,oe=n,se=s,me=r,fe=i;return _(),b("div",H,[C(g,{visible:S(ue),"onUpdate:visible":a[0]||(a[0]=e=>I(ue)?ue.value=e:null),width:800,"title-align":"start",title:"添加Banner","cancel-button-props":{type:"outline"},"unmount-on-close":"","mask-closable":!1,"esc-to-close":!1,"on-before-ok":pe,onCancel:a[1]||(a[1]=e=>ue.value=!1)},{default:j((()=>[C(S(v),{ref_key:"addRef",ref:ce,position:e.position},null,8,["position"])])),_:1},8,["visible"]),T("div",$,[C(fe,{bordered:!1,class:"flex-1 overflow-y-hidden","body-style":{height:"100%"}},{actions:j((()=>[S(q).total?(_(),z(me,{key:0,current:S(q).current,"page-size":S(q).pageSize,"show-total":S(q).showTotal,"show-page-size":S(q).showPageSize,"page-size-options":S(q).pageSizeOptions,total:S(q).total,onChange:S(ne),onPageSizeChange:S(le)},null,8,["current","page-size","show-total","show-page-size","page-size-options","total","onChange","onPageSizeChange"])):P("",!0)])),default:j((()=>[C(M,{class:"mb-[12px]"},{default:j((()=>[C(E,{span:16},{default:j((()=>[C(w,null,{default:j((()=>[C(x,{type:"primary",onClick:a[2]||(a[2]=()=>{X.value=void 0,ue.value=!0})},{icon:j((()=>[C(y)])),default:j((()=>[a[3]||(a[3]=B(" 添加Banner "))])),_:1})])),_:1})])),_:1})])),_:1}),C(se,{size:"large","row-key":"id",loading:S(D),pagination:!1,data:S(W),bordered:{cell:!0},scroll:{y:"calc(100% - 96px)"}},{columns:j((()=>[C(V,{align:"center",title:"序号",width:80},{cell:j((({rowIndex:e})=>[B(O(S(q).pageSize*(S(q).current-1)+e+1),1)])),_:1}),C(V,{align:"center",title:"Banner图",width:300},{cell:j((({record:e})=>[C(N,{modelValue:e.picturePath,disabled:"",class:"!justify-center"},null,8,["modelValue"])])),_:1}),C(V,{align:"center",title:"关联信息",width:300},{cell:j((({record:e})=>[C(w,null,{default:j((()=>{var a;return[C(A,{color:(null==(a=S(h).find((a=>a.value===e.linkType)))?void 0:a.color)??""},{default:j((()=>{var a;return[B(O((null==(a=S(h).find((a=>a.value===e.linkType)))?void 0:a.label)??""),1)]})),_:2},1032,["color"]),1===e.linkType?(_(),b("span",J,"分类信息："+O(e.storeCategoryName)+" - "+O(e.storeSubCategoryName),1)):2===e.linkType?(_(),b("span",K,"链接店铺："+O(e.storeName),1)):3===e.linkType?(_(),b("span",F,"链接地址："+O(e.linkUrl),1)):4===e.linkType?(_(),b("span",G,"活动标题："+O(e.activityTitle),1)):P("",!0)]})),_:2},1024)])),_:1}),C(V,{align:"center",title:"排序",width:160},{cell:j((({record:e})=>[T("div",null,[e.showSetSortIndex?(_(),z(w,{key:0},{default:j((()=>[C(Z,{modelValue:e.sortIndex,"onUpdate:modelValue":a=>e.sortIndex=a,"hide-button":"",min:1,max:9999,disabled:!e.showSetSortIndex},null,8,["modelValue","onUpdate:modelValue","disabled"]),C(x,{type:"primary",onClick:Y((a=>(async e=>{try{await de(e.id,e.sortIndex),t.success({title:"成功提示",content:"已设置Banner顺序",duration:1500}),ie()}catch(a){}})(e)),["stop"])},{icon:j((()=>[C(ee)])),_:2},1032,["onClick"]),C(x,{type:"primary",status:"warning",onClick:Y(S(ie),["stop"])},{icon:j((()=>[C(ae)])),_:1},8,["onClick"])])),_:2},1024)):(_(),b("span",Q,O(e.sortIndex),1))])])),_:1}),C(V,{align:"center",title:"展示有效期至",width:180,"data-index":"expirationEndTime"}),C(V,{align:"center",title:"操作",width:200,fixed:"right"},{cell:j((({record:e})=>[C(w,null,{split:j((()=>[C(te,{direction:"vertical"})])),default:j((()=>[C(oe,{disabled:S(W).some((e=>e.showSetSortIndex)),onClick:Y((()=>{e.showSetSortIndex=!0}),["stop"])},{default:j((()=>a[4]||(a[4]=[B(" 排序 ")]))),_:2},1032,["disabled","onClick"]),C(oe,{status:"danger",onClick:Y((a=>(e=>{try{L.warning({title:"提示",content:()=>R("div",{class:"text-center"},"确定删除该Banner？"),maskClosable:!1,escToClose:!1,hideCancel:!1,cancelButtonProps:{type:"outline"},onBeforeOk:async a=>{try{await re(e.id),t.success({title:"成功提示",content:"已删除Banner",duration:1500}),a(!0),ie()}catch(i){a(!1)}}})}catch(a){}})(e)),["stop"])},{default:j((()=>a[5]||(a[5]=[B("删除")]))),_:2},1032,["onClick"])])),_:2},1024)])),_:1})])),_:1},8,["loading","data"])])),_:1})])])}}}),X=Object.freeze(Object.defineProperty({__proto__:null,default:W},Symbol.toStringTag,{value:"Module"}));export{X as l,A as u};

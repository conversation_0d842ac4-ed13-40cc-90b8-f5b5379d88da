import{g as e,h as l,l as a,aU as t,c as n,_ as s,s as r,f as u}from"./index-D-8JbLQk.js";import{u as i}from"./index-BEo1tUsK.js";import{u as o}from"./index-DOhy6BH_.js";import{d,t as c,c as v,B as p,a3 as m,A as b,H as y,F as $,i as f,r as g,o as x,S as h,b as j,m as S,g as O}from"./vue-D-10XvVk.js";import{u as k}from"./use-index-D_ozg7PK.js";const w=Symbol("ArcoDescriptions");var I=Object.defineProperty,z=Object.getOwnPropertySymbols,A=Object.prototype.hasOwnProperty,D=Object.prototype.propertyIsEnumerable,E=(e,l,a)=>l in e?I(e,l,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[l]=a,N=(e,l)=>{for(var a in l||(l={}))A.call(l,a)&&E(e,a,l[a]);if(z)for(var a of z(l))D.call(l,a)&&E(e,a,l[a]);return e};const P=e=>e?e.reduce(((e,l)=>e+l.span),0):0;var B=d({name:"Descriptions",props:{data:{type:Array,default:()=>[]},column:{type:[Number,Object],default:3},title:String,layout:{type:String,default:"horizontal"},align:{type:[String,Object],default:"left"},size:{type:String},bordered:{type:Boolean,default:!1},labelStyle:{type:Object},valueStyle:{type:Object},tableLayout:{type:String,default:"auto"}},setup(s,{slots:r}){const{column:u,size:d}=c(s),f=e("descriptions"),{mergedSize:g}=o(d),x=i(u,3,!0),h=v((()=>{var e;return null!=(e=l(s.align)?s.align.label:s.align)?e:"left"})),j=v((()=>{var e;return null!=(e=l(s.align)?s.align.value:s.align)?e:"left"})),S=v((()=>N({textAlign:h.value},s.labelStyle))),O=v((()=>N({textAlign:j.value},s.valueStyle))),k=p(new Map),I=v((()=>Array.from(k.values()).sort(((e,l)=>e.index-l.index)).map((e=>e.span))));m(w,p({addItem:(e,l)=>{k.set(e,l)},removeItem:e=>{k.delete(e)}}));const z=e=>{const l=[];if(e.forEach(((e,a)=>{var t;const n=Math.min(null!=(t=y(e)?I.value[a]:e.span)?t:1,x.value),s=l[l.length-1],r=P(s);0===r||r>=x.value?l.push([{data:e,span:n}]):s.push({data:e,span:n+r>x.value?x.value-r:n})})),l.length){const e=l[l.length-1],a=P(e);a<x.value&&(e[e.length-1].span+=x.value-a)}return l},A=v((()=>{var e;return z(null!=(e=s.data)?e:[])})),D=(e,l)=>{var a,s,u,i,o;return y(e)?t(e,e.children)&&(null==(s=(a=e.children).label)?void 0:s.call(a))||(null==(u=e.props)?void 0:u.label):null!=(o=null==(i=r.label)?void 0:i.call(r,{label:e.label,index:l,data:e}))?o:n(e.label)?e.label():e.label},E=(e,l)=>{var a,t;return y(e)?e:null!=(t=null==(a=r.value)?void 0:a.call(r,{value:e.value,index:l,data:e}))?t:n(e.value)?e.value():e.value},B=(e,l)=>["inline-horizontal","inline-vertical"].includes(s.layout)?((e,l)=>b("tr",{class:`${f}-row`,key:`inline-${l}`},[e.map(((e,l)=>b("td",{key:`item-${l}`,class:`${f}-item`,colspan:e.span},[b("div",{class:[`${f}-item-label`,`${f}-item-label-inline`],style:S.value},[D(e.data,l)]),b("div",{class:[`${f}-item-value`,`${f}-item-value-inline`],style:O.value},[E(e.data,l)])])))]))(e,l):"vertical"===s.layout?(e=>b($,null,[b("tr",{class:`${f}-row`},[e.map(((e,l)=>b("td",{key:`label-${l}`,class:[`${f}-item-label`,`${f}-item-label-block`],style:S.value,colspan:e.span},[D(e.data,l)])))]),b("tr",{class:`${f}-row`},[e.map(((e,l)=>b("td",{key:`value-${l}`,class:[`${f}-item-value`,`${f}-item-value-block`],style:O.value,colspan:e.span},[E(e.data,l)])))])]))(e):((e,l)=>b("tr",{class:`${f}-row`,key:`tr-${l}`},[e.map((e=>b($,null,[b("td",{class:[`${f}-item-label`,`${f}-item-label-block`],style:S.value},[D(e.data,l)]),b("td",{class:[`${f}-item-value`,`${f}-item-value-block`],style:O.value,colspan:2*e.span-1},[E(e.data,l)])])))]))(e,l),C=v((()=>[f,`${f}-layout-${s.layout}`,`${f}-size-${g.value}`,{[`${f}-border`]:s.bordered},{[`${f}-table-layout-fixed`]:"fixed"===s.tableLayout}])),L=()=>{var e,l;const a=null!=(l=null==(e=r.title)?void 0:e.call(r))?l:s.title;return a?b("div",{class:`${f}-title`},[a]):null};return()=>{const e=r.default?z(a(r.default())):A.value;return b("div",{class:C.value},[L(),b("div",{class:`${f}-body`},[b("table",{class:`${f}-table`},[b("tbody",null,[e.map(((e,l)=>B(e,l)))])])])])}}});var C=s(d({name:"DescriptionsItem",props:{span:{type:Number,default:1},label:String},setup(l){var a;const{span:t}=c(l),n=e("descriptions"),s=f(w,{}),r=O(),u=g(),{computedIndex:i}=k({itemRef:u,selector:`.${n}-item-value`,parentClassName:`${n}-table`}),o=p({index:i.value,span:t.value});(null==r?void 0:r.uid)&&(null==(a=s.addItem)||a.call(s,r.uid,o));const d=()=>{var e;const l=(null==(e=null==r?void 0:r.proxy)?void 0:e.$el).parentElement;l&&l!==u.value&&(u.value=l)};return x((()=>d())),h((()=>d())),j((()=>{var e;(null==r?void 0:r.uid)&&(null==(e=s.removeItem)||e.call(s,r.uid))})),{prefixCls:n}}}),[["render",function(e,l,a,t,n,s){return S(e.$slots,"default")}]]);const L=Object.assign(B,{DescriptionsItem:C,install:(e,l)=>{r(e,l);const a=u(l);e.component(a+B.name,B),e.component(a+C.name,C)}});export{C as D,L as a};

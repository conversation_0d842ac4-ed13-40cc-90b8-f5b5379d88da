import{g as t,k as e,i as r,s as i,f as n}from"./index-D-8JbLQk.js";import{d as o,c as a,A as s}from"./vue-D-10XvVk.js";var l=o({name:"Divider",props:{direction:{type:String,default:"horizontal"},orientation:{type:String,default:"center"},type:{type:String},size:{type:Number},margin:{type:[Number,String]}},setup(i,{slots:n}){const o=t("divider"),l=a((()=>"horizontal"===i.direction)),d=a((()=>{const t={};if(i.size&&(t[l.value?"border-bottom-width":"border-left-width"]=e(i.size)?`${i.size}px`:i.size),i.type&&(t[l.value?"border-bottom-style":"border-left-style"]=i.type),!r(i.margin)){const r=e(i.margin)?`${i.margin}px`:i.margin;t.margin=l.value?`${r} 0`:`0 ${r}`}return t}));return()=>{var t;const e=null==(t=n.default)?void 0:t.call(n),r=[o,`${o}-${i.direction}`,{[`${o}-with-text`]:e}];return s("div",{role:"separator",class:r,style:d.value},[e&&"horizontal"===i.direction&&s("span",{class:[`${o}-text`,`${o}-text-${i.orientation}`]},[e])])}}});const d=Object.assign(l,{install:(t,e)=>{i(t,e);const r=n(e);t.component(r+l.name,l)}});export{d as D};

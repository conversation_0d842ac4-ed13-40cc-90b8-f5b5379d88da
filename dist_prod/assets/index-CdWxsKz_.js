import{g as e,j as t,a3 as a,B as l,s,f as i,l as n,_ as o}from"./index-D-8JbLQk.js";import{d as r,A as d,i as c,c as u,F as p,Q as v,t as m,B as y,a3 as f,o as $,j as g,k as h,m as b,l as z}from"./vue-D-10XvVk.js";import{u as S}from"./index-DOhy6BH_.js";var x=r({name:"DotLoading",props:{size:{type:Number}},setup(t){const a=e("dot-loading");return()=>{const e=t.size?{width:`${t.size}px`,height:`${t.size}px`}:{};return d("div",{class:a,style:{width:t.size?7*t.size+"px":void 0,height:t.size?`${t.size}px`:void 0}},[Array(5).fill(1).map(((t,l)=>d("div",{class:`${a}-item`,key:l,style:e},null)))])}}}),B=r({name:"Spin",props:{size:{type:Number},loading:Boolean,dot:Boolean,tip:String,hideIcon:{type:Boolean,default:!1}},setup(s,{slots:i}){const n=e("spin"),o=c(t,void 0),r=u((()=>[n,{[`${n}-loading`]:s.loading,[`${n}-with-tip`]:s.tip&&!i.default}])),m=()=>{if(i.icon){const e=a(i.icon());if(e)return v(e,{spin:!0})}return i.element?i.element():s.dot?d(x,{size:s.size},null):(null==o?void 0:o.slots.loading)?o.slots.loading():d(l,{spin:!0,size:s.size},null)},y=()=>{var e,t,a;const l=s.size?{fontSize:`${s.size}px`}:void 0,o=Boolean(null!=(e=i.tip)?e:s.tip);return d(p,null,[!s.hideIcon&&d("div",{class:`${n}-icon`,style:l},[m()]),o&&d("div",{class:`${n}-tip`},[null!=(a=null==(t=i.tip)?void 0:t.call(i))?a:s.tip])])};return()=>d("div",{class:r.value},[i.default?d(p,null,[i.default(),s.loading&&d("div",{class:`${n}-mask`},[d("div",{class:`${n}-mask-icon`},[y()])])]):y()])}});const j=Object.assign(B,{install:(e,t)=>{s(e,t);const a=i(t);e.component(a+B.name,B)}});var w="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function M(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}const O=Symbol("ArcoCard");var k=r({name:"Card",components:{Spin:j},props:{bordered:{type:Boolean,default:!0},loading:{type:Boolean,default:!1},hoverable:{type:Boolean,default:!1},size:{type:String},headerStyle:{type:Object,default:()=>({})},bodyStyle:{type:Object,default:()=>({})},title:{type:String},extra:{type:String}},setup(t,{slots:a}){const l=e("card"),{size:s}=m(t),{mergedSize:i}=S(s),o=u((()=>"small"===i.value||"mini"===i.value?"small":"medium")),r=e=>{const t=n(e);return d("div",{class:`${l}-actions`},[d("div",{class:`${l}-actions-right`},[t.map(((e,t)=>d("span",{key:`action-${t}`,class:`${l}-actions-item`},[e])))])])},c=y({hasMeta:!1,hasGrid:!1,slots:a,renderActions:r});f(O,c);const p=u((()=>[l,`${l}-size-${o.value}`,{[`${l}-loading`]:t.loading,[`${l}-bordered`]:t.bordered,[`${l}-hoverable`]:t.hoverable,[`${l}-contain-grid`]:c.hasGrid}]));return()=>{var e,s,i,n,o,u,v;const m=Boolean(null!=(e=a.title)?e:t.title),y=Boolean(null!=(s=a.extra)?s:t.extra);return d("div",{class:p.value},[(m||y)&&d("div",{class:[`${l}-header`,{[`${l}-header-no-title`]:!m}],style:t.headerStyle},[m&&d("div",{class:`${l}-header-title`},[null!=(n=null==(i=a.title)?void 0:i.call(a))?n:t.title]),y&&d("div",{class:`${l}-header-extra`},[null!=(u=null==(o=a.extra)?void 0:o.call(a))?u:t.extra])]),a.cover&&d("div",{class:`${l}-cover`},[a.cover()]),d("div",{class:`${l}-body`,style:t.bodyStyle},[t.loading?d(j,null,null):null==(v=a.default)?void 0:v.call(a),a.actions&&!c.hasMeta&&r(a.actions())])])}}}),A=r({name:"CardMeta",props:{title:{type:String},description:{type:String}},setup(t,{slots:a}){const l=e("card-meta"),s=c(O);return $((()=>{s&&(s.hasMeta=!0)})),()=>{var e,i,n,o,r,c;const u=Boolean(null!=(e=a.title)?e:t.title),p=Boolean(null!=(i=a.description)?i:t.description);return d("div",{class:l},[(u||p)&&d("div",{class:`${l}-content`},[u&&d("div",{class:`${l}-title`},[null!=(o=null==(n=a.title)?void 0:n.call(a))?o:t.title]),p&&d("div",{class:`${l}-description`},[null!=(c=null==(r=a.description)?void 0:r.call(a))?c:t.description])]),(a.avatar||(null==s?void 0:s.slots.actions))&&d("div",{class:[`${l}-footer `,{[`${l}-footer-only-actions`]:!a.avatar}]},[a.avatar&&d("div",{class:`${l}-avatar`},[a.avatar()]),s&&s.slots.actions&&s.renderActions(s.slots.actions())])])}}});var C=o(r({name:"CardGrid",props:{hoverable:{type:Boolean,default:!1}},setup(t){const a=e("card-grid"),l=c(O);$((()=>{l&&(l.hasGrid=!0)}));return{cls:u((()=>[a,{[`${a}-hoverable`]:t.hoverable}]))}}}),[["render",function(e,t,a,l,s,i){return g(),h("div",{class:z(e.cls)},[b(e.$slots,"default")],2)}]]);const G=Object.assign(k,{Meta:A,Grid:C,install:(e,t)=>{s(e,t);const a=i(t);e.component(a+k.name,k),e.component(a+A.name,A),e.component(a+C.name,C)}});export{G as C,j as S,w as c,M as g};

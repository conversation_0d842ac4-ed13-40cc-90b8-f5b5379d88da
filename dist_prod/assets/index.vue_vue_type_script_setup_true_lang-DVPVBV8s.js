import{V as e,d as t,c as s,j as o,k as n,m as a,l as r,O as i,A as l,v as p,H as c,y as u,u as d,a4 as m}from"./vue-D-10XvVk.js";import{_ as h,g,k as v,P as y,s as b,f as S}from"./index-D-8JbLQk.js";const f={appTheme:"light",showNavBar:!0,showBreadcrumb:!0,showSideMenu:!0,showTopMenu:!1,showTabBar:!0,hideMenu:!1,menuCollapse:!1,showFooter:!0,themeColor:"#165DFF",menuWidth:220,showGlobalSettingsDrawer:!1,showChangePasswordModal:!1,showNoticeDrawer:!1,device:"desktop",menuFromServer:!1,serverMenu:[]},z=e("appStore",{state:()=>({...f}),getters:{appCurrentSetting:e=>({...e}),appDevice:e=>e.device,appAsyncMenus:e=>e.serverMenu,appMenuCollapse:e=>e.menuCollapse},actions:{updateAppSettings(e){this.$patch({...e})},toggleAppTheme(e){"auto"===localStorage.getItem("app-theme")||null===localStorage.getItem("app-theme")||void 0===localStorage.getItem("app-theme")?(this.appTheme="light",document.body.removeAttribute("arco-theme")):(this.appTheme="dark",document.body.setAttribute("arco-theme","dark"))},toggleDevice(e){this.device=e}}});var w=h(t({name:"Icon",props:{type:String,size:[Number,String],rotate:Number,spin:Boolean},setup(e){const t=g("icon"),o=s((()=>{const t={};return e.size&&(t.fontSize=v(e.size)?`${e.size}px`:e.size),e.rotate&&(t.transform=`rotate(${e.rotate}deg)`),t}));return{cls:s((()=>[t,{[`${t}-loading`]:e.spin},e.type])),innerStyle:o}}}),[["render",function(e,t,s,l,p,c){return o(),n("svg",{class:r(e.cls),style:i(e.innerStyle),fill:"currentColor"},[a(e.$slots,"default")],6)}]]);const C=[],F=Object.assign(w,{addFromIconFontCn:e=>{const{src:s,extraProps:o={}}=e;if(!y&&(null==s?void 0:s.length)&&!C.includes(s)){const e=document.createElement("script");e.setAttribute("src",s),e.setAttribute("data-namespace",s),C.push(s),document.body.appendChild(e)}return t({name:"IconFont",props:{type:String,size:[Number,String],rotate:Number,spin:Boolean},setup:(e,{slots:t})=>()=>{var s;const n=e.type?l("use",{"xlink:href":`#${e.type}`},null):null==(s=t.default)?void 0:s.call(t);return l(w,p(e,o),"function"==typeof(a=n)||"[object Object]"===Object.prototype.toString.call(a)&&!c(a)?n:{default:()=>[n]});var a}})},install:(e,t)=>{b(e,t);const s=S(t);e.component(s+w.name,w)}}),j=t({__name:"index",props:{icon:{},size:{default:18}},setup(e){const t=F.addFromIconFontCn({src:"//at.alicdn.com/t/c/font_4679491_2367no0vgx7.js"});return(e,s)=>(o(),n("div",null,[e.icon.includes("icon-")?(o(),u(d(t),{key:0,type:e.icon,size:e.size},null,8,["type","size"])):(o(),u(m(`icon-${e.icon}`),{key:1,size:e.size},null,8,["size"]))]))}});export{j as _,z as u};

import"./index-DOhy6BH_.js";import"./index-BEo1tUsK.js";import{F as e,a as o}from"./index-DVDXfQhn.js";import"./index-DDFSMqsG.js";import{S as s}from"./index-Cuq5XRs0.js";import"./index-DD6vSYIM.js";import"./index-DQjhgQFu.js";import{u as r}from"./useCommon-BuUbRw8e.js";import{u as i}from"./index-BkMCHglW.js";import{d as t,o as m,K as n,j as d,k as p,A as a,z as l,u as j}from"./vue-D-10XvVk.js";import"./index-D-8JbLQk.js";import"./pick-Ccd8Sfcm.js";import"./index-DGtjsHgS.js";import"./ResizeObserver.es-CzGuHLZU.js";import"./resize-observer-Dtogi-DJ.js";import"./index-CdWxsKz_.js";import"./apiCommon-DcubqwY_.js";import"./index-DdMaxvYa.js";import"./index-DfEXMvnc.js";import"./use-children-components-v8i8lsOx.js";import"./index-DmW4RN1x.js";/* empty css              */import"./index-CUtvFEc_.js";import"./use-index-D_ozg7PK.js";import"./index-CHOaln3D.js";/* empty css              */import"./useLoading-D5mh7tTu.js";import"./usePagination-Dd_EW2BO.js";import"./apiStore-vHDpafS2.js";import"./dayjs.min-Daes5FZc.js";import"./index-O7pr3qsq.js";const u={class:"overflow-y-scroll no-scrollbar"},f=t({__name:"info",setup(t,{expose:f}){const{storeOptions:x,initStoreOptions:c}=r(),{form:h}=i();m((()=>{c()}));const b=n("formRef");return f({formRef:b,form:h}),(r,i)=>{const t=s,m=e,n=o;return d(),p("div",u,[a(n,{ref_key:"formRef",ref:b,model:j(h),"auto-label-width":""},{default:l((()=>[a(m,{"show-colon":"",label:"店铺名称",field:"storeId",rules:[{required:!0,message:`${r.$selectPlaceholder}店铺名称`}]},{default:l((()=>[a(t,{modelValue:j(h).storeId,"onUpdate:modelValue":i[0]||(i[0]=e=>j(h).storeId=e),options:j(x),placeholder:`${r.$selectPlaceholder}店铺名称`,"allow-search":""},null,8,["modelValue","options","placeholder"])])),_:1},8,["rules"])])),_:1},8,["model"])])}}});export{f as default};

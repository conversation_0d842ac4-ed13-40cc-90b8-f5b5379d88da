const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/detail-D4dRs1y_.js","assets/index-HLwNT5T9.js","assets/index-DOhy6BH_.js","assets/index-D-8JbLQk.js","assets/vue-D-10XvVk.js","assets/index-DxPaQOvH.css","assets/index-DB09tZwb.css","assets/index-DGtjsHgS.js","assets/index-BJBnsrKF.css","assets/index-DD6vSYIM.js","assets/index-DDFSMqsG.js","assets/pick-Ccd8Sfcm.js","assets/ResizeObserver.es-CzGuHLZU.js","assets/index-RZyF5P1Y.css","assets/index-C0ni2jp2.css","assets/index-CuYx5vtf.js","assets/render-function-CAXdZVZM.js","assets/index-BZi9bKOJ.css","assets/index-CdWxsKz_.js","assets/index-CX9L_GU1.css","assets/apiUpload-DpATemHF.js","assets/_plugin-vue_export-helper-BCo6x5W8.js","assets/index-IkV6U84s.css","assets/index-CUtvFEc_.js","assets/use-index-D_ozg7PK.js","assets/index-CHOaln3D.js","assets/index-komh9C6_.css","assets/index-DIKBiUsz.js","assets/index-BEo1tUsK.js","assets/index-6rnfXikd.css","assets/index-Cf1pvoHl.css","assets/index-Cuq5XRs0.js","assets/resize-observer-Dtogi-DJ.js","assets/index-DQjhgQFu.js","assets/index-Db7LPRu1.css","assets/index-Dbgee0nK.css","assets/useCommon-BuUbRw8e.js","assets/apiCommon-DcubqwY_.js","assets/useAddress-CutR4aE-.js","assets/dayjs.min-Daes5FZc.js","assets/index-DdMaxvYa.js","assets/index-DfEXMvnc.js","assets/use-children-components-v8i8lsOx.js","assets/index-B5FzkxT_.css","assets/index-DmW4RN1x.js","assets/index-Bl_vBcmJ.css","assets/index-DVDXfQhn.js","assets/index-8er2yjEK.css","assets/index-dpn1_5z1.js","assets/index-Cf9H8fsj.css","assets/useLoading-D5mh7tTu.js","assets/usePagination-Dd_EW2BO.js","assets/index-O7pr3qsq.js","assets/index-CJ6Fn8S6.css"])))=>i.map(i=>d[i]);
import{O as e,Q as a,R as t,S as l,T as o,N as i,m as s}from"./index-D-8JbLQk.js";import"./index-DOhy6BH_.js";import"./index-DDFSMqsG.js";import{S as r,T as n,L as d}from"./index-Cuq5XRs0.js";import"./index-DD6vSYIM.js";import"./index-DQjhgQFu.js";import{T as c,a as u,P as p}from"./index-DdMaxvYa.js";import{B as m,S as g}from"./index-DGtjsHgS.js";import{C as f}from"./index-CdWxsKz_.js";import{D as h}from"./index-DmW4RN1x.js";import{C as v,R as w}from"./index-BEo1tUsK.js";import{F as _,a as y}from"./index-DVDXfQhn.js";import{R as x}from"./index-dpn1_5z1.js";/* empty css              */import{B as b,r as j,c as C,d as z,o as S,k as T,A as k,z as P,u as I,e as O,q as F,f as V,j as $,I as B,y as R,p as q,J as A,af as M,M as D,h as L}from"./vue-D-10XvVk.js";import{u as Y}from"./useCommon-BuUbRw8e.js";import{u as N}from"./useLoading-D5mh7tTu.js";import{u as U}from"./usePagination-Dd_EW2BO.js";import{d as E}from"./dayjs.min-Daes5FZc.js";import{M as H}from"./index-O7pr3qsq.js";const J=()=>{const{loading:s,setLoading:r}=N(),{pagination:n}=U(),d=b({storeId:void 0,createTime:void 0}),c=j(void 0),u=C((()=>c.value?g.value.find((e=>e.id===c.value)):null)),p=j([]),m=C((()=>p.value.length?g.value.filter((e=>p.value.includes(e.id))):[])),g=j([]),f=async()=>{r(!0),c.value=void 0,p.value=[];try{const{data:a}=await(a=>e({url:"/admin/platform/storeFundFlow/list",method:"post",data:a}))({...d,pageNum:n.current,pageSize:n.pageSize});g.value=a.rows.map((e=>(e.createTime=E(e.createTime).format("YYYY-MM-DD HH:mm:ss"),e))),n.total=a.total,r(!1)}catch(a){g.value=[],n.total=0,r(!1)}},h=b({});return{loading:s,queryParams:d,pagination:n,rows:g,selectedId:c,selectedRow:u,selectedIds:p,selectedRows:m,selectAll:e=>{p.value=e?g.value.map((e=>e.id)):[]},rowSelect:(e,a,t)=>{p.value.includes(t.id)?p.value.splice(p.value.indexOf(t.id),1):p.value.push(t.id)},rowClick:e=>{p.value.includes(e.id)?p.value.splice(p.value.indexOf(e.id),1):p.value.push(e.id)},query:f,reset:()=>{n.current=1,Object.assign(d,{storeId:void 0,createTime:void 0}),f()},pageChange:async e=>{n.current=e,f()},pageSizeChange:async e=>{n.current=1,n.pageSize=e,f()},detail:async a=>{try{const{data:t}=await(a=>e({url:`/admin/platform/storeFundFlow/detail/${a}`,method:"get"}))(a);Object.assign(h,t)}catch(t){throw t}},form:h,exports:async()=>{try{await new Promise(((e,s)=>{a({url:t+"/admin/platform/storeFundFlow/exports",method:"post",responseType:"blob",headers:{Authorization:"Bearer "+l()}}).then((a=>{"application/vnd.openxmlformats"===a.data.type?(o(a,"application/vnd.openxmlformats;charset=utf-8","店铺流水信息.xlsx"),e(!0)):(i.warning({title:"下载出错",content:"请稍后重试",duration:3e3}),s())})).catch((()=>{s()}))}))}catch(e){throw e}}}},Q={class:"page-container"},G={class:"h-full flex flex-col gap-[18px]"},K=z({__name:"index",setup(e){const{storeOptions:a,initStoreOptions:t}=Y(),{loading:l,queryParams:o,pagination:b,rows:C,selectedId:z,selectedIds:N,selectAll:U,rowSelect:E,rowClick:K,query:W,reset:X,pageChange:Z,pageSizeChange:ee,exports:ae}=J(),te=D((()=>s((()=>import("./detail-D4dRs1y_.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53])))),le=j(!1),oe=()=>{try{H.warning({title:"提示",content:()=>L("div",{class:"text-center"},"确定导出所有流水信息？"),maskClosable:!1,escToClose:!1,hideCancel:!1,cancelButtonProps:{type:"outline"},onBeforeOk:async e=>{try{await ae(),i.success({title:"操作提示",content:"已导出所有流水信息",duration:1500}),e(!0)}catch(a){e(!1)}}})}catch(e){}};return S((()=>{t(),Z(1)})),(e,t)=>{const i=H,s=r,j=_,S=v,D=x,L=V("icon-search"),Y=m,N=V("icon-refresh"),U=g,E=w,J=y,K=f,W=V("icon-export"),ae=c,ie=n,se=h,re=d,ne=u,de=p;return $(),T("div",Q,[k(i,{visible:I(le),"onUpdate:visible":t[0]||(t[0]=e=>O(le)?le.value=e:null),width:1e3,"title-align":"start",title:"流水详情","cancel-button-props":{type:"outline"},"unmount-on-close":"","mask-closable":!1,"esc-to-close":!1,onCancel:t[1]||(t[1]=e=>le.value=!1),footer:!1,"body-style":"background-color: var(--color-fill-2)"},{default:P((()=>[k(I(te),{id:I(z)},null,8,["id"])])),_:1},8,["visible"]),F("div",G,[k(K,{bordered:!1},{default:P((()=>[k(J,{model:I(o),"auto-label-width":""},{default:P((()=>[k(E,{gutter:16},{default:P((()=>[k(S,{span:6},{default:P((()=>[k(j,{"show-colon":"",label:"店铺名称",field:"storeId"},{default:P((()=>[k(s,{modelValue:I(o).storeId,"onUpdate:modelValue":t[2]||(t[2]=e=>I(o).storeId=e),options:I(a),placeholder:`${e.$selectPlaceholder}店铺名称`,"allow-search":"","allow-clear":""},null,8,["modelValue","options","placeholder"])])),_:1})])),_:1}),k(S,{span:6},{default:P((()=>[k(j,{"show-colon":"",label:"日期",field:"createTime"},{default:P((()=>[k(D,{modelValue:I(o).createTime,"onUpdate:modelValue":t[3]||(t[3]=e=>I(o).createTime=e),placeholder:[`${e.$selectPlaceholder}开始日期`,`${e.$selectPlaceholder}结束日期`]},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),k(S,{span:6},{default:P((()=>[k(j,{"hide-label":""},{default:P((()=>[k(U,{size:18},{default:P((()=>[k(Y,{type:"primary",onClick:t[4]||(t[4]=e=>I(Z)(1))},{icon:P((()=>[k(L)])),default:P((()=>[t[5]||(t[5]=B(" 查询 "))])),_:1}),k(Y,{type:"outline",onClick:I(X)},{icon:P((()=>[k(N)])),default:P((()=>[t[6]||(t[6]=B(" 重置 "))])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),k(K,{bordered:!1,class:"flex-1 overflow-y-hidden","body-style":{height:"100%"}},{actions:P((()=>[I(b).total?($(),R(de,{key:0,current:I(b).current,"page-size":I(b).pageSize,"show-total":I(b).showTotal,"show-page-size":I(b).showPageSize,"page-size-options":I(b).pageSizeOptions,total:I(b).total,onChange:I(Z),onPageSizeChange:I(ee)},null,8,["current","page-size","show-total","show-page-size","page-size-options","total","onChange","onPageSizeChange"])):q("",!0)])),default:P((()=>[k(E,{class:"mb-[12px]"},{default:P((()=>[k(S,{span:16},{default:P((()=>[k(U,null,{default:P((()=>[k(Y,{disabled:!I(C).length,type:"primary",onClick:oe},{icon:P((()=>[k(W)])),default:P((()=>[t[7]||(t[7]=B(" 导出 "))])),_:1},8,["disabled"])])),_:1})])),_:1})])),_:1}),k(ne,{size:"large","row-key":"id",loading:I(l),pagination:!1,data:I(C),bordered:{cell:!0},scroll:{y:"calc(100% - 96px)"}},{columns:P((()=>[k(ae,{align:"center",title:"序号",width:80},{cell:P((({rowIndex:e})=>[B(A(e+1),1)])),_:1}),k(ae,{align:"center",title:"类型",width:150},{cell:P((({record:e})=>[k(ie,{color:1===e.type?"green":"orange"},{default:P((()=>[B(A(1===e.type?"订单收入":"向平台分账"),1)])),_:2},1032,["color"])])),_:1}),k(ae,{align:"center",title:"店铺",width:200,ellipsis:"",tooltip:""},{cell:P((({record:e})=>{var a;return[B(A((null==(a=e.store)?void 0:a.name)??"-"),1)]})),_:1}),k(ae,{align:"center",title:"关联订单",width:200},{cell:P((({record:e})=>{var a;return[B(A((null==(a=e.order)?void 0:a.orderNo)??"-"),1)]})),_:1}),k(ae,{align:"center",title:"金额",width:150},{cell:P((({record:e})=>[B(A(e.amount)+" 元",1)])),_:1}),k(ae,{align:"center",title:"状态",width:150},{cell:P((({record:e})=>[B(A(1===e.state?"处理完成":"处理中"),1)])),_:1}),k(ae,{align:"center",title:"余额",width:150},{cell:P((({record:e})=>[B(A(e.balance)+" 元",1)])),_:1}),k(ae,{align:"center",title:"可用余额",width:150},{cell:P((({record:e})=>[B(A(e.availableBalance)+" 元",1)])),_:1}),k(ae,{align:"center",title:"创建时间",width:180,"data-index":"createTime"}),k(ae,{align:"center",title:"操作",width:100,fixed:"right"},{cell:P((({record:e})=>[k(U,null,{split:P((()=>[k(se,{direction:"vertical"})])),default:P((()=>[k(re,{onClick:M((()=>{z.value=e.id,le.value=!0}),["stop"])},{default:P((()=>t[8]||(t[8]=[B(" 详情 ")]))),_:2},1032,["onClick"])])),_:2},1024)])),_:1})])),_:1},8,["loading","data"])])),_:1})])])}}}),W=Object.freeze(Object.defineProperty({__proto__:null,default:K},Symbol.toStringTag,{value:"Module"}));export{W as i,J as u};

import{j as e,a5 as t,_ as l,B as o,K as a,aj as n,ak as r,g as u,i,C as s,h as p,c,ao as d,I as f,a as v,ap as h,aq as m,ar as b,s as y,f as g,ae as w,as as C,at as x,al as O,W as S,au as B,v as T,av as k,w as R}from"./index-D-8JbLQk.js";import{r as P,B as M,i as E,c as j,d as $,f as L,j as A,k as F,y as z,p as I,l as V,t as N,w as W,A as D,v as _,I as q,n as H,F as Y,D as K,z as J,m as G,af as U,o as X,S as Z,b as Q,a3 as ee,ao as te,aj as le,a6 as oe,Z as ae,a7 as ne,J as re}from"./vue-D-10XvVk.js";import{p as ue}from"./pick-Ccd8Sfcm.js";import{u as ie,B as se}from"./index-DGtjsHgS.js";import{u as pe}from"./index-DOhy6BH_.js";import{i as ce}from"./ResizeObserver.es-CzGuHLZU.js";const de={formatYear:"YYYY 年",formatMonth:"YYYY 年 MM 月",today:"今天",view:{month:"月",year:"年",week:"周",day:"日"},month:{long:{January:"一月",February:"二月",March:"三月",April:"四月",May:"五月",June:"六月",July:"七月",August:"八月",September:"九月",October:"十月",November:"十一月",December:"十二月"},short:{January:"一月",February:"二月",March:"三月",April:"四月",May:"五月",June:"六月",July:"七月",August:"八月",September:"九月",October:"十月",November:"十一月",December:"十二月"}},week:{long:{self:"周",monday:"周一",tuesday:"周二",wednesday:"周三",thursday:"周四",friday:"周五",saturday:"周六",sunday:"周日"},short:{self:"周",monday:"一",tuesday:"二",wednesday:"三",thursday:"四",friday:"五",saturday:"六",sunday:"日"}}},fe={locale:"zh-CN",empty:{description:"暂无数据"},drawer:{okText:"确定",cancelText:"取消"},popconfirm:{okText:"确定",cancelText:"取消"},modal:{okText:"确定",cancelText:"取消"},pagination:{goto:"前往",page:"页",countPerPage:"条/页",total:"共 {0} 条"},table:{okText:"确定",resetText:"重置"},upload:{start:"开始",cancel:"取消",delete:"删除",retry:"点击重试",buttonText:"点击上传",preview:"预览",drag:"点击或拖拽文件到此处上传",dragHover:"释放文件并开始上传",error:"上传失败"},calendar:de,datePicker:{view:de.view,month:de.month,week:de.week,placeholder:{date:"请选择日期",week:"请选择周",month:"请选择月份",year:"请选择年份",quarter:"请选择季度",time:"请选择时间"},rangePlaceholder:{date:["开始日期","结束日期"],week:["开始周","结束周"],month:["开始月份","结束月份"],year:["开始年份","结束年份"],quarter:["开始季度","结束季度"],time:["开始时间","结束时间"]},selectTime:"选择时间",today:"今天",now:"此刻",ok:"确定"},image:{loading:"加载中"},imagePreview:{fullScreen:"全屏",rotateRight:"向右旋转",rotateLeft:"向左旋转",zoomIn:"放大",zoomOut:"缩小",originalSize:"原始尺寸"},typography:{copied:"已复制",copy:"复制",expand:"展开",collapse:"折叠",edit:"编辑"},form:{validateMessages:{required:"#{field} 是必填项",type:{string:"#{field} 不是合法的文本类型",number:"#{field} 不是合法的数字类型",boolean:"#{field} 不是合法的布尔类型",array:"#{field} 不是合法的数组类型",object:"#{field} 不是合法的对象类型",url:"#{field} 不是合法的 url 地址",email:"#{field} 不是合法的邮箱地址",ip:"#{field} 不是合法的 IP 地址"},number:{min:"`#{value}` 小于最小值 `#{min}`",max:"`#{value}` 大于最大值 `#{max}`",equal:"`#{value}` 不等于 `#{equal}`",range:"`#{value}` 不在 `#{min} ~ #{max}` 范围内",positive:"`#{value}` 不是正数",negative:"`#{value}` 不是负数"},array:{length:"`#{field}` 个数不等于 #{length}",minLength:"`#{field}` 个数最少为 #{minLength}",maxLength:"`#{field}` 个数最多为 #{maxLength}",includes:"#{field} 不包含 #{includes}",deepEqual:"#{field} 不等于 #{deepEqual}",empty:"`#{field}` 不是空数组"},string:{minLength:"字符数最少为 #{minLength}",maxLength:"字符数最多为 #{maxLength}",length:"字符数必须是 #{length}",match:"`#{value}` 不符合模式 #{pattern}",uppercase:"`#{value}` 必须全大写",lowercase:"`#{value}` 必须全小写"},object:{deepEqual:"`#{field}` 不等于期望值",hasKeys:"`#{field}` 不包含必须字段",empty:"`#{field}` 不是对象"},boolean:{true:"期望是 `true`",false:"期望是 `false`"}}},colorPicker:{history:"最近使用颜色",preset:"系统预设颜色",empty:"暂无"}},ve=P("zh-CN"),he=M({"zh-CN":fe}),me=()=>{const l=E(e,void 0),o=j((()=>{var e;return null!=(e=null==l?void 0:l.locale)?e:he[ve.value]})),a=j((()=>o.value.locale));return{i18nMessage:o,locale:a,t:(e,...l)=>{const a=e.split(".");let n=o.value;for(const t of a){if(!n[t])return e;n=n[t]}return t(n)&&l.length>0?n.replace(/{(\d+)}/g,((e,t)=>{var o;return null!=(o=l[t])?o:e})):n}}},be="undefined"==typeof window?global:window,ye=be.requestAnimationFrame,ge=be.cancelAnimationFrame;function we(e){let t=0;const l=(...l)=>{t&&ge(t),t=ye((()=>{e(...l),t=0}))};return l.cancel=()=>{ge(t),t=0},l}var Ce=l($({name:"FeedbackIcon",components:{IconLoading:o,IconCheckCircleFill:a,IconExclamationCircleFill:n,IconCloseCircleFill:r},props:{type:{type:String}},setup(e){const t=u("feedback-icon");return{cls:j((()=>[t,`${t}-status-${e.type}`]))}}}),[["render",function(e,t,l,o,a,n){const r=L("icon-loading"),u=L("icon-check-circle-fill"),i=L("icon-exclamation-circle-fill"),s=L("icon-close-circle-fill");return A(),F("span",{class:V(e.cls)},["validating"===e.type?(A(),z(r,{key:0})):"success"===e.type?(A(),z(u,{key:1})):"warning"===e.type?(A(),z(i,{key:2})):"error"===e.type?(A(),z(s,{key:3})):I("v-if",!0)],2)}]]);const xe={key:"Enter",code:"Enter"},Oe={key:"Backspace",code:"Backspace"};var Se=Object.defineProperty,Be=Object.getOwnPropertySymbols,Te=Object.prototype.hasOwnProperty,ke=Object.prototype.propertyIsEnumerable,Re=(e,t,l)=>t in e?Se(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l;const Pe=(e,t)=>{const l=((e,t)=>{for(var l in t||(t={}))Te.call(t,l)&&Re(e,l,t[l]);if(Be)for(var l of Be(t))ke.call(t,l)&&Re(e,l,t[l]);return e})({},e);for(const o of t)o in l&&delete l[o];return l};function Me(e){const t=P();return[function(){if(!e.value)return;const{selectionStart:l,selectionEnd:o,value:a}=e.value;if(null==l||null==o)return;const n=a.slice(0,Math.max(0,l)),r=a.slice(Math.max(0,o));t.value={selectionStart:l,selectionEnd:o,value:a,beforeTxt:n,afterTxt:r}},function(){if(!e.value||!t.value)return;const{value:l}=e.value,{beforeTxt:o,afterTxt:a,selectionStart:n}=t.value;if(!o||!a||!n)return;let r=l.length;if(l.endsWith(a))r=l.length-a.length;else if(l.startsWith(o))r=o.length;else{const e=o[n-1],t=l.indexOf(e,n-1);-1!==t&&(r=t+1)}e.value.setSelectionRange(r,r)}]}var Ee=Object.defineProperty,je=Object.getOwnPropertySymbols,$e=Object.prototype.hasOwnProperty,Le=Object.prototype.propertyIsEnumerable,Ae=(e,t,l)=>t in e?Ee(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,Fe=(e,t)=>{for(var l in t||(t={}))$e.call(t,l)&&Ae(e,l,t[l]);if(je)for(var l of je(t))Le.call(t,l)&&Ae(e,l,t[l]);return e},ze=$({name:"Input",inheritAttrs:!1,props:{modelValue:String,defaultValue:{type:String,default:""},size:{type:String},allowClear:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},error:{type:Boolean,default:!1},placeholder:String,maxLength:{type:[Number,Object],default:0},showWordLimit:{type:Boolean,default:!1},wordLength:{type:Function},wordSlice:{type:Function},inputAttrs:{type:Object},type:{type:String,default:"text"}},emits:{"update:modelValue":e=>!0,input:(e,t)=>!0,change:(e,t)=>!0,pressEnter:e=>!0,clear:e=>!0,focus:e=>!0,blur:e=>!0},setup(e,{emit:t,slots:l,attrs:o}){const{size:a,disabled:n,error:r,modelValue:h}=N(e),m=u("input"),b=P(),{mergedSize:y,mergedDisabled:g,mergedError:w,feedback:C,eventHandlers:x}=ie({size:a,disabled:n,error:r}),{mergedSize:O}=pe(y),[S,B]=Me(b),T=P(e.defaultValue),k=j((()=>{var t;return null!=(t=e.modelValue)?t:T.value}));W(h,(e=>{(i(e)||s(e))&&(T.value="")}));let R=k.value;const M=P(!1),E=j((()=>e.allowClear&&!e.readonly&&!g.value&&Boolean(k.value))),$=P(!1),L=P(""),A=t=>{var l;return c(e.wordLength)?e.wordLength(t):null!=(l=t.length)?l:0},F=j((()=>A(k.value))),z=j((()=>w.value||Boolean(p(e.maxLength)&&e.maxLength.errorOnly&&F.value>V.value))),I=j((()=>p(e.maxLength)&&Boolean(e.maxLength.errorOnly))),V=j((()=>p(e.maxLength)?e.maxLength.length:e.maxLength)),Y=j((()=>{const e=A("a");return Math.floor(V.value/e)})),K=l=>{var o,a;V.value&&!I.value&&A(l)>V.value&&(l=null!=(a=null==(o=e.wordSlice)?void 0:o.call(e,l,V.value))?a:l.slice(0,Y.value)),T.value=l,t("update:modelValue",l)},J=e=>{b.value&&e.target!==b.value&&(e.preventDefault(),b.value.focus())},G=(e,l)=>{var o,a;e!==R&&(R=e,t("change",e,l),null==(a=null==(o=x.value)?void 0:o.onChange)||a.call(o,l))},U=e=>{var l,o;M.value=!0,R=k.value,t("focus",e),null==(o=null==(l=x.value)?void 0:l.onFocus)||o.call(l,e)},X=e=>{var l,o;M.value=!1,G(k.value,e),t("blur",e),null==(o=null==(l=x.value)?void 0:l.onBlur)||o.call(l,e)},Z=e=>{var l,o,a;const{value:n,selectionStart:r,selectionEnd:u}=e.target;if("compositionend"===e.type){if($.value=!1,L.value="",V.value&&!I.value&&F.value>=V.value&&A(n)>V.value&&r===u)return void Q();K(n),t("input",n,e),null==(o=null==(l=x.value)?void 0:l.onInput)||o.call(l,e),Q()}else $.value=!0,L.value=k.value+(null!=(a=e.data)?a:"")},Q=()=>{S(),H((()=>{b.value&&k.value!==b.value.value&&(b.value.value=k.value,B())}))},ee=e=>{var l,o;const{value:a}=e.target;if(!$.value){if(V.value&&!I.value&&F.value>=V.value&&A(a)>V.value&&"insertText"===e.inputType)return void Q();K(a),t("input",a,e),null==(o=null==(l=x.value)?void 0:l.onInput)||o.call(l,e),Q()}},te=e=>{K(""),G("",e),t("clear",e)},le=e=>{const l=e.key||e.code;$.value||l!==xe.key||(G(k.value,e),t("pressEnter",e))},oe=j((()=>[`${m}-outer`,`${m}-outer-size-${O.value}`,{[`${m}-outer-has-suffix`]:Boolean(l.suffix),[`${m}-outer-disabled`]:g.value}])),ae=j((()=>[`${m}-wrapper`,{[`${m}-error`]:z.value,[`${m}-disabled`]:g.value,[`${m}-focus`]:M.value}])),ne=j((()=>[m,`${m}-size-${O.value}`])),re=j((()=>Pe(o,d))),se=j((()=>ue(o,d))),ce=j((()=>{const t=Fe(Fe({},se.value),e.inputAttrs);return z.value&&(t["aria-invalid"]=!0),t})),de=t=>{var o;return D("span",_({class:ae.value,onMousedown:J},t?void 0:re.value),[l.prefix&&D("span",{class:`${m}-prefix`},[l.prefix()]),D("input",_({ref:b,class:ne.value,value:k.value,type:e.type,placeholder:e.placeholder,readonly:e.readonly,disabled:g.value,onInput:ee,onKeydown:le,onFocus:U,onBlur:X,onCompositionstart:Z,onCompositionupdate:Z,onCompositionend:Z},ce.value),null),E.value&&D(f,{prefix:m,class:`${m}-clear-btn`,onClick:te},{default:()=>[D(v,null,null)]}),(l.suffix||Boolean(e.maxLength)&&e.showWordLimit||Boolean(C.value))&&D("span",{class:[`${m}-suffix`,{[`${m}-suffix-has-feedback`]:C.value}]},[Boolean(e.maxLength)&&e.showWordLimit&&D("span",{class:`${m}-word-limit`},[F.value,q("/"),V.value]),null==(o=l.suffix)?void 0:o.call(l),Boolean(C.value)&&D(Ce,{type:C.value},null)])])};return{inputRef:b,render:()=>l.prepend||l.append?D("span",_({class:oe.value},re.value),[l.prepend&&D("span",{class:`${m}-prepend`},[l.prepend()]),de(!0),l.append&&D("span",{class:`${m}-append`},[l.append()])]):de()}},methods:{focus(){var e;null==(e=this.inputRef)||e.focus()},blur(){var e;null==(e=this.inputRef)||e.blur()}},render(){return this.render()}}),Ie=$({name:"InputSearch",props:{searchButton:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},size:{type:String},buttonText:{type:String},buttonProps:{type:Object}},emits:{search:(e,t)=>!0},setup(e,{emit:t,slots:l}){const{size:a}=N(e),n=u("input-search"),{mergedSize:r}=pe(a),i=P(),s=e=>{i.value.inputRef&&t("search",i.value.inputRef.value,e)},p=()=>{var t;return D(Y,null,[e.loading?D(o,null,null):D(f,{onClick:s},{default:()=>[D(h,null,null)]}),null==(t=l.suffix)?void 0:t.call(l)])},c=()=>{var t;let o={};return o=e.buttonText||l["button-default"]||l["button-icon"]?{default:null!=(t=l["button-default"])?t:e.buttonText?()=>e.buttonText:void 0,icon:l["button-icon"]}:{icon:()=>D(h,null,null)},D(se,_({type:"primary",class:`${n}-btn`,disabled:e.disabled,size:r.value,loading:e.loading},e.buttonProps,{onClick:s}),o)};return{inputRef:i,render:()=>D(ze,{ref:i,class:n,size:r.value,disabled:e.disabled},{prepend:l.prepend,prefix:l.prefix,suffix:e.searchButton?l.suffix:p,append:e.searchButton?c:l.append})}},methods:{focus(){var e;null==(e=this.inputRef)||e.focus()},blur(){var e;null==(e=this.inputRef)||e.blur()}},render(){return this.render()}});function Ve(e){const t=P(e);return[t,e=>{t.value=e}]}function Ne(e,t){const{value:l}=N(t),[o,a]=Ve(i(l.value)?e:l.value);W(l,(e=>{i(e)&&a(void 0)}));return[j((()=>i(l.value)?o.value:l.value)),a,o]}var We=l($({name:"InputPassword",components:{IconEye:m,IconEyeInvisible:b,AIconHover:f,AInput:ze},props:{visibility:{type:Boolean,default:void 0},defaultVisibility:{type:Boolean,default:!0},invisibleButton:{type:Boolean,default:!0}},emits:["visibility-change","update:visibility"],setup(e,{emit:t}){const{visibility:l,defaultVisibility:o}=N(e),a=P(),[n,r]=Ne(o.value,M({value:l})),u=e=>{e!==n.value&&(t("visibility-change",e),t("update:visibility",e),r(e))};return{inputRef:a,mergedVisible:n,handleInvisible:()=>{u(!n.value)}}},methods:{focus(){var e;null==(e=this.inputRef)||e.focus()},blur(){var e;null==(e=this.inputRef)||e.blur()}}}),[["render",function(e,t,l,o,a,n){const r=L("icon-eye"),u=L("icon-eye-invisible"),i=L("a-icon-hover"),s=L("a-input");return A(),z(s,{ref:"inputRef",type:e.mergedVisible?"password":"text"},K({_:2},[e.$slots.prepend?{name:"prepend",fn:J((()=>[G(e.$slots,"prepend")]))}:void 0,e.$slots.prefix?{name:"prefix",fn:J((()=>[G(e.$slots,"prefix")]))}:void 0,e.invisibleButton||e.$slots.suffix?{name:"suffix",fn:J((()=>[e.invisibleButton?(A(),z(i,{key:0,onClick:e.handleInvisible,onMousedown:t[0]||(t[0]=U((()=>{}),["prevent"])),onMouseup:t[1]||(t[1]=U((()=>{}),["prevent"]))},{default:J((()=>[e.mergedVisible?(A(),z(u,{key:1})):(A(),z(r,{key:0}))])),_:1},8,["onClick"])):I("v-if",!0),G(e.$slots,"suffix")]))}:void 0,e.$slots.append?{name:"append",fn:J((()=>[G(e.$slots,"append")]))}:void 0]),1032,["type"])}]]);var De=l($({name:"InputGroup",setup:()=>({prefixCls:u("input-group")})}),[["render",function(e,t,l,o,a,n){return A(),F("div",{class:V(e.prefixCls)},[G(e.$slots,"default")],2)}]]);const _e=Object.assign(ze,{Search:Ie,Password:We,Group:De,install:(e,t)=>{y(e,t);const l=g(t);e.component(l+ze.name,ze),e.component(l+De.name,De),e.component(l+Ie.name,Ie),e.component(l+We.name,We)}});var qe=Object.defineProperty,He=Object.getOwnPropertySymbols,Ye=Object.prototype.hasOwnProperty,Ke=Object.prototype.propertyIsEnumerable,Je=(e,t,l)=>t in e?qe(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,Ge=(e,t)=>{for(var l in t||(t={}))Ye.call(t,l)&&Je(e,l,t[l]);if(He)for(var l of He(t))Ke.call(t,l)&&Je(e,l,t[l]);return e};const Ue=(e,t)=>{var l,o;const a=e.getBoundingClientRect();return{top:a.top,bottom:a.bottom,left:a.left,right:a.right,scrollTop:a.top-t.top,scrollBottom:a.bottom-t.top,scrollLeft:a.left-t.left,scrollRight:a.right-t.left,width:null!=(l=e.offsetWidth)?l:e.clientWidth,height:null!=(o=e.offsetHeight)?o:e.clientHeight}},Xe=(e,t)=>{switch(t){case"top":switch(e){case"bottom":return"top";case"bl":return"tl";case"br":return"tr";default:return e}case"bottom":switch(e){case"top":return"bottom";case"tl":return"bl";case"tr":return"br";default:return e}case"left":switch(e){case"right":return"left";case"rt":return"lt";case"rb":return"lb";default:return e}case"right":switch(e){case"left":return"right";case"lt":return"rt";case"lb":return"rb";default:return e}default:return e}},Ze=(e,t,{containerRect:l,triggerRect:o,popupRect:a,offset:n,translate:r})=>{const u=(e=>{switch(e){case"top":case"tl":case"tr":default:return"top";case"bottom":case"bl":case"br":return"bottom";case"left":case"lt":case"lb":return"left";case"right":case"rt":case"rb":return"right"}})(e),i=(()=>{const{height:e,width:t}=C();return{width:Math.min(t,window.innerWidth),height:Math.min(e,window.innerHeight)}})(),s=l.top+t.top,p=i.height-(l.top+t.top+a.height),c=l.left+t.left,d=i.width-(l.left+t.left+a.width);let f=e;if("top"===u&&s<0)if(o.top>a.height)t.top=-l.top;else{const u=Qe("bottom",o,a,{offset:n,translate:r});i.height-(l.top+u.top+a.height)>0&&(f=Xe(e,"bottom"),t.top=u.top)}if("bottom"===u&&p<0)if(i.height-o.bottom>a.height)t.top=-l.top+(i.height-a.height);else{const u=Qe("top",o,a,{offset:n,translate:r});l.top+u.top>0&&(f=Xe(e,"top"),t.top=u.top)}if("left"===u&&c<0)if(o.left>a.width)t.left=-l.left;else{const u=Qe("right",o,a,{offset:n,translate:r});i.width-(l.left+u.left+a.width)>0&&(f=Xe(e,"right"),t.left=u.left)}if("right"===u&&d<0)if(i.width-o.right>a.width)t.left=-l.left+(i.width-a.width);else{const u=Qe("left",o,a,{offset:n,translate:r});l.left+u.left>0&&(f=Xe(e,"left"),t.left=u.left)}return"top"!==u&&"bottom"!==u||(c<0?t.left=-l.left:d<0&&(t.left=-l.left+(i.width-a.width))),"left"!==u&&"right"!==u||(s<0?t.top=-l.top:p<0&&(t.top=-l.top+(i.height-a.height))),{popupPosition:t,position:f}},Qe=(e,t,l,{offset:o=0,translate:a=[0,0]}={})=>{var n;const r=null!=(n=w(a)?a:a[e])?n:[0,0];switch(e){case"top":return{left:t.scrollLeft+Math.round(t.width/2)-Math.round(l.width/2)+r[0],top:t.scrollTop-l.height-o+r[1]};case"tl":return{left:t.scrollLeft+r[0],top:t.scrollTop-l.height-o+r[1]};case"tr":return{left:t.scrollRight-l.width+r[0],top:t.scrollTop-l.height-o+r[1]};case"bottom":return{left:t.scrollLeft+Math.round(t.width/2)-Math.round(l.width/2)+r[0],top:t.scrollBottom+o+r[1]};case"bl":return{left:t.scrollLeft+r[0],top:t.scrollBottom+o+r[1]};case"br":return{left:t.scrollRight-l.width+r[0],top:t.scrollBottom+o+r[1]};case"left":return{left:t.scrollLeft-l.width-o+r[0],top:t.scrollTop+Math.round(t.height/2)-Math.round(l.height/2)+r[1]};case"lt":return{left:t.scrollLeft-l.width-o+r[0],top:t.scrollTop+r[1]};case"lb":return{left:t.scrollLeft-l.width-o+r[0],top:t.scrollBottom-l.height+r[1]};case"right":return{left:t.scrollRight+o+r[0],top:t.scrollTop+Math.round(t.height/2)-Math.round(l.height/2)+r[1]};case"rt":return{left:t.scrollRight+o+r[0],top:t.scrollTop+r[1]};case"rb":return{left:t.scrollRight+o+r[0],top:t.scrollBottom-l.height+r[1]};default:return{left:0,top:0}}},et=e=>{let t="0";["top","bottom"].includes(e)?t="50%":["left","lt","lb","tr","br"].includes(e)&&(t="100%");let l="0";return["left","right"].includes(e)?l="50%":["top","tl","tr","lb","rb"].includes(e)&&(l="100%"),`${t} ${l}`},tt=e=>e.scrollHeight>e.offsetHeight||e.scrollWidth>e.offsetWidth,lt=e=>{var t;const l=[];let o=e;for(;o&&o!==document.documentElement;)tt(o)&&l.push(o),o=null!=(t=o.parentElement)?t:void 0;return l},ot=()=>{const e={},t=P(),l=()=>{const l=x(e.value);l!==t.value&&(t.value=l)};return X((()=>l())),Z((()=>l())),{children:e,firstElement:t}};var at=$({name:"ResizeObserver",props:{watchOnUpdated:Boolean},emits:["resize"],setup(e,{emit:t,slots:l}){const{children:o,firstElement:a}=ot();let n;const r=()=>{n&&(n.disconnect(),n=null)};return W(a,(e=>{var l;n&&r(),e&&(l=e)&&(n=new ce((e=>{const l=e[0];t("resize",l)})),n.observe(l))})),Q((()=>{n&&r()})),()=>{var e;return o.value=null==(e=l.default)?void 0:e.call(l),o.value}}});function nt(e,t){const l=P(e[t]);return Z((()=>{const o=e[t];l.value!==o&&(l.value=o)})),l}const rt=Symbol("ArcoTrigger");var ut=$({name:"ClientOnly",setup(e,{slots:t}){const l=P(!1);return X((()=>l.value=!0)),()=>{var e;return l.value?null==(e=t.default)?void 0:e.call(t):null}}});const it=({popupContainer:e,visible:t,defaultContainer:l="body",documentContainer:o})=>{const a=P(e.value),n=P(),r=()=>{const t=O(e.value),r=t?e.value:l,u=null!=t?t:o?document.documentElement:O(l);r!==a.value&&(a.value=r),u!==n.value&&(n.value=u)};return X((()=>r())),W(t,(t=>{a.value!==e.value&&t&&r()})),{teleportContainer:a,containerRef:n}};var st=Object.defineProperty,pt=Object.defineProperties,ct=Object.getOwnPropertyDescriptors,dt=Object.getOwnPropertySymbols,ft=Object.prototype.hasOwnProperty,vt=Object.prototype.propertyIsEnumerable,ht=(e,t,l)=>t in e?st(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,mt=(e,t)=>{for(var l in t||(t={}))ft.call(t,l)&&ht(e,l,t[l]);if(dt)for(var l of dt(t))vt.call(t,l)&&ht(e,l,t[l]);return e};const bt=["onClick","onMouseenter","onMouseleave","onFocusin","onFocusout","onContextmenu"];var yt=$({name:"Trigger",inheritAttrs:!1,props:{popupVisible:{type:Boolean,default:void 0},defaultPopupVisible:{type:Boolean,default:!1},trigger:{type:[String,Array],default:"hover"},position:{type:String,default:"bottom"},disabled:{type:Boolean,default:!1},popupOffset:{type:Number,default:0},popupTranslate:{type:[Array,Object]},showArrow:{type:Boolean,default:!1},alignPoint:{type:Boolean,default:!1},popupHoverStay:{type:Boolean,default:!0},blurToClose:{type:Boolean,default:!0},clickToClose:{type:Boolean,default:!0},clickOutsideToClose:{type:Boolean,default:!0},unmountOnClose:{type:Boolean,default:!0},contentClass:{type:[String,Array,Object]},contentStyle:{type:Object},arrowClass:{type:[String,Array,Object]},arrowStyle:{type:Object},popupStyle:{type:Object},animationName:{type:String,default:"fade-in"},duration:{type:[Number,Object]},mouseEnterDelay:{type:Number,default:100},mouseLeaveDelay:{type:Number,default:100},focusDelay:{type:Number,default:0},autoFitPopupWidth:{type:Boolean,default:!1},autoFitPopupMinWidth:{type:Boolean,default:!1},autoFixPosition:{type:Boolean,default:!0},popupContainer:{type:[String,Object]},updateAtScroll:{type:Boolean,default:!1},autoFitTransformOrigin:{type:Boolean,default:!1},hideEmpty:{type:Boolean,default:!1},openedClass:{type:[String,Array,Object]},autoFitPosition:{type:Boolean,default:!0},renderToBody:{type:Boolean,default:!0},preventFocus:{type:Boolean,default:!1},scrollToClose:{type:Boolean,default:!1},scrollToCloseDistance:{type:Number,default:0}},emits:{"update:popupVisible":e=>!0,popupVisibleChange:e=>!0,show:()=>!0,hide:()=>!0,resize:()=>!0},setup(t,{emit:l,slots:o,attrs:a}){const{popupContainer:n}=N(t),r=u("trigger"),i=j((()=>Pe(a,bt))),s=E(e,void 0),p=j((()=>[].concat(t.trigger))),d=new Set,f=E(rt,void 0),{children:v,firstElement:h}=ot(),m=P(),b=P(t.defaultPopupVisible),y=P(t.position),g=P({}),w=P({}),C=P({}),x=P(),O=P({top:0,left:0});let $=null,L=null;const A=j((()=>{var e;return null!=(e=t.popupVisible)?e:b.value})),{teleportContainer:F,containerRef:z}=it({popupContainer:n,visible:A,documentContainer:!0}),{zIndex:I}=S("popup",{visible:A});let V=0,q=!1,K=!1;const J=e=>{if(t.alignPoint){const{pageX:t,pageY:l}=e;O.value={top:l,left:t}}},G=()=>{if(!h.value||!m.value||!z.value)return;const e=z.value.getBoundingClientRect(),l=t.alignPoint?{top:O.value.top,bottom:O.value.top,left:O.value.left,right:O.value.left,scrollTop:O.value.top,scrollBottom:O.value.top,scrollLeft:O.value.left,scrollRight:O.value.left,width:0,height:0}:Ue(h.value,e),o=()=>Ue(m.value,e),a=o(),{style:n,position:r}=((e,t,l,o,{offset:a=0,translate:n=[0,0],customStyle:r={},autoFitPosition:u=!1}={})=>{let i=e,s=Qe(e,l,o,{offset:a,translate:n});if(u){const r=Ze(e,s,{containerRect:t,popupRect:o,triggerRect:l,offset:a,translate:n});s=r.popupPosition,i=r.position}return{style:Ge({left:`${s.left}px`,top:`${s.top}px`},r),position:i}})(t.position,e,l,a,{offset:t.popupOffset,translate:t.popupTranslate,customStyle:t.popupStyle,autoFitPosition:t.autoFitPosition});t.autoFitTransformOrigin&&(w.value={transformOrigin:et(r)}),t.autoFitPopupMinWidth?n.minWidth=`${l.width}px`:t.autoFitPopupWidth&&(n.width=`${l.width}px`),y.value!==r&&(y.value=r),g.value=n,t.showArrow&&H((()=>{C.value=((e,t,l,{customStyle:o={}})=>{if(["top","tl","tr","bottom","bl","br"].includes(e)){let a=Math.abs(t.scrollLeft+t.width/2-l.scrollLeft);return a>l.width-8&&(a=t.width>l.width?l.width/2:l.width-8),["top","tl","tr"].includes(e)?Ge({left:`${a}px`,bottom:"0",transform:"translate(-50%,50%) rotate(45deg)"},o):Ge({left:`${a}px`,top:"0",transform:"translate(-50%,-50%) rotate(45deg)"},o)}let a=Math.abs(t.scrollTop+t.height/2-l.scrollTop);return a>l.height-8&&(a=t.height>l.height?l.height/2:l.height-8),["left","lt","lb"].includes(e)?Ge({top:`${a}px`,right:"0",transform:"translate(50%,-50%) rotate(45deg)"},o):Ge({top:`${a}px`,left:"0",transform:"translate(-50%,-50%) rotate(45deg)"},o)})(r,l,o(),{customStyle:t.arrowStyle})}))},U=(e,t)=>{if(e===A.value&&0===V)return;const o=()=>{b.value=e,l("update:popupVisible",e),l("popupVisibleChange",e),e&&H((()=>{G()}))};e||($=null,L=null),t?(V&&(window.clearTimeout(V),V=0),e!==A.value&&(V=window.setTimeout(o,t))):o()},re=e=>{var l;null==(l=a.onClick)||l.call(a,e),t.disabled||A.value&&!t.clickToClose||(p.value.includes("click")?(J(e),U(!A.value)):p.value.includes("contextMenu")&&A.value&&U(!1))},ue=e=>{var l;null==(l=a.onMouseenter)||l.call(a,e),!t.disabled&&p.value.includes("hover")&&(J(e),U(!0,t.mouseEnterDelay))},ie=e=>{null==f||f.onMouseenter(e),ue(e)},se=e=>{var l;null==(l=a.onMouseleave)||l.call(a,e),!t.disabled&&p.value.includes("hover")&&U(!1,t.mouseLeaveDelay)},pe=e=>{null==f||f.onMouseleave(e),se(e)},de=e=>{var l;null==(l=a.onFocusin)||l.call(a,e),!t.disabled&&p.value.includes("focus")&&U(!0,t.focusDelay)},fe=e=>{var l;null==(l=a.onFocusout)||l.call(a,e),!t.disabled&&p.value.includes("focus")&&t.blurToClose&&U(!1)},ve=e=>{var l;null==(l=a.onContextmenu)||l.call(a,e),t.disabled||!p.value.includes("contextMenu")||A.value&&!t.clickToClose||(J(e),U(!A.value),e.preventDefault())};ee(rt,M({onMouseenter:ie,onMouseleave:pe,addChildRef:e=>{d.add(e),null==f||f.addChildRef(e)},removeChildRef:e=>{d.delete(e),null==f||f.removeChildRef(e)}}));const he=()=>{R(document.documentElement,"mousedown",ye),q=!1},me=nt(o,"content"),be=j((()=>{var e;return t.hideEmpty&&B(null==(e=me.value)?void 0:e.call(me))})),ye=e=>{var t,l,o;if(!(null==(t=h.value)?void 0:t.contains(e.target))&&!(null==(l=m.value)?void 0:l.contains(e.target))){for(const t of d)if(null==(o=t.value)?void 0:o.contains(e.target))return;he(),U(!1)}},ge=(e,l)=>{const[o,a]=e,{scrollTop:n,scrollLeft:r}=l;return Math.abs(n-o)>=t.scrollToCloseDistance||Math.abs(r-a)>=t.scrollToCloseDistance},Ce=we((e=>{if(A.value)if(t.scrollToClose||(null==s?void 0:s.scrollToClose)){const t=e.target;$||($=[t.scrollTop,t.scrollLeft]),ge($,t)?U(!1):G()}else G()})),xe=()=>{R(window,"scroll",Oe),K=!1},Oe=we((e=>{const t=e.target.documentElement;L||(L=[t.scrollTop,t.scrollLeft]),ge(L,t)&&(U(!1),xe())})),Se=()=>{A.value&&G()},Be=()=>{Se(),l("resize")},Te=e=>{t.preventFocus&&e.preventDefault()};null==f||f.addChildRef(m);const ke=j((()=>A.value?t.openedClass:void 0));let Re;W(A,(e=>{if(t.clickOutsideToClose&&(!e&&q?he():e&&!q&&(T(document.documentElement,"mousedown",ye),q=!0)),(t.scrollToClose||(null==s?void 0:s.scrollToClose))&&(T(window,"scroll",Oe),K=!0),t.updateAtScroll||(null==s?void 0:s.updateAtScroll))if(e){Re=lt(h.value);for(const e of Re)e.addEventListener("scroll",Ce)}else if(Re){for(const e of Re)e.removeEventListener("scroll",Ce);Re=void 0}e&&(je.value=!0)})),W((()=>[t.autoFitPopupWidth,t.autoFitPopupMinWidth]),(()=>{A.value&&G()}));const{createResizeObserver:Me,destroyResizeObserver:Ee}=(({elementRef:e,onResize:t})=>{let l;return{createResizeObserver:()=>{e.value&&(l=new ce((e=>{const l=e[0];c(t)&&t(l)})),l.observe(e.value))},destroyResizeObserver:()=>{l&&(l.disconnect(),l=null)}}})({elementRef:z,onResize:Se});X((()=>{if(Me(),A.value&&(G(),t.clickOutsideToClose&&!q&&(T(document.documentElement,"mousedown",ye),q=!0),t.updateAtScroll||(null==s?void 0:s.updateAtScroll))){Re=lt(h.value);for(const e of Re)e.addEventListener("scroll",Ce)}})),Z((()=>{A.value&&G()})),te((()=>{U(!1)})),Q((()=>{if(null==f||f.removeChildRef(m),Ee(),q&&he(),K&&xe(),Re){for(const e of Re)e.removeEventListener("scroll",Ce);Re=void 0}}));const je=P(A.value),$e=P(!1),Le=()=>{$e.value=!0},Ae=()=>{$e.value=!1,A.value&&l("show")},Fe=()=>{$e.value=!1,A.value||(je.value=!1,l("hide"))};return()=>{var e,l;return v.value=null!=(l=null==(e=o.default)?void 0:e.call(o))?l:[],k(v.value,{class:ke.value,onClick:re,onMouseenter:ue,onMouseleave:se,onFocusin:de,onFocusout:fe,onContextmenu:ve}),D(Y,null,[t.autoFixPosition?D(at,{onResize:Be},{default:()=>[v.value]}):v.value,D(ut,null,{default:()=>[D(le,{to:F.value,disabled:!t.renderToBody},{default:()=>[(!t.unmountOnClose||A.value||je.value)&&!be.value&&D(at,{onResize:Se},{default:()=>{return[D("div",_({ref:m,class:[`${r}-popup`,`${r}-position-${y.value}`],style:(e=mt({},g.value),l={zIndex:I.value,pointerEvents:$e.value?"none":"auto"},pt(e,ct(l))),"trigger-placement":y.value,onMouseenter:ie,onMouseleave:pe,onMousedown:Te},i.value),[D(oe,{name:t.animationName,duration:t.duration,appear:!0,onBeforeEnter:Le,onAfterEnter:Ae,onBeforeLeave:Le,onAfterLeave:Fe},{default:()=>{var e;return[ae(D("div",{class:`${r}-popup-wrapper`,style:w.value},[D("div",{class:[`${r}-content`,t.contentClass],style:t.contentStyle},[null==(e=o.content)?void 0:e.call(o)]),t.showArrow&&D("div",{ref:x,class:[`${r}-arrow`,t.arrowClass],style:C.value},null)]),[[ne,A.value]])]}})])];var e,l}})]})]})])}}});const gt=Object.assign(yt,{install:(e,t)=>{y(e,t);const l=g(t);e.component(l+yt.name,yt)}}),wt={ENTER:"Enter",ESC:"Escape",BACKSPACE:"Backspace",TAB:"Tab",SPACE:" ",ARROW_UP:"ArrowUp",ARROW_DOWN:"ArrowDown",ARROW_LEFT:"ArrowLeft",ARROW_RIGHT:"ArrowRight"},Ct=e=>JSON.stringify({key:e.key,ctrl:Boolean(e.ctrl),shift:Boolean(e.shift),alt:Boolean(e.alt),meta:Boolean(e.meta)}),xt=e=>{const l={};return e.forEach(((e,o)=>{const a=t(o)?{key:o}:o;l[Ct(a)]=e})),e=>{const t=Ct({key:e.key,ctrl:e.ctrlKey,shift:e.shiftKey,alt:e.altKey,meta:e.metaKey}),o=l[t];o&&(e.stopPropagation(),o(e))}};function Ot(e,t){return void 0===t&&(t=15),+parseFloat(Number(e).toPrecision(t))}function St(e){var t=e.toString().split(/[eE]/),l=(t[0].split(".")[1]||"").length-+(t[1]||0);return l>0?l:0}function Bt(e){if(-1===e.toString().indexOf("e"))return Number(e.toString().replace(".",""));var t=St(e);return t>0?Ot(Number(e)*Math.pow(10,t)):Number(e)}function Tt(e){jt&&(e>Number.MAX_SAFE_INTEGER||e<Number.MIN_SAFE_INTEGER)&&console.warn(e+" is beyond boundary when transfer to integer, the results may not be accurate")}function kt(e){return function(){for(var t=[],l=0;l<arguments.length;l++)t[l]=arguments[l];var o=t[0];return t.slice(1).reduce((function(t,l){return e(t,l)}),o)}}var Rt=kt((function(e,t){var l=Bt(e),o=Bt(t),a=St(e)+St(t),n=l*o;return Tt(n),n/Math.pow(10,a)})),Pt=kt((function(e,t){var l=Math.pow(10,Math.max(St(e),St(t)));return(Rt(e,l)+Rt(t,l))/l})),Mt=kt((function(e,t){var l=Math.pow(10,Math.max(St(e),St(t)));return(Rt(e,l)-Rt(t,l))/l})),Et=kt((function(e,t){var l=Bt(e),o=Bt(t);return Tt(l),Tt(o),Rt(l/o,Ot(Math.pow(10,St(t)-St(e))))}));var jt=!0;var $t={strip:Ot,plus:Pt,minus:Mt,times:Rt,divide:Et,round:function(e,t){var l=Math.pow(10,t),o=Et(Math.round(Math.abs(Rt(e,l))),l);return e<0&&0!==o&&(o=Rt(o,-1)),o},digitLength:St,float2Fixed:Bt,enableBoundaryChecking:function(e){void 0===e&&(e=!0),jt=e}};const Lt=(e,t)=>{if(!e||!t)return;const l=(t=t.replace(/\[(\w+)\]/g,".$1")).split(".");if(0===l.length)return;let o=e;for(let a=0;a<l.length;a++){if(!p(o)&&!w(o)||!l[a])return;if(a===l.length-1)return o[l[a]];o=o[l[a]]}},At=(e,t,l,{addPath:o}={})=>{if(!e||!t)return;const a=(t=t.replace(/\[(\w+)\]/g,".$1")).split(".");if(0===a.length)return;let n=e;for(let r=0;r<a.length;r++){if(!p(n)&&!w(n)||!a[r])return;r!==a.length-1?(o&&i(n[a[r]])&&(n[a[r]]={}),n=n[a[r]]):n[a[r]]=l}};var Ft=Object.defineProperty,zt=Object.getOwnPropertySymbols,It=Object.prototype.hasOwnProperty,Vt=Object.prototype.propertyIsEnumerable,Nt=(e,t,l)=>t in e?Ft(e,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):e[t]=l,Wt=(e,t)=>{for(var l in t||(t={}))It.call(t,l)&&Nt(e,l,t[l]);if(zt)for(var l of zt(t))Vt.call(t,l)&&Nt(e,l,t[l]);return e};var Dt=l($({name:"Tooltip",components:{Trigger:gt},props:{popupVisible:{type:Boolean,default:void 0},defaultPopupVisible:{type:Boolean,default:!1},content:String,position:{type:String,default:"top"},mini:{type:Boolean,default:!1},backgroundColor:{type:String},contentClass:{type:[String,Array,Object]},contentStyle:{type:Object},arrowClass:{type:[String,Array,Object]},arrowStyle:{type:Object},popupContainer:{type:[String,Object]}},emits:{"update:popupVisible":e=>!0,popupVisibleChange:e=>!0},setup(e,{emit:t}){const l=u("tooltip"),o=P(e.defaultPopupVisible),a=j((()=>{var t;return null!=(t=e.popupVisible)?t:o.value})),n=j((()=>[`${l}-content`,e.contentClass,{[`${l}-mini`]:e.mini}])),r=j((()=>{if(e.backgroundColor||e.contentStyle)return Wt({backgroundColor:e.backgroundColor},e.contentStyle)})),i=j((()=>[`${l}-popup-arrow`,e.arrowClass])),s=j((()=>{if(e.backgroundColor||e.arrowStyle)return Wt({backgroundColor:e.backgroundColor},e.arrowStyle)}));return{prefixCls:l,computedPopupVisible:a,contentCls:n,computedContentStyle:r,arrowCls:i,computedArrowStyle:s,handlePopupVisibleChange:e=>{o.value=e,t("update:popupVisible",e),t("popupVisibleChange",e)}}}}),[["render",function(e,t,l,o,a,n){const r=L("Trigger");return A(),z(r,{class:V(e.prefixCls),trigger:"hover",position:e.position,"popup-visible":e.computedPopupVisible,"popup-offset":10,"show-arrow":"","content-class":e.contentCls,"content-style":e.computedContentStyle,"arrow-class":e.arrowCls,"arrow-style":e.computedArrowStyle,"popup-container":e.popupContainer,"animation-name":"zoom-in-fade-out","auto-fit-transform-origin":"",role:"tooltip",onPopupVisibleChange:e.handlePopupVisibleChange},{content:J((()=>[G(e.$slots,"content",{},(()=>[q(re(e.content),1)]))])),default:J((()=>[G(e.$slots,"default")])),_:3},8,["class","position","popup-visible","content-class","content-style","arrow-class","arrow-style","popup-container","onPopupVisibleChange"])}]]);const _t=Object.assign(Dt,{install:(e,t)=>{y(e,t);const l=g(t);e.component(l+Dt.name,Dt)}});export{Oe as B,ut as C,xe as E,Ce as F,_e as I,wt as K,at as R,gt as T,me as a,Ne as b,Ie as c,nt as d,_t as e,We as f,ge as g,it as h,$t as i,xt as j,Lt as k,Me as l,Pe as o,ye as r,At as s,we as t,Ve as u};

import{a,b as e}from"./apiCommon-DcubqwY_.js";import{r as o}from"./vue-D-10XvVk.js";const t=()=>{const t=o([]),l=o([]),r=o([]),n=o([]),c=o([]),s=o([]),i=o([]),u=o(!1),y=o([]),b=o([]),v=o([]),d=o([]);return{approveStateOptions:[{label:"审核中",value:0,color:"blue"},{label:"审核通过",value:1,color:"green"},{label:"审核拒绝",value:-1,color:"red"}],saleStateOptions:[{label:"已上架",value:1,color:"green"},{label:"未上架",value:-1,color:"red"}],delStateOptions:[{label:"未删除",value:1,color:"blue"},{label:"已删除",value:-1,color:"red"}],userStateOptions:[{label:"启用",value:1,color:"blue"},{label:"禁用",value:-1,color:"red"}],orderStateOptions:[{label:"待付款",value:0,color:"gray"},{label:"待发货",value:1,color:"orange"},{label:"待收货/待核销",value:2,color:"blue"},{label:"已收货/已核销",value:3,color:"green"},{label:"退款/售后",value:4,color:"green"},{label:"已取消",value:-1,color:"red"}],orderDeliveryTypeOptions:[{label:"快递发货",value:1,color:"blue"},{label:"线下核销",value:2,color:"green"}],orderPayTypeOptions:[{label:"微信支付",value:1,color:"blue"}],storeBusinessStateOptions:[{label:"正常营业",value:1,color:"blue"},{label:"暂停营业",value:-1,color:"red"}],announcementStateOptions:[{label:"已读",value:1,color:"blue"},{label:"未读",value:-1,color:"red"}],bannerLinkTypeOptions:[{label:"店铺分类",value:1,color:"cyan"},{label:"店铺详情",value:2,color:"blue"},{label:"外部链接",value:3,color:"gray"},{label:"营销活动",value:4,color:"purple"}],storeOptions:t,initStoreOptions:async({storeCategoryId:e,storeSubCategoryId:o}={storeCategoryId:void 0,storeSubCategoryId:void 0})=>{try{const{data:l}=await a("store",{storeCategoryId:e,storeSubCategoryId:o});t.value=l}catch(l){}},allStoreCategoryOptions:l,initAllStoreCategoryOptions:async()=>{try{const{data:e}=await a("allStoreCategory");l.value=e}catch(e){}},storeMccOptions:r,initStoreMccOptions:async()=>{try{const{data:e}=await a("storeMccCodes");r.value=e}catch(e){}},storeCategoryOptions:n,initStoreCategoryOptions:async()=>{try{const{data:e}=await a("storeCategory");n.value=e}catch(e){}},storeSubCategoryOptions:c,initStoreSubCategoryOptions:async e=>{try{if(c.value=[],e){const{data:o}=await a("storeSubCategory",{parentId:e});c.value=o}}catch(o){}},commodityTypeOptions:[{label:"实物商品",value:1,color:"blue"},{label:"电子卡券",value:2,color:"green"}],commodityCategoryOptions:s,initCommodityCategoryOptions:async()=>{try{const{data:e}=await a("commodityCategory");s.value=e}catch(e){}},activityOptions:i,initActivityOptions:async e=>{try{if(i.value=[],!e)return;const{data:o}=await a("activity",{storeId:e});i.value=o}catch(o){}},addressTipsLoading:u,addressTips:y,getAddressTips:async a=>{try{y.value=[],u.value=!0;const{data:o}=await e(a);y.value=o,u.value=!1}catch(o){u.value=!1}},roleOptions:b,initRoleOptions:async()=>{try{const{data:e}=await a("platformAccountRole");b.value=e}catch(e){}},bankCodeOptions:v,initBankCodeOptions:async()=>{try{const{data:e}=await a("bankCodes");v.value=e}catch(e){}},bankBranchIdOptions:d,initBankBranchIdOptions:async({bankCode:e,bankBranchName:o})=>{try{d.value=[];const{data:t}=await a("bankBranchIds",{bankCode:e,bankBranchName:o});d.value=t}catch(t){}}}};export{t as u};

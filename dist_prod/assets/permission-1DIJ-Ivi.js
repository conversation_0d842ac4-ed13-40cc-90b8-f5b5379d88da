import"./index-DOhy6BH_.js";import{C as e}from"./index-DQjhgQFu.js";import{i as t,d as a,t as n,h as l,r as o,S as r,g as d,c as s,B as i,f as c,j as u,k as v,p,q as y,F as h,L as f,l as g,A as k,D as b,z as K,m as N,y as m,C as x,v as S,I as C,J as D,a6 as w,a as E,a3 as T,ab as L,o as $,u as I}from"./vue-D-10XvVk.js";import{e as P,B as O,aH as B,aT as j,I as A,i as F,c as M,ae as R,_,u as V,g as z,s as H,f as q,E as Y,aV as J}from"./index-D-8JbLQk.js";import{d as G,o as Q,t as U,b as W}from"./index-DDFSMqsG.js";import{R as X}from"./render-function-CAXdZVZM.js";import{V as Z}from"./index-Cuq5XRs0.js";import"./index-DGtjsHgS.js";import"./pick-Ccd8Sfcm.js";import"./ResizeObserver.es-CzGuHLZU.js";import"./resize-observer-Dtogi-DJ.js";import"./index-DD6vSYIM.js";import"./index-CdWxsKz_.js";const ee=Symbol("TreeInjectionKey");function te(e){return e.selectable&&!e.disabled}function ae(e){return!e.isLeaf&&e.children}function ne(e){return Set.prototype.add.bind(e)}function le(e){return Set.prototype.delete.bind(e)}function oe(e){return!e.disabled&&!e.disableCheckbox&&!!e.checkable}function re(e){var t;const a=[];return null==(t=e.children)||t.forEach((e=>{oe(e)&&a.push(e.key,...re(e))})),a}function de(e){var t;const{node:a,checkedKeySet:n,indeterminateKeySet:l}=e;let o=a.parent;for(;o;){if(oe(o)){const e=o.key,a=(null==(t=o.children)?void 0:t.filter(oe))||[];let r=0;const d=a.length;a.some((({key:e})=>{if(n.has(e))r+=1;else if(l.has(e))return r+=.5,!0;return!1})),r&&r!==d?l.add(e):l.delete(e),r&&r===d?n.add(e):n.delete(e)}o=o.parent}}function se(e){const{node:t,checked:a,checkedKeys:n,indeterminateKeys:l,checkStrictly:o=!1}=e,{key:r}=t,d=new Set(n),s=new Set(l);if(a?d.add(r):d.delete(r),s.delete(r),!o){const e=re(t);a?e.forEach(ne(d)):e.forEach(le(d)),e.forEach(le(s)),de({node:t,checkedKeySet:d,indeterminateKeySet:s})}return[[...d],[...s]]}function ie(){return t(ee)||{}}var ce=a({name:"TreeNodeSwitcher",components:{IconLoading:O,RenderFunction:X},props:{prefixCls:String,loading:Boolean,showLine:Boolean,treeNodeData:{type:Object},icons:{type:Object},nodeStatus:{type:Object}},emits:["click"],setup(e,{slots:t,emit:a}){const{icons:l,nodeStatus:o,treeNodeData:r}=n(e),d=ie(),s=G(t,"switcher-icon"),i=G(t,"loading-icon");return{getSwitcherIcon:()=>{var e,t,a;const n=null!=(t=null==(e=null==l?void 0:l.value)?void 0:e.switcherIcon)?t:s.value;return n?n(o.value):null==(a=d.switcherIcon)?void 0:a.call(d,r.value,o.value)},getLoadingIcon:()=>{var e,t,a;const n=null!=(t=null==(e=null==l?void 0:l.value)?void 0:e.loadingIcon)?t:i.value;return n?n(o.value):null==(a=d.loadingIcon)?void 0:a.call(d,r.value,o.value)},onClick(e){a("click",e)}}},render(){var e,t,a;const{prefixCls:n,getSwitcherIcon:o,getLoadingIcon:r,onClick:d,nodeStatus:s={},loading:i,showLine:c}=this,{expanded:u,isLeaf:v}=s;if(i)return null!=(e=r())?e:l(O);let p=null,y=!1;if(v)c&&(p=null!=(a=o())?a:l(j));else{const e=c?l("span",{class:`${n}-${u?"minus":"plus"}-icon`}):l(B);p=null!=(t=o())?t:e,y=!c}if(!p)return null;const h=l("span",{class:`${n}-switcher-icon`,onClick:d},p);return y?l(A,{class:`${n}-icon-hover`},(()=>h)):h}}),ue=Object.defineProperty,ve=Object.defineProperties,pe=Object.getOwnPropertyDescriptors,ye=Object.getOwnPropertySymbols,he=Object.prototype.hasOwnProperty,fe=Object.prototype.propertyIsEnumerable,ge=(e,t,a)=>t in e?ue(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a,ke=(e,t)=>{for(var a in t||(t={}))he.call(t,a)&&ge(e,a,t[a]);if(ye)for(var a of ye(t))fe.call(t,a)&&ge(e,a,t[a]);return e},be=(e,t)=>ve(e,pe(t));const Ke=(()=>{let e=0;return()=>(e+=1,`__arco_tree${e}`)})();function Ne({subEnable:e,superEnable:t,isLeaf:a,treeNodeData:n,level:l}){return F(e)?M(t)?t(n,{isLeaf:a,level:l}):null!=t&&t:e}function me(e){var t,a;const{treeNodeData:n,parentNode:l,isTail:o=!0,treeProps:r}=e,{fieldNames:d}=r||{},s=function(e,t){const a=ke({},e);t&&Object.keys(t).forEach((n=>{const l=t[n];l!==n&&(a[n]=e[l],delete a[l])}));return a}(n,d),i=r.loadMore?!!s.isLeaf:!(null==(t=s.children)?void 0:t.length),c=l?l.level+1:0,u=be(ke({},Q(s,["children"])),{key:null!=(a=s.key)?a:Ke(),selectable:Ne({subEnable:s.selectable,superEnable:null==r?void 0:r.selectable,isLeaf:i,level:c,treeNodeData:n}),disabled:!!s.disabled,disableCheckbox:!!s.disableCheckbox,checkable:Ne({subEnable:s.checkable,superEnable:null==r?void 0:r.checkable,isLeaf:i,level:c,treeNodeData:n}),isLeaf:i,isTail:o,blockNode:!!(null==r?void 0:r.blockNode),showLine:!!(null==r?void 0:r.showLine),level:c,lineless:l?[...l.lineless,l.isTail]:[],draggable:(v=s.draggable,p=null==r?void 0:r.draggable,!!(F(v)?p:v))});var v,p;return be(ke({},u),{treeNodeProps:u,treeNodeData:n,parent:l,parentKey:null==l?void 0:l.key,pathParentKeys:l?[...l.pathParentKeys,l.key]:[]})}function xe(){const e=d(),t=()=>{var t;return null!=(t=null==e?void 0:e.vnode.key)?t:Ke()},a=o(t());return r((()=>{a.value=t()})),a}const Se=a({name:"BaseTreeNode",components:{NodeSwitcher:ce,Checkbox:e,RenderFunction:X,IconDragDotVertical:V},props:{title:{type:String},selectable:{type:Boolean},disabled:{type:Boolean},disableCheckbox:{type:Boolean},checkable:{type:Boolean},draggable:{type:Boolean},isLeaf:{type:Boolean},icon:{type:Function},switcherIcon:{type:Function},loadingIcon:{type:Function},dragIcon:{type:Function},isTail:{type:Boolean},blockNode:{type:Boolean},showLine:{type:Boolean},level:{type:Number,default:0},lineless:{type:Array,default:()=>[]}},setup(e){const t=xe(),a=z("tree-node"),l=ie(),r=s((()=>{var e;return null==(e=l.key2TreeNode)?void 0:e.get(t.value)})),d=s((()=>r.value.treeNodeData)),c=s((()=>r.value.children)),u=s((()=>{var e;const t=null==(e=l.treeProps)?void 0:e.actionOnNodeClick;return t?R(a=t)?a:[a]:[];var a})),{isLeaf:v,isTail:p,selectable:y,disabled:h,disableCheckbox:f,draggable:g}=n(e),k=s((()=>{var e;return[`${a}`,{[`${a}-selected`]:E.value,[`${a}-is-leaf`]:v.value,[`${a}-is-tail`]:p.value,[`${a}-expanded`]:T.value,[`${a}-disabled-selectable`]:!y.value&&!(null==(e=l.treeProps)?void 0:e.disableSelectActionOnly),[`${a}-disabled`]:h.value}]})),b=o(),{isDragOver:K,isDragging:N,isAllowDrop:m,dropPosition:x,setDragStatus:S}=function(e){const{key:t,refTitle:a}=n(e),l=ie(),r=o(!1),d=o(!1),s=o(!1),i=o(0),c=U((e=>{if(!a.value)return;const n=a.value.getBoundingClientRect(),o=window.pageYOffset+n.top,{pageY:r}=e,d=n.height/4,c=r-o;i.value=c<d?-1:c<n.height-d?0:1,s.value=!l.allowDrop||l.allowDrop(t.value,i.value)}));return{isDragOver:r,isDragging:d,isAllowDrop:s,dropPosition:i,setDragStatus(e,a){switch(e){case"dragStart":d.value=!0,i.value=0,l.onDragStart&&l.onDragStart(t.value,a);break;case"dragEnd":d.value=!1,r.value=!1,i.value=0,c.cancel(),l.onDragEnd&&l.onDragEnd(t.value,a);break;case"dragOver":r.value=!0,c(a),l.onDragOver&&l.onDragOver(t.value,a);break;case"dragLeave":r.value=!1,i.value=0,c.cancel(),l.onDragLeave&&l.onDragLeave(t.value,a);break;case"drop":l.onDrop&&l.onDrop(t.value,i.value,a),r.value=!1,i.value=0,c.cancel()}}}}(i({key:t,refTitle:b})),C=s((()=>[`${a}-title`,{[`${a}-title-draggable`]:g.value,[`${a}-title-gap-top`]:K.value&&m.value&&x.value<0,[`${a}-title-gap-bottom`]:K.value&&m.value&&x.value>0,[`${a}-title-highlight`]:!N.value&&K.value&&m.value&&0===x.value,[`${a}-title-dragging`]:N.value,[`${a}-title-block`]:r.value.blockNode}])),D=s((()=>{var e,a;return null==(a=null==(e=l.checkedKeys)?void 0:e.includes)?void 0:a.call(e,t.value)})),w=s((()=>{var e,a;return null==(a=null==(e=l.indeterminateKeys)?void 0:e.includes)?void 0:a.call(e,t.value)})),E=s((()=>{var e,a;return null==(a=null==(e=l.selectedKeys)?void 0:e.includes)?void 0:a.call(e,t.value)})),T=s((()=>{var e,a;return null==(a=null==(e=l.expandedKeys)?void 0:e.includes)?void 0:a.call(e,t.value)})),L=s((()=>{var e,a;return null==(a=null==(e=l.loadingKeys)?void 0:e.includes)?void 0:a.call(e,t.value)})),$=s((()=>l.dragIcon)),I=s((()=>l.nodeIcon));function P(e){var a,n;v.value||(!(null==(a=c.value)?void 0:a.length)&&M(l.onLoadMore)?l.onLoadMore(t.value):null==(n=null==l?void 0:l.onExpand)||n.call(l,!T.value,t.value,e))}const O=i({loading:L,checked:D,selected:E,indeterminate:w,expanded:T,isLeaf:v}),B=s((()=>l.nodeTitle?()=>{var e;return null==(e=l.nodeTitle)?void 0:e.call(l,d.value,O)}:void 0)),j=s((()=>l.nodeExtra?()=>{var e;return null==(e=l.nodeExtra)?void 0:e.call(l,d.value,O)}:void 0));return{nodekey:t,refTitle:b,prefixCls:a,classNames:k,titleClassNames:C,indeterminate:w,checked:D,expanded:T,selected:E,treeTitle:B,treeNodeData:d,loading:L,treeDragIcon:$,treeNodeIcon:I,extra:j,nodeStatus:O,onCheckboxChange(e,a){var n;f.value||h.value||null==(n=l.onCheck)||n.call(l,e,t.value,a)},onTitleClick(e){var a;u.value.includes("expand")&&P(e),y.value&&!h.value&&(null==(a=l.onSelect)||a.call(l,t.value,e))},onSwitcherClick:P,onDragStart(e){var t;if(g.value){e.stopPropagation(),S("dragStart",e);try{null==(t=e.dataTransfer)||t.setData("text/plain","")}catch(a){}}},onDragEnd(e){g.value&&(e.stopPropagation(),S("dragEnd",e))},onDragOver(e){g&&(e.stopPropagation(),e.preventDefault(),S("dragOver",e))},onDragLeave(e){g.value&&(e.stopPropagation(),S("dragLeave",e))},onDrop(e){g.value&&m.value&&(e.stopPropagation(),e.preventDefault(),S("drop",e))}}}}),Ce=["data-level","data-key"],De=["draggable"];var we=_(Se,[["render",function(e,t,a,n,l,o){const r=c("NodeSwitcher"),d=c("Checkbox"),s=c("RenderFunction"),i=c("IconDragDotVertical");return u(),v("div",{class:g(e.classNames),"data-level":e.level,"data-key":e.nodekey},[p(" 缩进 "),y("span",{class:g(`${e.prefixCls}-indent`)},[(u(!0),v(h,null,f(e.level,(t=>(u(),v("span",{key:t,class:g([`${e.prefixCls}-indent-block`,{[`${e.prefixCls}-indent-block-lineless`]:e.lineless[t-1]}])},null,2)))),128))],2),p(" switcher "),y("span",{class:g([`${e.prefixCls}-switcher`,{[`${e.prefixCls}-switcher-expanded`]:e.expanded}])},[k(r,{"prefix-cls":e.prefixCls,loading:e.loading,"show-line":e.showLine,"tree-node-data":e.treeNodeData,icons:{switcherIcon:e.switcherIcon,loadingIcon:e.loadingIcon},"node-status":e.nodeStatus,onClick:e.onSwitcherClick},b({_:2},[e.$slots["switcher-icon"]?{name:"switcher-icon",fn:K((()=>[p(" @slot 定制 switcher 图标，会覆盖 Tree 的配置 "),N(e.$slots,"switcher-icon")]))}:void 0,e.$slots["loading-icon"]?{name:"loading-icon",fn:K((()=>[p(" @slot 定制 loading 图标，会覆盖 Tree 的配置 "),N(e.$slots,"loading-icon")]))}:void 0]),1032,["prefix-cls","loading","show-line","tree-node-data","icons","node-status","onClick"])],2),p(" checkbox "),e.checkable?(u(),m(d,{key:0,disabled:e.disableCheckbox||e.disabled,"model-value":e.checked,indeterminate:e.indeterminate,"uninject-group-context":"",onChange:e.onCheckboxChange},null,8,["disabled","model-value","indeterminate","onChange"])):p("v-if",!0),p(" 内容 "),y("span",{ref:"refTitle",class:g(e.titleClassNames),draggable:e.draggable,onDragstart:t[0]||(t[0]=(...t)=>e.onDragStart&&e.onDragStart(...t)),onDragend:t[1]||(t[1]=(...t)=>e.onDragEnd&&e.onDragEnd(...t)),onDragover:t[2]||(t[2]=(...t)=>e.onDragOver&&e.onDragOver(...t)),onDragleave:t[3]||(t[3]=(...t)=>e.onDragLeave&&e.onDragLeave(...t)),onDrop:t[4]||(t[4]=(...t)=>e.onDrop&&e.onDrop(...t)),onClick:t[5]||(t[5]=(...t)=>e.onTitleClick&&e.onTitleClick(...t))},[e.$slots.icon||e.icon||e.treeNodeIcon?(u(),v("span",{key:0,class:g([`${e.prefixCls}-icon`,`${e.prefixCls}-custom-icon`])},[p(" 节点图标 "),e.$slots.icon?N(e.$slots,"icon",x(S({key:0},e.nodeStatus))):e.icon?(u(),m(s,S({key:1,"render-func":e.icon},e.nodeStatus),null,16,["render-func"])):e.treeNodeIcon?(u(),m(s,S({key:2,"render-func":e.treeNodeIcon,node:e.treeNodeData},e.nodeStatus),null,16,["render-func","node"])):p("v-if",!0)],2)):p("v-if",!0),y("span",{class:g(`${e.prefixCls}-title-text`)},[e.treeTitle?(u(),m(s,{key:0,"render-func":e.treeTitle},null,8,["render-func"])):(u(),v(h,{key:1},[p(" 标题，treeTitle 优先级高于节点的 title "),N(e.$slots,"title",{title:e.title},(()=>[C(D(e.title),1)]))],2112)),e.draggable?(u(),v("span",{key:2,class:g([`${e.prefixCls}-icon`,`${e.prefixCls}-drag-icon`])},[p(" 拖拽图标 "),e.$slots["drag-icon"]?N(e.$slots,"drag-icon",x(S({key:0},e.nodeStatus))):e.dragIcon?(u(),m(s,S({key:1,"render-func":e.dragIcon},e.nodeStatus),null,16,["render-func"])):e.treeDragIcon?(u(),m(s,S({key:2,"render-func":e.treeDragIcon,node:e.treeNodeData},e.nodeStatus),null,16,["render-func","node"])):(u(),m(i,{key:3}))],2)):p("v-if",!0)],2)],42,De),p(" 额外 "),e.extra?(u(),m(s,{key:1,"render-func":e.extra},null,8,["render-func"])):p("v-if",!0)],10,Ce)}]]);const Ee=a({name:"ExpandTransition",props:{expanded:Boolean},emits:["end"],setup:(e,{emit:t})=>({onEnter(t){const a=`${t.scrollHeight}px`;t.style.height=e.expanded?"0":a,t.offsetHeight,t.style.height=e.expanded?a:"0"},onAfterEnter(a){a.style.height=e.expanded?"":"0",t("end")},onBeforeLeave(e){e.style.display="none"}})});var Te=_(a({name:"TransitionNodeList",components:{ExpandTransition:_(Ee,[["render",function(e,t,a,n,l,o){return u(),m(w,{onEnter:e.onEnter,onAfterEnter:e.onAfterEnter,onBeforeLeave:e.onBeforeLeave},{default:K((()=>[N(e.$slots,"default")])),_:3},8,["onEnter","onAfterEnter","onBeforeLeave"])}]]),BaseTreeNode:we},props:{nodeKey:{type:[String,Number],required:!0}},setup(e){const t=[`${z("tree")}-node-list`],a=ie(),{nodeKey:l}=n(e),o=s((()=>{var e,t;return null==(t=null==(e=a.expandedKeys)?void 0:e.includes)?void 0:t.call(e,l.value)})),r=s((()=>{var e;const t=new Set(a.expandedKeys||[]),n=null==(e=a.flattenTreeData)?void 0:e.filter((e=>{var t,n;return!!(null==(t=e.pathParentKeys)?void 0:t.includes(l.value))&&(!a.filterTreeNode||(null==(n=a.filterTreeNode)?void 0:n.call(a,e.treeNodeData)))}));return null==n?void 0:n.filter((e=>{var a;if(o.value)return null==(a=e.pathParentKeys)?void 0:a.every((e=>t.has(e)));const n=e.pathParentKeys.indexOf(l.value);return e.pathParentKeys.slice(n+1).every((e=>t.has(e)))}))})),d=s((()=>{var e,t;return(null==(e=a.currentExpandKeys)?void 0:e.includes(l.value))&&(null==(t=r.value)?void 0:t.length)}));return{classNames:t,visibleNodeList:r,show:d,expanded:o,onTransitionEnd(){var e;null==(e=a.onExpandEnd)||e.call(a,l.value)}}}}),[["render",function(e,t,a,n,l,o){const r=c("BaseTreeNode"),d=c("ExpandTransition");return u(),m(d,{expanded:e.expanded,onEnd:e.onTransitionEnd},{default:K((()=>[e.show?(u(),v("div",{key:0,class:g(e.classNames)},[(u(!0),v(h,null,f(e.visibleNodeList,(e=>(u(),m(r,S({key:e.key},e.treeNodeProps),null,16)))),128))],2)):p("v-if",!0)])),_:1},8,["expanded","onEnd"])}]]),Le=Object.defineProperty,$e=Object.getOwnPropertySymbols,Ie=Object.prototype.hasOwnProperty,Pe=Object.prototype.propertyIsEnumerable,Oe=(e,t,a)=>t in e?Le(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a,Be=a({name:"TreeNode",inheritAttrs:!1,props:((e,t)=>{for(var a in t||(t={}))Ie.call(t,a)&&Oe(e,a,t[a]);if($e)for(var a of $e(t))Pe.call(t,a)&&Oe(e,a,t[a]);return e})({},we.props),setup(e,{slots:t,attrs:a}){const n=xe();return()=>k(h,null,[k(we,S(e,a,{key:n.value}),t),k(Te,{key:n.value,nodeKey:n.value},null)])}});function je(e){const{defaultCheckedKeys:t,checkedKeys:a,key2TreeNode:l,checkStrictly:r,halfCheckedKeys:d,onlyCheckLeaf:i}=n(e),c=o(!1),u=o([]),v=o([]),p=o(),y=o(),h=e=>function(e){const{initCheckedKeys:t,key2TreeNode:a,checkStrictly:n,onlyCheckLeaf:l}=e,o=new Set,r=new Set,d=new Set;return n?t.forEach(ne(o)):t.forEach((e=>{var t;const n=a.get(e);if(!n||r.has(e)||l&&(null==(t=n.children)?void 0:t.length))return;const s=re(n);s.forEach(ne(r)),s.forEach(le(d)),o.add(e),d.delete(e),de({node:n,checkedKeySet:o,indeterminateKeySet:d})})),[[...o,...r],[...d]]}({initCheckedKeys:e,key2TreeNode:l.value,checkStrictly:r.value,onlyCheckLeaf:i.value}),f=e=>{const t=h(e);[u.value,v.value]=t};return f(a.value||(null==t?void 0:t.value)||[]),E((()=>{a.value?[p.value,y.value]=h(a.value):c.value&&(p.value=void 0,y.value=void 0,u.value=[],v.value=[]),c.value||(c.value=!0)})),{checkedKeys:s((()=>p.value||u.value)),indeterminateKeys:s((()=>r.value&&d.value?d.value:y.value||v.value)),setCheckedState:(e,t,a=!1)=>(a?f(e):(u.value=e,v.value=t),[u.value,v.value])}}function Ae(e){const{treeData:t,fieldNames:a,selectable:l,showLine:r,blockNode:d,checkable:i,loadMore:c,draggable:u}=n(e),v=o([]);E((()=>{var e,n;v.value=function(e,t){return function e(a,n){if(!a)return;const{fieldNames:l}=t,o=[];return a.forEach(((r,d)=>{const s=me({treeNodeData:r,treeProps:t,parentNode:n,isTail:d===a.length-1});s.children=e(r[(null==l?void 0:l.children)||"children"],s),o.push(s)})),o}(e)}(t.value||[],{selectable:null!=(e=null==l?void 0:l.value)&&e,showLine:!!(null==r?void 0:r.value),blockNode:!!(null==d?void 0:d.value),checkable:null!=(n=null==i?void 0:i.value)&&n,fieldNames:null==a?void 0:a.value,loadMore:!!(null==c?void 0:c.value),draggable:!!(null==u?void 0:u.value)})}));const p=s((()=>function(e){const t=[];return function e(a){a&&a.forEach((a=>{t.push(a),e(a.children)}))}(e),t}(v.value))),y=s((()=>function(e){const t=new Map;return e.forEach((e=>{t.set(e.key,e)})),t}(p.value)));return{treeData:v,flattenTreeData:p,key2TreeNode:y}}var Fe=_(a({name:"Tree",components:{VirtualList:Z,TreeNode:Be},props:{size:{type:String,default:"medium"},blockNode:{type:Boolean},defaultExpandAll:{type:Boolean,default:!0},multiple:{type:Boolean},checkable:{type:[Boolean,String,Function],default:!1},selectable:{type:[Boolean,Function],default:!0},checkStrictly:{type:Boolean},checkedStrategy:{type:String,default:"all"},defaultSelectedKeys:{type:Array},selectedKeys:{type:Array},defaultCheckedKeys:{type:Array},checkedKeys:{type:Array},defaultExpandedKeys:{type:Array},expandedKeys:{type:Array},data:{type:Array,default:()=>[]},fieldNames:{type:Object},showLine:{type:Boolean},loadMore:{type:Function},draggable:{type:Boolean},allowDrop:{type:Function},filterTreeNode:{type:Function},searchValue:{type:String,default:""},virtualListProps:{type:Object},defaultExpandSelected:{type:Boolean},defaultExpandChecked:{type:Boolean},autoExpandParent:{type:Boolean,default:!0},halfCheckedKeys:{type:Array},onlyCheckLeaf:{type:Boolean,default:!1},animation:{type:Boolean,default:!0},actionOnNodeClick:{type:String},disableSelectActionOnly:{type:Boolean,default:!1}},emits:{select:(e,t)=>!0,"update:selectedKeys":e=>!0,check:(e,t)=>!0,"update:checkedKeys":e=>!0,"update:halfCheckedKeys":e=>!0,expand:(e,t)=>!0,"update:expandedKeys":e=>!0,dragStart:(e,t)=>!0,dragEnd:(e,t)=>!0,dragOver:(e,t)=>!0,dragLeave:(e,t)=>!0,drop:e=>!0},setup(e,{emit:t,slots:a}){const{data:l,showLine:r,multiple:d,loadMore:c,checkStrictly:u,checkedKeys:v,defaultCheckedKeys:p,selectedKeys:y,defaultSelectedKeys:h,expandedKeys:f,defaultExpandedKeys:g,checkedStrategy:k,selectable:b,checkable:K,blockNode:N,fieldNames:m,size:x,defaultExpandAll:S,filterTreeNode:C,draggable:D,allowDrop:w,defaultExpandSelected:E,defaultExpandChecked:L,autoExpandParent:$,halfCheckedKeys:I,onlyCheckLeaf:P,animation:O}=n(e),B=z("tree"),j=s((()=>[`${B}`,{[`${B}-checkable`]:K.value,[`${B}-show-line`]:r.value},`${B}-size-${x.value}`])),A=G(a,"switcher-icon"),R=G(a,"loading-icon"),_=G(a,"drag-icon"),V=G(a,"icon"),H=G(a,"title"),q=G(a,"extra"),{treeData:Y,flattenTreeData:J,key2TreeNode:Q}=Ae(i({treeData:l,selectable:b,showLine:r,blockNode:N,checkable:K,fieldNames:m,loadMore:c,draggable:D})),{checkedKeys:U,indeterminateKeys:X,setCheckedState:Z}=je(i({defaultCheckedKeys:p,checkedKeys:v,checkStrictly:u,key2TreeNode:Q,halfCheckedKeys:I,onlyCheckLeaf:P})),[te,ae]=W((null==h?void 0:h.value)||[],i({value:y})),ne=o([]),le=o();const[oe,re]=W(function(){if(null==g?void 0:g.value){const e=new Set([]);return g.value.forEach((t=>{if(e.has(t))return;const a=Q.value.get(t);a&&[...$.value?a.pathParentKeys:[],t].forEach((t=>e.add(t)))})),[...e]}if(S.value)return J.value.filter((e=>e.children&&e.children.length)).map((e=>e.key));if(E.value||L.value){const e=new Set([]),t=t=>{t.forEach((t=>{const a=Q.value.get(t);a&&(a.pathParentKeys||[]).forEach((t=>e.add(t)))}))};return E.value&&t(te.value),L.value&&t(U.value),[...e]}return[]}(),i({value:f})),de=o([]),ie=s((()=>{const e=new Set(oe.value),t=new Set(de.value);return J.value.filter((a=>{var n;if(!(!C||!C.value||(null==C?void 0:C.value(a.treeNodeData))))return!1;const l=F(a.parentKey),o=null==(n=a.pathParentKeys)?void 0:n.every((a=>e.has(a)&&!t.has(a)));return l||o}))}));function ce(e,t=k.value){let a=[...e];return"parent"===t?a=e.filter((t=>{const a=Q.value.get(t);return a&&!(!F(a.parentKey)&&e.includes(a.parentKey))})):"child"===t&&(a=e.filter((e=>{var t,a;return!(null==(a=null==(t=Q.value.get(e))?void 0:t.children)?void 0:a.length)}))),a}function ue(e){return e.map((e=>{var t;return(null==(t=Q.value.get(e))?void 0:t.treeNodeData)||void 0})).filter(Boolean)}function ve(e){const{targetKey:a,targetChecked:n,newCheckedKeys:l,newIndeterminateKeys:o,event:r}=e,d=a?Q.value.get(a):void 0,s=ce(l);t("update:checkedKeys",s),t("update:halfCheckedKeys",o),t("check",s,{checked:n,node:null==d?void 0:d.treeNodeData,checkedNodes:ue(s),halfCheckedKeys:o,halfCheckedNodes:ue(o),e:r})}function pe(e){const{targetKey:a,targetSelected:n,newSelectedKeys:l,event:o}=e,r=a?Q.value.get(a):void 0;t("update:selectedKeys",l),t("select",l,{selected:n,node:null==r?void 0:r.treeNodeData,selectedNodes:ue(l),e:o})}function ye(e){const{targetKey:a,targetExpanded:n,newExpandedKeys:l,event:o}=e,r=a?Q.value.get(a):void 0;t("expand",l,{expanded:n,node:null==r?void 0:r.treeNodeData,expandedNodes:ue(l),e:o}),t("update:expandedKeys",l)}function he(e,t,a){const n=Q.value.get(t);if(!n)return;const[l,o]=se({node:n,checked:e,checkedKeys:U.value,indeterminateKeys:X.value,checkStrictly:u.value});Z(l,o),ve({targetKey:t,targetChecked:e,newCheckedKeys:l,newIndeterminateKeys:o,event:a})}function fe(e,t,a){if(de.value.includes(t))return;if(!Q.value.get(t))return;const n=new Set(oe.value);e?n.add(t):n.delete(t);const l=[...n];re(l),O.value&&de.value.push(t),ye({targetKey:t,targetExpanded:e,newExpandedKeys:l,event:a})}function ge(e){const t=de.value.indexOf(e);de.value.splice(t,1)}const ke=s((()=>(null==c?void 0:c.value)?async e=>{if(!M(c.value))return;const t=Q.value.get(e);if(!t)return;const{treeNodeData:a}=t;ne.value=[...new Set([...ne.value,e])];try{await c.value(a),ne.value=ne.value.filter((t=>t!==e)),fe(!0,e),U.value.includes(e)&&he(!0,e)}catch(n){ne.value=ne.value.filter((t=>t!==e)),console.error("[tree]load data error: ",n)}}:void 0)),be=i({treeProps:e,switcherIcon:A,loadingIcon:R,dragIcon:_,nodeIcon:V,nodeTitle:H,nodeExtra:q,treeData:Y,flattenTreeData:J,key2TreeNode:Q,checkedKeys:U,indeterminateKeys:X,selectedKeys:te,expandedKeys:oe,loadingKeys:ne,currentExpandKeys:de,onLoadMore:ke,filterTreeNode:C,onCheck:he,onSelect:function(e,t){if(!Q.value.get(e))return;let a,n;if(d.value){const t=new Set(te.value);n=!t.has(e),n?t.add(e):t.delete(e),a=[...t]}else n=!0,a=[e];ae(a),pe({targetKey:e,targetSelected:n,newSelectedKeys:a,event:t})},onExpand:fe,onExpandEnd:ge,allowDrop(e,t){const a=Q.value.get(e);return!a||!M(w.value)||!!w.value({dropNode:a.treeNodeData,dropPosition:t})},onDragStart(e,a){const n=Q.value.get(e);le.value=n,n&&t("dragStart",a,n.treeNodeData)},onDragEnd(e,a){const n=Q.value.get(e);le.value=void 0,n&&t("dragEnd",a,n.treeNodeData)},onDragOver(e,a){const n=Q.value.get(e);n&&t("dragOver",a,n.treeNodeData)},onDragLeave(e,a){const n=Q.value.get(e);n&&t("dragLeave",a,n.treeNodeData)},onDrop(e,a,n){const l=Q.value.get(e);le.value&&l&&l.key!==le.value.key&&!l.pathParentKeys.includes(le.value.key||"")&&t("drop",{e:n,dragNode:le.value.treeNodeData,dropNode:l.treeNodeData,dropPosition:a})}});return T(ee,be),{classNames:j,visibleTreeNodeList:ie,treeContext:be,virtualListRef:o(),computedSelectedKeys:te,computedExpandedKeys:oe,computedCheckedKeys:U,computedIndeterminateKeys:X,getPublicCheckedKeys:ce,getNodes:ue,internalCheckNodes:function(e,t,a){if(!e.length)return;let n=[...U.value],l=[...X.value];e.forEach((e=>{const a=Q.value.get(e);a&&([n,l]=se({node:a,checked:t,checkedKeys:[...n],indeterminateKeys:[...l],checkStrictly:u.value}))})),Z(n,l),ve({targetKey:a,targetChecked:F(a)?void 0:t,newCheckedKeys:n,newIndeterminateKeys:l})},internalSetCheckedKeys:function(e){const[t,a]=Z(e,[],!0);ve({newCheckedKeys:t,newIndeterminateKeys:a})},internalSelectNodes:function(e,t,a){if(!e.length)return;let n;if(d.value){const a=new Set(te.value);e.forEach((e=>{t?a.add(e):a.delete(e)})),n=[...a]}else n=t?[e[0]]:[];ae(n),pe({targetKey:a,targetSelected:F(a)?void 0:t,newSelectedKeys:n})},internalSetSelectedKeys:function(e){let t=e;!d.value&&e.length>1&&(t=[e[0]]),ae(t),pe({newSelectedKeys:t})},internalExpandNodes:function(e,t,a){const n=new Set(oe.value);e.forEach((e=>{t?n.add(e):n.delete(e),ge(e)}));const l=[...n];re(l),ye({targetKey:a,targetExpanded:F(a)?void 0:t,newExpandedKeys:l})},internalSetExpandedKeys:function(e){de.value=[],re(e),ye({newExpandedKeys:e})}}},methods:{toggleCheck(e,t){const{key2TreeNode:a,onCheck:n,checkedKeys:l}=this.treeContext,o=!l.includes(e),r=a.get(e);r&&oe(r)&&n(o,e,t)},scrollIntoView(e){this.virtualListRef&&this.virtualListRef.scrollTo(e)},getSelectedNodes(){return this.getNodes(this.computedSelectedKeys)},getCheckedNodes(e={}){const{checkedStrategy:t,includeHalfChecked:a}=e,n=this.getPublicCheckedKeys(this.computedCheckedKeys,t);return[...this.getNodes(n),...a?this.getHalfCheckedNodes():[]]},getHalfCheckedNodes(){return this.getNodes(this.computedIndeterminateKeys)},getExpandedNodes(){return this.getNodes(this.computedExpandedKeys)},checkAll(e=!0){const{key2TreeNode:t}=this.treeContext,a=e?[...t.keys()].filter((e=>{const a=t.get(e);return a&&oe(a)})):[];this.internalSetCheckedKeys(a)},checkNode(e,t=!0,a=!1){const{checkStrictly:n,treeContext:l}=this,{key2TreeNode:o}=l,r=R(e),d=(r?e:[e]).filter((e=>{const t=o.get(e);return t&&oe(t)&&(n||!a||function(e){return P(e.isLeaf)?e.isLeaf:!e.children}(t))}));this.internalCheckNodes(d,t,r?void 0:e)},selectAll(e=!0){const{key2TreeNode:t}=this.treeContext,a=e?[...t.keys()].filter((e=>{const a=t.get(e);return a&&te(a)})):[];this.internalSetSelectedKeys(a)},selectNode(e,t=!0){const{key2TreeNode:a}=this.treeContext,n=R(e),l=(n?e:[e]).filter((e=>{const t=a.get(e);return t&&te(t)}));this.internalSelectNodes(l,t,n?void 0:e)},expandAll(e=!0){const{key2TreeNode:t}=this.treeContext,a=e?[...t.keys()].filter((e=>{const a=t.get(e);return a&&ae(a)})):[];this.internalSetExpandedKeys(a)},expandNode(e,t=!0){const{key2TreeNode:a}=this.treeContext,n=R(e),l=(n?e:[e]).filter((e=>{const t=a.get(e);return t&&ae(t)}));this.internalExpandNodes(l,t,n?void 0:e)}}}),[["render",function(e,t,a,n,l,o){const r=c("TreeNode"),d=c("VirtualList");return u(),v("div",{class:g(e.classNames)},[e.virtualListProps?(u(),m(d,S({key:0,ref:"virtualListRef"},e.virtualListProps,{data:e.visibleTreeNodeList}),{item:K((({item:t})=>[k(r,S({key:`${e.searchValue}-${t.key}`},t.treeNodeProps),null,16)])),_:1},16,["data"])):(u(!0),v(h,{key:1},f(e.visibleTreeNodeList,(e=>(u(),m(r,S({key:e.key},e.treeNodeProps),null,16)))),128))],2)}]]);const Me=Object.assign(Fe,{install:(e,t)=>{H(e,t);const a=q(t);e.component(a+Fe.name,Fe)}}),Re=a({__name:"permission",props:{permission:{}},setup(e,{expose:t}){const a=e,n=Y(),l=e=>{let t=[];return e.map((e=>{var a,n;t.push({key:e.name,title:null==(a=e.meta)?void 0:a.title,disabled:!!(null==(n=e.meta)?void 0:n.disabled),children:e.children?l(e.children):[]})})),t};L();const r=s((()=>n.getAllMenus)),d=l(r.value);J(r.value,"name");const i=o([]),c=o([]),v=(e,t)=>{i.value=e,c.value=t.halfCheckedKeys};return $((()=>{var e,t;i.value=(null==(e=a.permission)?void 0:e.checkedKeys)??[],c.value=(null==(t=a.permission)?void 0:t.halfCheckedKeys)??[]})),t({checkedKeys:i,halfCheckedKeys:c}),(e,t)=>{const a=Me;return u(),m(a,{"checked-keys":I(i),"half-checked-keys":I(c),"selected-keys":I(i),checkable:"",multiple:!0,data:I(d),onCheck:v},null,8,["checked-keys","half-checked-keys","selected-keys","data"])}}});export{Re as default};

import"./index-DOhy6BH_.js";import{C as e}from"./index-CdWxsKz_.js";import{D as l,a}from"./index-DIKBiUsz.js";import{_ as o}from"./index-HLwNT5T9.js";import{u as i}from"./index-DPq_x2ya.js";import{d as t,ae as n,o as s,K as d,j as r,k as f,q as u,A as p,z as m,I as b,J as _,u as j,y as c,p as x,F as h}from"./vue-D-10XvVk.js";import{_ as y}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-D-8JbLQk.js";import"./index-BEo1tUsK.js";import"./pick-Ccd8Sfcm.js";import"./use-index-D_ozg7PK.js";import"./index-DGtjsHgS.js";import"./index-DD6vSYIM.js";import"./index-DDFSMqsG.js";import"./ResizeObserver.es-CzGuHLZU.js";import"./index-CuYx5vtf.js";import"./render-function-CAXdZVZM.js";import"./apiUpload-DpATemHF.js";import"./index-Cuq5XRs0.js";import"./resize-observer-Dtogi-DJ.js";import"./index-DQjhgQFu.js";import"./index-DdMaxvYa.js";import"./index-DfEXMvnc.js";import"./use-children-components-v8i8lsOx.js";import"./index-DmW4RN1x.js";import"./index-CUtvFEc_.js";import"./index-CHOaln3D.js";import"./index-DVDXfQhn.js";import"./index-dpn1_5z1.js";import"./dayjs.min-Daes5FZc.js";/* empty css              */import"./useLoading-D5mh7tTu.js";import"./usePagination-Dd_EW2BO.js";import"./index-O7pr3qsq.js";const k={class:"h-[700px] overflow-y-scroll no-scrollbar"},v={class:"h-full flex flex-col gap-[18px]"},g=y(t({__name:"detail",props:{id:{}},setup(t,{expose:y}){const g=n(),P=t,{form:N,detail:w}=i(1);s((()=>{w(P.id)}));return y({formRef:d("formRef"),form:N}),(i,t)=>{const n=l,s=o,d=a,y=e;return r(),f("div",k,[u("div",v,[p(y,{bordered:!1},{default:m((()=>[p(d,{bordered:"",size:"large",column:1},{default:m((()=>[p(n,{label:"申请主体"},{default:m((()=>[b(_(1===j(N).type?"个人":"单位"),1)])),_:1}),p(n,{label:"个人证件照"},{default:m((()=>[p(s,{modelValue:j(N).photo,disabled:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),p(y,{bordered:!1,title:"基本信息"},{default:m((()=>[p(d,{bordered:"",size:"large",column:2},{default:m((()=>["VipList"===j(g).name?(r(),c(n,{key:0,label:"会员编号",span:2},{default:m((()=>[b(_(j(N).vipNo),1)])),_:1})):x("",!0),1===j(N).type?(r(),f(h,{key:1},[p(n,{label:"姓名"},{default:m((()=>[b(_(j(N).info.name||"-"),1)])),_:1}),p(n,{label:"身份证号码"},{default:m((()=>[b(_(j(N).info.identityNumber||"-"),1)])),_:1}),p(n,{label:"民族"},{default:m((()=>[b(_(j(N).info.ethnicGroup||"-"),1)])),_:1}),p(n,{label:"性别"},{default:m((()=>[b(_(j(N).info.gender||"-"),1)])),_:1}),p(n,{label:"出生年月"},{default:m((()=>[b(_(j(N).info.birth||"-"),1)])),_:1}),p(n,{label:"政治面貌"},{default:m((()=>[b(_(j(N).info.politicalOutlook||"-"),1)])),_:1}),p(n,{label:"文化程度"},{default:m((()=>[b(_(j(N).info.education||"-"),1)])),_:1}),p(n,{label:"工作单位"},{default:m((()=>[b(_(j(N).info.workplace||"-"),1)])),_:1}),p(n,{label:"职务"},{default:m((()=>[b(_(j(N).info.office||"-"),1)])),_:1}),p(n,{label:"办公电话"},{default:m((()=>[b(_(j(N).info.workPhone||"-"),1)])),_:1}),p(n,{label:"家庭住址"},{default:m((()=>[b(_(j(N).info.homeAddress||"-"),1)])),_:1}),p(n,{label:"住宅电话"},{default:m((()=>[b(_(j(N).info.homePhone||"-"),1)])),_:1}),p(n,{label:"入会介绍人"},{default:m((()=>[b(_(j(N).info.introducePerson||"-"),1)])),_:1}),p(n,{label:"介绍人联系方式"},{default:m((()=>[b(_(j(N).info.introducePhone||"-"),1)])),_:1}),p(n,{label:"特（专）长",span:2},{default:m((()=>[b(_(j(N).info.specialty||"-"),1)])),_:1}),p(n,{label:"个人简历",span:2},{default:m((()=>[b(_(j(N).info.resume||"-"),1)])),_:1})],64)):x("",!0),2===j(N).type?(r(),f(h,{key:2},[p(n,{label:"姓名"},{default:m((()=>[b(_(j(N).info.name||"-"),1)])),_:1}),p(n,{label:"身份证号码"},{default:m((()=>[b(_(j(N).info.identityNumber||"-"),1)])),_:1}),p(n,{label:"出生年月"},{default:m((()=>[b(_(j(N).info.birth||"-"),1)])),_:1}),p(n,{label:"社团名称"},{default:m((()=>[b(_(j(N).info.societyName||"-"),1)])),_:1}),p(n,{label:"单位名称"},{default:m((()=>[b(_(j(N).info.workplaceName||"-"),1)])),_:1}),p(n,{label:"单位性质"},{default:m((()=>[b(_(j(N).info.workplaceNature||"-"),1)])),_:1}),p(n,{label:"工作内容"},{default:m((()=>[b(_(j(N).info.JobDescription||"-"),1)])),_:1}),p(n,{label:"法定代表人"},{default:m((()=>[b(_(j(N).info.legalPerson||"-"),1)])),_:1}),p(n,{label:"职务"},{default:m((()=>[b(_(j(N).info.office||"-"),1)])),_:1}),p(n,{label:"职称"},{default:m((()=>[b(_(j(N).info.jobTitle||"-"),1)])),_:1}),p(n,{label:"通讯地址"},{default:m((()=>[b(_(j(N).info.address||"-"),1)])),_:1}),p(n,{label:"联系电话"},{default:m((()=>[b(_(j(N).info.contactNumber||"-"),1)])),_:1}),p(n,{label:"入会介绍人"},{default:m((()=>[b(_(j(N).info.introducePerson||"-"),1)])),_:1}),p(n,{label:"介绍人联系方式"},{default:m((()=>[b(_(j(N).info.introducePhone||"-"),1)])),_:1}),p(n,{label:"个人简历",span:2},{default:m((()=>[b(_(j(N).info.resume||"-"),1)])),_:1}),p(n,{label:"入会理由",span:2},{default:m((()=>[b(_(j(N).info.joinInReason||"-"),1)])),_:1})],64)):x("",!0)])),_:1})])),_:1})])])}}}),[["__scopeId","data-v-7ffa9ca0"]]);export{g as default};

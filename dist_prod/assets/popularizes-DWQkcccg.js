import"./index-DOhy6BH_.js";import{I as e}from"./index-DDFSMqsG.js";import{S as l,T as a}from"./index-Cuq5XRs0.js";import"./index-DD6vSYIM.js";import"./index-DQjhgQFu.js";import{T as o,a as t,P as d}from"./index-DdMaxvYa.js";import{B as r,S as s}from"./index-DGtjsHgS.js";import{C as i}from"./index-CdWxsKz_.js";import{_ as n}from"./index-HLwNT5T9.js";import{A as p}from"./index-CUtvFEc_.js";import{C as u,R as c}from"./index-BEo1tUsK.js";import{F as m,a as f}from"./index-DVDXfQhn.js";import{R as h}from"./index-dpn1_5z1.js";/* empty css              */import{n as y}from"./index-D-8JbLQk.js";import{u as _}from"./useCommon-BuUbRw8e.js";import{u as v}from"./hooks-DI-DMeWq.js";import{d as g}from"./dayjs.min-Daes5FZc.js";import{I as w}from"./index-DfEXMvnc.js";import{d as x,o as S,k as V,q as j,A as b,z as k,f as $,j as T,u as z,I as P,y as N,p as U,J as C,F as A,L as Y}from"./vue-D-10XvVk.js";import"./pick-Ccd8Sfcm.js";import"./ResizeObserver.es-CzGuHLZU.js";import"./resize-observer-Dtogi-DJ.js";import"./use-children-components-v8i8lsOx.js";import"./index-CuYx5vtf.js";import"./render-function-CAXdZVZM.js";import"./apiUpload-DpATemHF.js";import"./_plugin-vue_export-helper-BCo6x5W8.js";import"./use-index-D_ozg7PK.js";import"./index-CHOaln3D.js";import"./apiCommon-DcubqwY_.js";import"./useLoading-D5mh7tTu.js";import"./usePagination-Dd_EW2BO.js";const F={class:"page-container"},D={class:"h-full flex flex-col gap-[18px]"},O={class:"text-left"},H={class:"pt-1"},I={class:"pt-1"},M=["src"],R={class:"text-left"},q={class:"pt-1"},L=["src"],B={class:"text-left"},J={class:"pt-1"},Z={class:"pt-1"},E={class:"text-left"},G={class:"pt-1"},K={class:"pt-1"},Q={class:"pt-1"},W={class:"pt-1"},X={class:"pt-1 w-[210px]"},ee={class:"text-left"},le={class:"pt-1"},ae={class:"pt-1"},oe={class:"pt-1 w-[210px] truncate"},te={class:"text-left"},de={class:"w-[300px] truncate"},re={class:"pt-1"},se={key:0,class:"pt-1"},ie={key:1,class:"pt-1"},ne={class:"pt-1"},pe={class:"pt-1"},ue={key:0,class:"pt-1"},ce={key:1},me={key:1},fe=x({__name:"popularizes",setup(x){const{orderStateOptions:fe,orderDeliveryTypeOptions:he,orderPayTypeOptions:ye}=_(),{loading:_e,queryParams:ve,pagination:ge,rows:we,reset:xe,pageChange:Se,pageSizeChange:Ve}=v({hasParentUser:1});return S((()=>{Se(1)})),(_,v)=>{const x=e,S=m,fe=u,je=l,be=w,ke=c,$e=h,Te=$("icon-search"),ze=r,Pe=$("icon-refresh"),Ne=s,Ue=f,Ce=i,Ae=o,Ye=p,Fe=a,De=n,Oe=t,He=d;return T(),V("div",F,[j("div",D,[b(Ce,{bordered:!1},{default:k((()=>[b(Ue,{model:z(ve),"auto-label-width":""},{default:k((()=>[b(ke,{gutter:16},{default:k((()=>[b(fe,{span:6},{default:k((()=>[b(S,{"show-colon":"",label:"订单编号",field:"orderNo"},{default:k((()=>[b(x,{modelValue:z(ve).orderNo,"onUpdate:modelValue":v[0]||(v[0]=e=>z(ve).orderNo=e),placeholder:`${_.$inputPlaceholder}订单编号`,"allow-clear":""},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),b(fe,{span:6},{default:k((()=>[b(S,{"show-colon":"",label:"配送方式",field:"deliveryType"},{default:k((()=>[b(je,{modelValue:z(ve).deliveryType,"onUpdate:modelValue":v[1]||(v[1]=e=>z(ve).deliveryType=e),options:z(he),placeholder:`${_.$selectPlaceholder}配送方式`,"allow-clear":""},null,8,["modelValue","options","placeholder"])])),_:1})])),_:1}),b(fe,{span:6},{default:k((()=>[b(S,{"show-colon":"",label:"付款方式",field:"payType"},{default:k((()=>[b(je,{modelValue:z(ve).payType,"onUpdate:modelValue":v[2]||(v[2]=e=>z(ve).payType=e),options:z(ye),placeholder:`${_.$selectPlaceholder}付款方式`,"allow-clear":""},null,8,["modelValue","options","placeholder"])])),_:1})])),_:1}),b(fe,{span:6},{default:k((()=>[b(S,{"show-colon":"",label:"商品名称",field:"commodityName"},{default:k((()=>[b(x,{modelValue:z(ve).commodityName,"onUpdate:modelValue":v[3]||(v[3]=e=>z(ve).commodityName=e),placeholder:`${_.$inputPlaceholder}商品名称`,"allow-clear":""},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),b(fe,{span:6},{default:k((()=>[b(S,{"show-colon":"",label:"店铺编号",field:"storeNo"},{default:k((()=>[b(x,{modelValue:z(ve).storeNo,"onUpdate:modelValue":v[4]||(v[4]=e=>z(ve).storeNo=e),placeholder:`${_.$inputPlaceholder}店铺编号`,"allow-clear":""},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),b(fe,{span:6},{default:k((()=>[b(S,{"show-colon":"",label:"店铺名称",field:"storeName"},{default:k((()=>[b(x,{modelValue:z(ve).storeName,"onUpdate:modelValue":v[5]||(v[5]=e=>z(ve).storeName=e),placeholder:`${_.$inputPlaceholder}店铺名称`,"allow-clear":""},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),b(fe,{span:6},{default:k((()=>[b(S,{"show-colon":"",label:"用户编号",field:"userNo"},{default:k((()=>[b(x,{modelValue:z(ve).userNo,"onUpdate:modelValue":v[6]||(v[6]=e=>z(ve).userNo=e),placeholder:`${_.$inputPlaceholder}用户编号`,"allow-clear":""},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),b(fe,{span:6},{default:k((()=>[b(S,{"show-colon":"",label:"用户昵称",field:"nickname"},{default:k((()=>[b(x,{modelValue:z(ve).nickname,"onUpdate:modelValue":v[7]||(v[7]=e=>z(ve).nickname=e),placeholder:`${_.$inputPlaceholder}用户昵称`,"allow-clear":""},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),b(fe,{span:6},{default:k((()=>[b(S,{"show-colon":"",label:"手机号",field:"mobile"},{default:k((()=>[b(be,{"hide-button":"",modelValue:z(ve).mobile,"onUpdate:modelValue":v[8]||(v[8]=e=>z(ve).mobile=e),placeholder:`${_.$inputPlaceholder}手机号`,"allow-clear":""},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),b(fe,{span:6},{default:k((()=>[b(S,{"show-colon":"",label:"付款金额"},{default:k((()=>[b(ke,{align:"center",class:"w-full"},{default:k((()=>[b(fe,{span:11},{default:k((()=>[b(S,{"no-style":"",field:"minAmount"},{default:k((()=>[b(be,{"hide-button":"",modelValue:z(ve).minAmount,"onUpdate:modelValue":v[9]||(v[9]=e=>z(ve).minAmount=e),placeholder:`${_.$inputPlaceholder}最小金额`,"allow-clear":""},{suffix:k((()=>v[13]||(v[13]=[P("元")]))),_:1},8,["modelValue","placeholder"])])),_:1})])),_:1}),b(fe,{span:2,class:"text-center"},{default:k((()=>v[14]||(v[14]=[P("-")]))),_:1}),b(fe,{span:11},{default:k((()=>[b(S,{"no-style":"",field:"maxAmount"},{default:k((()=>[b(be,{"hide-button":"",modelValue:z(ve).maxAmount,"onUpdate:modelValue":v[10]||(v[10]=e=>z(ve).maxAmount=e),placeholder:`${_.$inputPlaceholder}最大金额`,"allow-clear":""},{suffix:k((()=>v[15]||(v[15]=[P("元")]))),_:1},8,["modelValue","placeholder"])])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),b(fe,{span:6},{default:k((()=>[b(S,{"show-colon":"",label:"下单时间",field:"createTime"},{default:k((()=>[b($e,{modelValue:z(ve).createTime,"onUpdate:modelValue":v[11]||(v[11]=e=>z(ve).createTime=e),placeholder:[`${_.$selectPlaceholder}开始日期`,`${_.$selectPlaceholder}结束日期`]},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),b(fe,{span:6},{default:k((()=>[b(S,{"hide-label":""},{default:k((()=>[b(Ne,{size:18},{default:k((()=>[b(ze,{type:"primary",onClick:v[12]||(v[12]=e=>z(Se)(1))},{icon:k((()=>[b(Te)])),default:k((()=>[v[16]||(v[16]=P(" 查询 "))])),_:1}),b(ze,{type:"outline",onClick:z(xe)},{icon:k((()=>[b(Pe)])),default:k((()=>[v[17]||(v[17]=P(" 重置 "))])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),b(Ce,{bordered:!1,class:"flex-1 overflow-y-hidden","body-style":{height:"100%"}},{actions:k((()=>[z(ge).total?(T(),N(He,{key:0,current:z(ge).current,"page-size":z(ge).pageSize,"show-total":z(ge).showTotal,"show-page-size":z(ge).showPageSize,"page-size-options":z(ge).pageSizeOptions,total:z(ge).total,onChange:z(Se),onPageSizeChange:z(Ve)},null,8,["current","page-size","show-total","show-page-size","page-size-options","total","onChange","onPageSizeChange"])):U("",!0)])),default:k((()=>[b(Oe,{size:"large","row-key":"id",loading:z(_e),pagination:!1,data:z(we),bordered:{cell:!0},scroll:{y:"calc(100% - 52px)"}},{columns:k((()=>[b(Ae,{align:"center",title:"序号",width:80},{cell:k((({rowIndex:e})=>[P(C(z(ge).pageSize*(z(ge).current-1)+e+1),1)])),_:1}),b(Ae,{align:"center",title:"订单信息",width:300,ellipsis:"",tooltip:""},{cell:k((({record:e})=>[b(Ne,null,{default:k((()=>[j("div",O,[j("p",null,"订单编号："+C(e.orderNo),1),j("p",H,"下单时间："+C(e.createTime),1),j("p",I,"备注信息："+C(e.remark||"-"),1)])])),_:2},1024)])),_:1}),b(Ae,{align:"center",title:"商品信息",width:300},{cell:k((({record:e})=>[j("div",null,[(T(!0),V(A,null,Y(e.commodities,((e,l)=>(T(),V("div",{key:l},[b(Ne,null,{default:k((()=>[b(Ye,{shape:"square",size:32},{default:k((()=>[j("img",{src:e.cover},null,8,M)])),_:2},1024),j("div",R,[j("p",null,"商品名称："+C(e.name),1),j("p",q,"商品数量："+C(e.count)+" "+C(e.unit),1)])])),_:2},1024)])))),128))])])),_:1}),b(Ae,{align:"center",title:"用户信息",width:350,ellipsis:"",tooltip:""},{cell:k((({record:e})=>[b(Ne,null,{default:k((()=>[b(Ye,{size:32},{default:k((()=>[j("img",{src:e.user.avatar},null,8,L)])),_:2},1024),j("div",B,[j("p",null,"用户编号："+C(e.user.no),1),j("p",J,"用户昵称："+C(e.user.nickname),1),j("p",Z,"用户手机："+C(z(y)(e.user.mobile)),1)])])),_:2},1024)])),_:1}),b(Ae,{align:"center",title:"支付信息",width:300},{cell:k((({record:e})=>[b(Ne,null,{default:k((()=>{var l;return[j("div",E,[j("p",null,"商品金额："+C(e.commodityAmount.toFixed(2))+" 元",1),j("p",G,"优惠金额："+C(e.reduceAmount.toFixed(2))+" 元",1),j("p",K,"付款金额："+C(e.payAmount.toFixed(2))+" 元",1),j("p",Q,"付款方式："+C((null==(l=z(ye).find((l=>l.value===e.payType)))?void 0:l.label)??""),1),j("p",W,"支付状态："+C(0===e.payState?"待付款":-1===e.payState?"支付失败":1===e.payState?"支付成功":""),1),j("p",X,"付款时间："+C(e.payTime),1)])]})),_:2},1024)])),_:1}),b(Ae,{align:"center",title:"收货信息",width:300},{cell:k((({record:e})=>[b(Ne,null,{default:k((()=>{var l,a,o,t;return[j("div",ee,[j("p",null,[v[18]||(v[18]=P(" 配送方式： ")),b(Fe,{color:(null==(l=z(he).find((l=>l.value===e.deliveryType)))?void 0:l.color)??""},{default:k((()=>{var l;return[P(C((null==(l=z(he).find((l=>l.value===e.deliveryType)))?void 0:l.label)??""),1)]})),_:2},1032,["color"])]),j("p",le,"收货人："+C(1===e.deliveryType?null==(a=e.delivery)?void 0:a.userName:"-"),1),j("p",ae,"手机号码："+C(1===e.deliveryType?null==(o=e.delivery)?void 0:o.contactNumber:"-"),1),j("p",oe,"收货地址："+C(1===e.deliveryType?null==(t=e.delivery)?void 0:t.address:"-"),1)])]})),_:2},1024)])),_:1}),b(Ae,{align:"center",title:"订单状态",width:250},{cell:k((({record:e})=>[-1===e.state?(T(),N(Fe,{key:0,color:"gray"},{default:k((()=>v[19]||(v[19]=[P("已取消")]))),_:1})):U("",!0),-2===e.state?(T(),N(Fe,{key:1,color:"gray"},{default:k((()=>v[20]||(v[20]=[P("已关闭（已退款）")]))),_:1})):U("",!0),-3===e.state?(T(),N(Fe,{key:2,color:"gray"},{default:k((()=>v[21]||(v[21]=[P("售后中")]))),_:1})):U("",!0),0===e.state?(T(),N(Fe,{key:3,color:"red"},{default:k((()=>v[22]||(v[22]=[P("待付款")]))),_:1})):U("",!0),1===e.state?(T(),N(Fe,{key:4,color:"orange"},{default:k((()=>[P(C(-1===e.afterSaleState?"待发货":"待发货（售后申请被商家驳回）"),1)])),_:2},1024)):U("",!0),2===e.state?(T(),N(Fe,{key:5,color:"orange"},{default:k((()=>[P(C(-1===e.afterSaleState?1===e.deliveryType?"待收货":"待核销":1===e.deliveryType?"待收货（售后申请被商家驳回）":"待核销（售后申请被商家驳回）"),1)])),_:2},1024)):U("",!0),3===e.state?(T(),N(Fe,{key:6,color:"blue"},{default:k((()=>[P(C(-1===e.commentState?"待评价":"已完成"),1)])),_:2},1024)):U("",!0)])),_:1}),b(Ae,{align:"center",title:"售后",width:400},{cell:k((({record:e})=>[1===e.afterSaleState?(T(),N(Ne,{key:0},{default:k((()=>{var l,a,o,t,d,r,s,i,n,p,u,c,m;return[j("div",te,[j("p",de,"售后原因："+C(null==(l=e.afterSale)?void 0:l.reason),1),j("p",re,"审核状态："+C(1===(null==(a=e.afterSale)?void 0:a.approveState)?"审核通过":-1===(null==(o=e.afterSale)?void 0:o.approveState)?"审核拒绝":"待审核"),1),-1===(null==(t=e.afterSale)?void 0:t.approveState)?(T(),V("p",se,"拒绝原因："+C(null==(d=e.afterSale)?void 0:d.approveReason),1)):U("",!0),0!==(null==(r=e.afterSale)?void 0:r.approveState)?(T(),V("p",ie,"审核时间："+C(z(g)(null==(s=e.afterSale)?void 0:s.approveTime).format("YYYY-MM-DD HH:mm:ss")),1)):U("",!0),1===(null==(i=e.afterSale)?void 0:i.approveState)?(T(),V(A,{key:2},[j("p",ne,"退款金额："+C(null==(p=null==(n=e.afterSale)?void 0:n.refundAmount)?void 0:p.toFixed(2))+" 元",1),j("p",pe,"退款状态："+C(-1===(null==(u=e.afterSale)?void 0:u.refundState)?"未退款":0===(null==(c=e.afterSale)?void 0:c.refundState)?"退款中":"已退款"),1),1===e.refundState?(T(),V("p",ue,"退款时间："+C(z(g)(null==(m=e.afterSale)?void 0:m.refundTime).format("YYYY-MM-DD HH:mm:ss")),1)):U("",!0)],64)):U("",!0)])]})),_:2},1024)):(T(),V("span",ce,"-"))])),_:1}),b(Ae,{align:"center",title:"发票",width:200},{cell:k((({record:e})=>[e.invoice?(T(),N(De,{key:0,modelValue:e.invoice,disabled:"",class:"!justify-center"},null,8,["modelValue"])):(T(),V("span",me,"-"))])),_:1})])),_:1},8,["loading","data"])])),_:1})])])}}});export{fe as default};

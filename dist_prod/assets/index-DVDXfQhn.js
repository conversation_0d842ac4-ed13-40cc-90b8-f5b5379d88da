import{_ as e,g as t,c as r,ae as i,e as a,ax as o,k as l,s,f as n}from"./index-D-8JbLQk.js";import{d as u,r as c,t as d,c as p,B as h,a3 as f,j as b,k as g,m as v,l as m,af as y,i as j,o as O,b as w,f as C,y as x,z as S,a4 as M,v as E,p as L,A as k,I as F,J as V,g as _,q,L as $,a6 as P,F as W,n as B,a5 as R,D as z}from"./vue-D-10XvVk.js";import{f as A,b as T}from"./index-DGtjsHgS.js";import{u as I}from"./index-DOhy6BH_.js";import{R as H,e as D,a as N,k as Y,s as K}from"./index-DDFSMqsG.js";import{R as X,C as J}from"./index-BEo1tUsK.js";function Z(e){return"object"==typeof e&&null!=e&&1===e.nodeType}function G(e,t){return(!t||"hidden"!==e)&&"visible"!==e&&"clip"!==e}function Q(e,t){if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){var r=getComputedStyle(e,null);return G(r.overflowY,t)||G(r.overflowX,t)||!!(a=function(e){if(!e.ownerDocument||!e.ownerDocument.defaultView)return null;try{return e.ownerDocument.defaultView.frameElement}catch(t){return null}}(i=e))&&(a.clientHeight<i.scrollHeight||a.clientWidth<i.scrollWidth)}var i,a;return!1}function U(e,t,r,i,a,o,l,s){return o<e&&l>t||o>e&&l<t?0:o<=e&&s<=r||l>=t&&s>=r?o-e-i:l>t&&s<r||o<e&&s>r?l-t+a:0}var ee=function(e,t){var r=window,i=t.scrollMode,a=t.block,o=t.inline,l=t.boundary,s=t.skipOverflowHiddenElements,n="function"==typeof l?l:function(e){return e!==l};if(!Z(e))throw new TypeError("Invalid target");for(var u,c,d=document.scrollingElement||document.documentElement,p=[],h=e;Z(h)&&n(h);){if((h=null==(c=(u=h).parentElement)?u.getRootNode().host||null:c)===d){p.push(h);break}null!=h&&h===document.body&&Q(h)&&!Q(document.documentElement)||null!=h&&Q(h,s)&&p.push(h)}for(var f=r.visualViewport?r.visualViewport.width:innerWidth,b=r.visualViewport?r.visualViewport.height:innerHeight,g=window.scrollX||pageXOffset,v=window.scrollY||pageYOffset,m=e.getBoundingClientRect(),y=m.height,j=m.width,O=m.top,w=m.right,C=m.bottom,x=m.left,S="start"===a||"nearest"===a?O:"end"===a?C:O+y/2,M="center"===o?x+j/2:"end"===o?w:x,E=[],L=0;L<p.length;L++){var k=p[L],F=k.getBoundingClientRect(),V=F.height,_=F.width,q=F.top,$=F.right,P=F.bottom,W=F.left;if("if-needed"===i&&O>=0&&x>=0&&C<=b&&w<=f&&O>=q&&C<=P&&x>=W&&w<=$)return E;var B=getComputedStyle(k),R=parseInt(B.borderLeftWidth,10),z=parseInt(B.borderTopWidth,10),A=parseInt(B.borderRightWidth,10),T=parseInt(B.borderBottomWidth,10),I=0,H=0,D="offsetWidth"in k?k.offsetWidth-k.clientWidth-R-A:0,N="offsetHeight"in k?k.offsetHeight-k.clientHeight-z-T:0,Y="offsetWidth"in k?0===k.offsetWidth?0:_/k.offsetWidth:0,K="offsetHeight"in k?0===k.offsetHeight?0:V/k.offsetHeight:0;if(d===k)I="start"===a?S:"end"===a?S-b:"nearest"===a?U(v,v+b,b,z,T,v+S,v+S+y,y):S-b/2,H="start"===o?M:"center"===o?M-f/2:"end"===o?M-f:U(g,g+f,f,R,A,g+M,g+M+j,j),I=Math.max(0,I+v),H=Math.max(0,H+g);else{I="start"===a?S-q-z:"end"===a?S-P+T+N:"nearest"===a?U(q,P,V,z,T+N,S,S+y,y):S-(q+V/2)+N/2,H="start"===o?M-W-R:"center"===o?M-(W+_/2)+D/2:"end"===o?M-$+A+D:U(W,$,_,R,A+D,M,M+j,j);var X=k.scrollLeft,J=k.scrollTop;S+=J-(I=Math.max(0,Math.min(J+I/K,k.scrollHeight-V/K+N))),M+=X-(H=Math.max(0,Math.min(X+H/Y,k.scrollWidth-_/Y+D)))}E.push({el:k,top:I,left:H})}return E};function te(e){return e===Object(e)&&0!==Object.keys(e).length}function re(e,t){var r=e.isConnected||e.ownerDocument.documentElement.contains(e);if(te(t)&&"function"==typeof t.behavior)return t.behavior(r?ee(e,t):[]);if(r){var i=function(e){return!1===e?{block:"end",inline:"nearest"}:te(e)?e:{block:"start",inline:"nearest"}}(t);return function(e,t){void 0===t&&(t="auto");var r="scrollBehavior"in document.body.style;e.forEach((function(e){var i=e.el,a=e.top,o=e.left;i.scroll&&r?i.scroll({top:a,left:o,behavior:t}):(i.scrollTop=a,i.scrollLeft=o)}))}(ee(e,i),i.behavior)}}const ie=["success","warning","error","validating"],ae=(e,t)=>{const r=t.replace(/[[.]/g,"_").replace(/\]/g,"");return e?`${e}-${r}`:`${r}`};var oe=Object.defineProperty,le=Object.getOwnPropertySymbols,se=Object.prototype.hasOwnProperty,ne=Object.prototype.propertyIsEnumerable,ue=(e,t,r)=>t in e?oe(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var ce=e(u({name:"Form",props:{model:{type:Object,required:!0},layout:{type:String,default:"horizontal"},size:{type:String},labelColProps:{type:Object,default:()=>({span:5,offset:0})},wrapperColProps:{type:Object,default:()=>({span:19,offset:0})},labelColStyle:Object,wrapperColStyle:Object,labelAlign:{type:String,default:"right"},disabled:{type:Boolean,default:void 0},rules:{type:Object},autoLabelWidth:{type:Boolean,default:!1},id:{type:String},scrollToFirstError:{type:Boolean,default:!1}},emits:{submit:(e,t)=>!0,submitSuccess:(e,t)=>!0,submitFailed:(e,t)=>!0},setup(e,{emit:o}){const l=t("form"),s=c(),{id:n,model:u,layout:b,disabled:g,labelAlign:v,labelColProps:m,wrapperColProps:y,labelColStyle:j,wrapperColStyle:O,size:w,rules:C}=d(e),{mergedSize:x}=I(w),S=p((()=>"horizontal"===e.layout&&e.autoLabelWidth)),M=[],E=h({}),L=p((()=>Math.max(...Object.values(E)))),k=(t,r)=>{const i=(s.value||document.body).querySelector(`#${ae(e.id,t)}`);i&&re(i,((e,t)=>{for(var r in t||(t={}))se.call(t,r)&&ue(e,r,t[r]);if(le)for(var r of le(t))ne.call(t,r)&&ue(e,r,t[r]);return e})({behavior:"smooth",block:"nearest",scrollMode:"if-needed"},r))},F=t=>{const r=a(e.scrollToFirstError)?void 0:e.scrollToFirstError;k(t,r)},V=(t,a)=>{const o=[];for(const e of M)(i(t)&&t.includes(e.field)||t===e.field)&&o.push(e.validate());return Promise.all(o).then((t=>{const i={};let o=!1;return t.forEach((e=>{e&&(o=!0,i[e.field]=e)})),o&&e.scrollToFirstError&&F(Object.keys(i)[0]),r(a)&&a(o?i:void 0),o?i:void 0}))};f(A,h({id:n,layout:b,disabled:g,labelAlign:v,labelColProps:m,wrapperColProps:y,labelColStyle:j,wrapperColStyle:O,model:u,size:x,rules:C,fields:M,touchedFields:[],addField:e=>{e&&e.field&&M.push(e)},removeField:e=>{e&&e.field&&M.splice(M.indexOf(e),1)},validateField:V,setLabelWidth:(e,t)=>{t&&E[t]!==e&&(E[t]=e)},removeLabelWidth:e=>{e&&delete E[e]},maxLabelWidth:L,autoLabelWidth:S}));return{cls:p((()=>[l,`${l}-layout-${e.layout}`,`${l}-size-${x.value}`,{[`${l}-auto-label-width`]:e.autoLabelWidth}])),formRef:s,handleSubmit:t=>{const r=[];M.forEach((e=>{r.push(e.validate())})),Promise.all(r).then((r=>{const i={};let a=!1;r.forEach((e=>{e&&(a=!0,i[e.field]=e)})),a?(e.scrollToFirstError&&F(Object.keys(i)[0]),o("submitFailed",{values:u.value,errors:i},t)):o("submitSuccess",u.value,t),o("submit",{values:u.value,errors:a?i:void 0},t)}))},innerValidate:t=>{const i=[];return M.forEach((e=>{i.push(e.validate())})),Promise.all(i).then((i=>{const a={};let o=!1;return i.forEach((e=>{e&&(o=!0,a[e.field]=e)})),o&&e.scrollToFirstError&&F(Object.keys(a)[0]),r(t)&&t(o?a:void 0),o?a:void 0}))},innerValidateField:V,innerResetFields:e=>{const t=e?[].concat(e):[];M.forEach((e=>{(0===t.length||t.includes(e.field))&&e.resetField()}))},innerClearValidate:e=>{const t=e?[].concat(e):[];M.forEach((e=>{(0===t.length||t.includes(e.field))&&e.clearValidate()}))},innerSetFields:e=>{M.forEach((t=>{e[t.field]&&t.setField(e[t.field])}))},innerScrollToField:k}},methods:{validate(e){return this.innerValidate(e)},validateField(e,t){return this.innerValidateField(e,t)},resetFields(e){return this.innerResetFields(e)},clearValidate(e){return this.innerClearValidate(e)},setFields(e){return this.innerSetFields(e)},scrollToField(e){return this.innerScrollToField(e)}}}),[["render",function(e,t,r,i,a,o){return b(),g("form",{ref:"formRef",class:m(e.cls),onSubmit:t[0]||(t[0]=y(((...t)=>e.handleSubmit&&e.handleSubmit(...t)),["prevent"]))},[v(e.$slots,"default")],34)}]]),de=Object.prototype.toString;function pe(e){return"[object Array]"===de.call(e)}function he(e){return"[object Object]"===de.call(e)}function fe(e){return"[object String]"===de.call(e)}function be(e){return"[object Function]"===de.call(e)}function ge(e){return null==e||""===e}function ve(e){return pe(e)&&!e.length}var me=function(e,t){if("object"!=typeof e||"object"!=typeof t)return e===t;if(be(e)&&be(t))return e===t||e.toString()===t.toString();if(Object.keys(e).length!==Object.keys(t).length)return!1;for(var r in e){if(!me(e[r],t[r]))return!1}return!0},ye=function(e,t){var r=Object.assign({},e);return Object.keys(t||{}).forEach((function(e){var i=r[e],a=null==t?void 0:t[e];r[e]=he(i)?Object.assign(Object.assign({},i),a):a||i})),r},je="#{field} is not a #{type} type",Oe={required:"#{field} is required",type:{ip:je,email:je,url:je,string:je,number:je,array:je,object:je,boolean:je},number:{min:"`#{value}` is not greater than `#{min}`",max:"`#{value}` is not less than `#{max}`",equal:"`#{value}` is not equal to `#{equal}`",range:"`#{value}` is not in range `#{min} ~ #{max}`",positive:"`#{value}` is not a positive number",negative:"`#{value}` is not a negative number"},string:{maxLength:"#{field} cannot be longer than #{maxLength} characters",minLength:"#{field} must be at least #{minLength} characters",length:"#{field} must be exactly #{length} characters",match:"`#{value}` does not match pattern #{pattern}",uppercase:"`#{value}` must be all uppercase",lowercase:"`#{value}` must be all lowercased"},array:{length:"#{field} must be exactly #{length} in length",minLength:"#{field} cannot be less than #{minLength} in length",maxLength:"#{field} cannot be greater than #{maxLength} in length",includes:"#{field} is not includes #{includes}",deepEqual:"#{field} is not deep equal with #{deepEqual}",empty:"#{field} is not an empty array"},object:{deepEqual:"#{field} is not deep equal to expected value",hasKeys:"#{field} does not contain required fields",empty:"#{field} is not an empty object"},boolean:{true:"Expect true but got `#{value}`",false:"Expect false but got `#{value}`"}},we=function(e,t){var r=this;this.getValidateMsg=function(e,t){void 0===t&&(t={});var i=Object.assign(Object.assign({},t),{value:r.obj,field:r.field,type:r.type}),a=function(e,t){for(var r=t.split("."),i=e,a=0;a<r.length;a++)if(void 0===(i=i&&i[r[a]]))return i;return i}(r.validateMessages,e);return be(a)?a(i):fe(a)?a.replace(/\#\{.+?\}/g,(function(e){var t=e.slice(2,-1);if(t in i){if(he(i[t])||pe(i[t]))try{return JSON.stringify(i[t])}catch(r){return i[t]}return String(i[t])}return e})):a},he(t)&&fe(e)&&t.trim?this.obj=e.trim():he(t)&&t.ignoreEmptyString&&""===e?this.obj=void 0:this.obj=e,this.message=t.message,this.type=t.type,this.error=null,this.field=t.field||t.type,this.validateMessages=ye(Oe,t.validateMessages)},Ce={not:{configurable:!0},isRequired:{configurable:!0},end:{configurable:!0}};Ce.not.get=function(){return this._not=!this._not,this},Ce.isRequired.get=function(){if(ge(this.obj)||ve(this.obj)){var e=this.getValidateMsg("required");this.error={value:this.obj,type:this.type,requiredError:!0,message:this.message||(he(e)?e:(this._not?"[NOT MODE]:":"")+e)}}return this},Ce.end.get=function(){return this.error},we.prototype.addError=function(e){!this.error&&e&&(this.error={value:this.obj,type:this.type,message:this.message||(he(e)?e:(this._not?"[NOT MODE]:":"")+e)})},we.prototype.validate=function(e,t){return(this._not?e:!e)&&this.addError(t),this},we.prototype.collect=function(e){e&&e(this.error)},Object.defineProperties(we.prototype,Ce);var xe=function(e){function t(t,r){e.call(this,t,Object.assign(Object.assign({},r),{type:"string"})),this.validate(!r||!r.strict||fe(this.obj),this.getValidateMsg("type.string"))}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var r={uppercase:{configurable:!0},lowercase:{configurable:!0}};return t.prototype.maxLength=function(e){return this.obj?this.validate(this.obj.length<=e,this.getValidateMsg("string.maxLength",{maxLength:e})):this},t.prototype.minLength=function(e){return this.obj?this.validate(this.obj.length>=e,this.getValidateMsg("string.minLength",{minLength:e})):this},t.prototype.length=function(e){return this.obj?this.validate(this.obj.length===e,this.getValidateMsg("string.length",{length:e})):this},t.prototype.match=function(e){var t=e instanceof RegExp;return t&&(e.lastIndex=0),this.validate(void 0===this.obj||t&&e.test(this.obj),this.getValidateMsg("string.match",{pattern:e}))},r.uppercase.get=function(){return this.obj?this.validate(this.obj.toUpperCase()===this.obj,this.getValidateMsg("string.uppercase")):this},r.lowercase.get=function(){return this.obj?this.validate(this.obj.toLowerCase()===this.obj,this.getValidateMsg("string.lowercase")):this},Object.defineProperties(t.prototype,r),t}(we),Se=function(e){function t(t,r){e.call(this,t,Object.assign(Object.assign({},r),{type:"number"})),this.validate(!r||!r.strict||function(e){return"[object Number]"===de.call(e)&&e==e}(this.obj),this.getValidateMsg("type.number"))}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var r={positive:{configurable:!0},negative:{configurable:!0}};return t.prototype.min=function(e){return ge(this.obj)?this:this.validate(this.obj>=e,this.getValidateMsg("number.min",{min:e}))},t.prototype.max=function(e){return ge(this.obj)?this:this.validate(this.obj<=e,this.getValidateMsg("number.max",{max:e}))},t.prototype.equal=function(e){return ge(this.obj)?this:this.validate(this.obj===e,this.getValidateMsg("number.equal",{equal:e}))},t.prototype.range=function(e,t){return ge(this.obj)?this:this.validate(this.obj>=e&&this.obj<=t,this.getValidateMsg("number.range",{min:e,max:t}))},r.positive.get=function(){return ge(this.obj)?this:this.validate(this.obj>0,this.getValidateMsg("number.positive"))},r.negative.get=function(){return ge(this.obj)?this:this.validate(this.obj<0,this.getValidateMsg("number.negative"))},Object.defineProperties(t.prototype,r),t}(we),Me=function(e){function t(t,r){e.call(this,t,Object.assign(Object.assign({},r),{type:"array"})),this.validate(!r||!r.strict||pe(this.obj),this.getValidateMsg("type.array",{value:this.obj,type:this.type}))}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var r={empty:{configurable:!0}};return t.prototype.length=function(e){return this.obj?this.validate(this.obj.length===e,this.getValidateMsg("array.length",{value:this.obj,length:e})):this},t.prototype.minLength=function(e){return this.obj?this.validate(this.obj.length>=e,this.getValidateMsg("array.minLength",{value:this.obj,minLength:e})):this},t.prototype.maxLength=function(e){return this.obj?this.validate(this.obj.length<=e,this.getValidateMsg("array.maxLength",{value:this.obj,maxLength:e})):this},t.prototype.includes=function(e){var t=this;return this.obj?this.validate(e.every((function(e){return-1!==t.obj.indexOf(e)})),this.getValidateMsg("array.includes",{value:this.obj,includes:e})):this},t.prototype.deepEqual=function(e){return this.obj?this.validate(me(this.obj,e),this.getValidateMsg("array.deepEqual",{value:this.obj,deepEqual:e})):this},r.empty.get=function(){return this.validate(ve(this.obj),this.getValidateMsg("array.empty",{value:this.obj}))},Object.defineProperties(t.prototype,r),t}(we),Ee=function(e){function t(t,r){e.call(this,t,Object.assign(Object.assign({},r),{type:"object"})),this.validate(!r||!r.strict||he(this.obj),this.getValidateMsg("type.object"))}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var r={empty:{configurable:!0}};return t.prototype.deepEqual=function(e){return this.obj?this.validate(me(this.obj,e),this.getValidateMsg("object.deepEqual",{deepEqual:e})):this},t.prototype.hasKeys=function(e){var t=this;return this.obj?this.validate(e.every((function(e){return t.obj[e]})),this.getValidateMsg("object.hasKeys",{keys:e})):this},r.empty.get=function(){return this.validate(he(e=this.obj)&&0===Object.keys(e).length,this.getValidateMsg("object.empty"));var e},Object.defineProperties(t.prototype,r),t}(we),Le=function(e){function t(t,r){e.call(this,t,Object.assign(Object.assign({},r),{type:"boolean"})),this.validate(!r||!r.strict||function(e){return"[object Boolean]"===de.call(e)}(this.obj),this.getValidateMsg("type.boolean"))}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var r={true:{configurable:!0},false:{configurable:!0}};return r.true.get=function(){return this.validate(!0===this.obj,this.getValidateMsg("boolean.true"))},r.false.get=function(){return this.validate(!1===this.obj,this.getValidateMsg("boolean.false"))},Object.defineProperties(t.prototype,r),t}(we),ke=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,Fe=new RegExp("^(?!mailto:)(?:(?:http|https|ftp)://)(?:\\S+(?::\\S*)?@)?(?:(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[0-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]+-?)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]+-?)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))|localhost)(?::\\d{2,5})?(?:(/|\\?|#)[^\\s]*)?$","i"),Ve=/^(2(5[0-5]{1}|[0-4]\d{1})|[0-1]?\d{1,2})(\.(2(5[0-5]{1}|[0-4]\d{1})|[0-1]?\d{1,2})){3}$/,_e=function(e){function t(t,r){e.call(this,t,Object.assign(Object.assign({},r),{type:"type"}))}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var r={email:{configurable:!0},url:{configurable:!0},ip:{configurable:!0}};return r.email.get=function(){return this.type="email",this.validate(void 0===this.obj||ke.test(this.obj),this.getValidateMsg("type.email"))},r.url.get=function(){return this.type="url",this.validate(void 0===this.obj||Fe.test(this.obj),this.getValidateMsg("type.url"))},r.ip.get=function(){return this.type="ip",this.validate(void 0===this.obj||Ve.test(this.obj),this.getValidateMsg("type.ip"))},Object.defineProperties(t.prototype,r),t}(we),qe=function(e){function t(t,r){e.call(this,t,Object.assign(Object.assign({},r),{type:"custom"}))}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var r={validate:{configurable:!0}};return r.validate.get=function(){var e=this;return function(t,r){var i;if(t)return(i=t(e.obj,e.addError.bind(e)))&&i.then?(r&&i.then((function(){r&&r(e.error)}),(function(e){console.error(e)})),[i,e]):(r&&r(e.error),e.error)}},Object.defineProperties(t.prototype,r),t}(we),$e=function(e,t){return new Pe(e,Object.assign({field:"value"},t))};$e.globalConfig={},$e.setGlobalConfig=function(e){$e.globalConfig=e||{}};var Pe=function(e,t){var r=$e.globalConfig,i=Object.assign(Object.assign(Object.assign({},r),t),{validateMessages:ye(r.validateMessages,t.validateMessages)});this.string=new xe(e,i),this.number=new Se(e,i),this.array=new Me(e,i),this.object=new Ee(e,i),this.boolean=new Le(e,i),this.type=new _e(e,i),this.custom=new qe(e,i)},We=function(e,t){void 0===t&&(t={}),this.schema=e,this.options=t};We.prototype.messages=function(e){this.options=Object.assign(Object.assign({},this.options),{validateMessages:ye(this.options.validateMessages,e)})},We.prototype.validate=function(e,t){var r=this;if(he(e)){var i=[],a=null;this.schema&&Object.keys(this.schema).forEach((function(t){if(pe(r.schema[t]))for(var l=function(l){var s=r.schema[t][l],n=s.type,u=s.message;if(!n&&!s.validator)throw"You must specify a type to field "+t+"!";var c=Object.assign(Object.assign({},r.options),{message:u,field:t});"ignoreEmptyString"in s&&(c.ignoreEmptyString=s.ignoreEmptyString),"strict"in s&&(c.strict=s.strict);var d=new Pe(e[t],c),p=d.type[n]||null;if(!p){if(s.validator)return p=d.custom.validate(s.validator),void("[object Array]"===Object.prototype.toString.call(p)&&p[0].then?i.push({function:p[0],_this:p[1],key:t}):p&&o(t,p));p=d[n]}if(Object.keys(s).forEach((function(e){s.required&&(p=p.isRequired),"message"!==e&&p[e]&&s[e]&&"object"==typeof p[e]&&(p=p[e]),p[e]&&void 0!==s[e]&&"function"==typeof p[e]&&(p=p[e](s[e]))})),p.collect((function(e){e&&o(t,e)})),a)return"break"},s=0;s<r.schema[t].length;s++){if("break"===l(s))break}})),i.length>0?Promise.all(i.map((function(e){return e.function}))).then((function(){i.forEach((function(e){e._this.error&&o(e.key,e._this.error)})),t&&t(a)})):t&&t(a)}function o(e,t){a||(a={}),a[e]&&!t.requiredError||(a[e]=t)}};const Be=u({name:"FormItemLabel",components:{ResizeObserver:H,Tooltip:D,IconQuestionCircle:o},props:{required:{type:Boolean,default:!1},showColon:{type:Boolean,default:!1},component:{type:String,default:"label"},asteriskPosition:{type:String,default:"start"},tooltip:{type:String},attrs:Object},setup(){const e=t("form-item-label"),r=j(A,void 0),i=_(),a=c();return O((()=>{a.value&&l(a.value.offsetWidth)&&(null==r||r.setLabelWidth(a.value.offsetWidth,null==i?void 0:i.uid))})),w((()=>{null==r||r.removeLabelWidth(null==i?void 0:i.uid)})),{prefixCls:e,labelRef:a,handleResize:()=>{a.value&&l(a.value.offsetWidth)&&(null==r||r.setLabelWidth(a.value.offsetWidth,null==i?void 0:i.uid))}}}}),Re=[q("svg",{fill:"currentColor",viewBox:"0 0 1024 1024",width:"1em",height:"1em"},[q("path",{d:"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z"})],-1)],ze=[q("svg",{fill:"currentColor",viewBox:"0 0 1024 1024",width:"1em",height:"1em"},[q("path",{d:"M583.338667 17.066667c18.773333 0 34.133333 15.36 34.133333 34.133333v349.013333l313.344-101.888a34.133333 34.133333 0 0 1 43.008 22.016l42.154667 129.706667a34.133333 34.133333 0 0 1-21.845334 43.178667l-315.733333 102.4 208.896 287.744a34.133333 34.133333 0 0 1-7.509333 47.786666l-110.421334 80.213334a34.133333 34.133333 0 0 1-47.786666-7.509334L505.685333 706.218667 288.426667 1005.226667a34.133333 34.133333 0 0 1-47.786667 7.509333l-110.421333-80.213333a34.133333 34.133333 0 0 1-7.509334-47.786667l214.186667-295.253333L29.013333 489.813333a34.133333 34.133333 0 0 1-22.016-43.008l42.154667-129.877333a34.133333 34.133333 0 0 1 43.008-22.016l320.512 104.106667L412.672 51.2c0-18.773333 15.36-34.133333 34.133333-34.133333h136.533334z"})],-1)];var Ae=e(Be,[["render",function(e,t,r,i,a,o){const l=C("icon-question-circle"),s=C("Tooltip"),n=C("ResizeObserver");return b(),x(n,{onResize:e.handleResize},{default:S((()=>[(b(),x(M(e.component),E({ref:"labelRef",class:e.prefixCls},e.attrs),{default:S((()=>[e.required&&"start"===e.asteriskPosition?(b(),g("strong",{key:0,class:m(`${e.prefixCls}-required-symbol`)},Re,2)):L("v-if",!0),v(e.$slots,"default"),e.tooltip?(b(),x(s,{key:1,content:e.tooltip},{default:S((()=>[k(l,{class:m(`${e.prefixCls}-tooltip`)},null,8,["class"])])),_:1},8,["content"])):L("v-if",!0),e.required&&"end"===e.asteriskPosition?(b(),g("strong",{key:2,class:m(`${e.prefixCls}-required-symbol`)},ze,2)):L("v-if",!0),F(" "+V(e.showColon?":":""),1)])),_:3},16,["class"]))])),_:3},8,["onResize"])}]]);var Te=e(u({name:"FormItemMessage",props:{error:{type:Array,default:()=>[]},help:String},setup:()=>({prefixCls:t("form-item-message")})}),[["render",function(e,t,r,i,a,o){return e.error.length>0?(b(!0),g(W,{key:0},$(e.error,(t=>(b(),x(P,{key:t,name:"form-blink",appear:""},{default:S((()=>[q("div",{role:"alert",class:m([e.prefixCls])},V(t),3)])),_:2},1024)))),128)):e.help||e.$slots.help?(b(),x(P,{key:1,name:"form-blink",appear:""},{default:S((()=>[q("div",{class:m([e.prefixCls,`${e.prefixCls}-help`])},[v(e.$slots,"help",{},(()=>[F(V(e.help),1)]))],2)])),_:3})):L("v-if",!0)}]]),Ie=Object.defineProperty,He=Object.getOwnPropertySymbols,De=Object.prototype.hasOwnProperty,Ne=Object.prototype.propertyIsEnumerable,Ye=(e,t,r)=>t in e?Ie(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Ke=(e,t)=>{for(var r in t||(t={}))De.call(t,r)&&Ye(e,r,t[r]);if(He)for(var r of He(t))Ne.call(t,r)&&Ye(e,r,t[r]);return e};var Xe=e(u({name:"FormItem",components:{ArcoRow:X,ArcoCol:J,FormItemLabel:Ae,FormItemMessage:Te},props:{field:{type:String,default:""},label:String,tooltip:{type:String},showColon:{type:Boolean,default:!1},noStyle:{type:Boolean,default:!1},disabled:{type:Boolean,default:void 0},help:String,extra:String,required:{type:Boolean,default:!1},asteriskPosition:{type:String,default:"start"},rules:{type:[Object,Array]},validateStatus:{type:String},validateTrigger:{type:[String,Array],default:"change"},labelColProps:Object,wrapperColProps:Object,hideLabel:{type:Boolean,default:!1},hideAsterisk:{type:Boolean,default:!1},labelColStyle:Object,wrapperColStyle:Object,rowProps:Object,rowClass:[String,Array,Object],contentClass:[String,Array,Object],contentFlex:{type:Boolean,default:!0},mergeProps:{type:[Boolean,Function],default:!0},labelColFlex:{type:[Number,String]},feedback:{type:Boolean,default:!1},labelComponent:{type:String,default:"label"},labelAttrs:Object},setup(e){const r=t("form-item"),{field:i}=d(e),a=j(A,{}),{autoLabelWidth:o,layout:l}=d(a),{i18nMessage:s}=N(),n=p((()=>{var t;const r=Ke({},null!=(t=e.labelColProps)?t:a.labelColProps);return e.labelColFlex?r.flex=e.labelColFlex:a.autoLabelWidth&&(r.flex=`${a.maxLabelWidth}px`),r})),u=p((()=>{var t;const r=Ke({},null!=(t=e.wrapperColProps)?t:a.wrapperColProps);return i.value&&(r.id=ae(a.id,i.value)),(e.labelColFlex||a.autoLabelWidth)&&(r.flex="auto"),r})),b=p((()=>{var t;return null!=(t=e.labelColStyle)?t:a.labelColStyle})),g=p((()=>{var t;return null!=(t=e.wrapperColStyle)?t:a.wrapperColStyle})),v=Y(a.model,e.field),m=h({}),y=h({}),C=p((()=>(e=>{let t="";for(const r of Object.keys(e)){const i=e[r];i&&(!t||ie.indexOf(i)>ie.indexOf(t))&&(t=e[r])}return t})(m))),x=p((()=>(e=>{const t=[];for(const r of Object.keys(e)){const i=e[r];i&&t.push(i)}return t})(y))),S=c(!1),M=p((()=>Y(a.model,e.field))),E=p((()=>{var t;return Boolean(null!=(t=e.disabled)?t:null==a?void 0:a.disabled)})),L=p((()=>{var t;return null!=(t=e.validateStatus)?t:C.value})),k=p((()=>"error"===L.value)),F=p((()=>{var t,r,i;const o=[].concat(null!=(i=null!=(r=e.rules)?r:null==(t=null==a?void 0:a.rules)?void 0:t[e.field])?i:[]),l=o.some((e=>e.required));return e.required&&!l?[{required:!0}].concat(o):o})),V=p((()=>F.value.some((e=>e.required)))),_=e.noStyle?j(T,void 0):void 0,q=(t,{status:r,message:i})=>{m[t]=r,y[t]=i,e.noStyle&&(null==_||_.updateValidateState(t,{status:r,message:i}))},$=p((()=>e.feedback&&L.value?L.value:void 0)),P=()=>{var t;if(S.value)return Promise.resolve();const r=F.value;if(!i.value||0===r.length)return C.value&&I(),Promise.resolve();const a=i.value,o=M.value;q(a,{status:"",message:""});const l=new We({[a]:r.map((e=>{var t=((e,t)=>{var r={};for(var i in e)De.call(e,i)&&t.indexOf(i)<0&&(r[i]=e[i]);if(null!=e&&He)for(var i of He(e))t.indexOf(i)<0&&Ne.call(e,i)&&(r[i]=e[i]);return r})(e,[]);return t.type||t.validator||(t.type="string"),t}))},{ignoreEmptyString:!0,validateMessages:null==(t=s.value.form)?void 0:t.validateMessages});return new Promise((t=>{l.validate({[a]:o},(r=>{var o;const l=Boolean(null==r?void 0:r[a]);q(a,{status:l?"error":"",message:null!=(o=null==r?void 0:r[a].message)?o:""});const s=l?{label:e.label,field:i.value,value:r[a].value,type:r[a].type,isRequiredError:Boolean(r[a].requiredError),message:r[a].message}:void 0;t(s)}))}))},W=p((()=>[].concat(e.validateTrigger))),z=p((()=>W.value.reduce(((e,t)=>{switch(t){case"change":return e.onChange=()=>{P()},e;case"input":return e.onInput=()=>{B((()=>{P()}))},e;case"focus":return e.onFocus=()=>{P()},e;case"blur":return e.onBlur=()=>{P()},e;default:return e}}),{})));f(T,h({eventHandlers:z,size:a&&R(a,"size"),disabled:E,error:k,feedback:$,updateValidateState:q}));const I=()=>{i.value&&q(i.value,{status:"",message:""})},H=h({field:i,disabled:E,error:k,validate:P,clearValidate:I,resetField:()=>{I(),S.value=!0,(null==a?void 0:a.model)&&i.value&&K(a.model,i.value,v),B((()=>{S.value=!1}))},setField:e=>{var t,r;i.value&&(S.value=!0,"value"in e&&(null==a?void 0:a.model)&&i.value&&K(a.model,i.value,e.value),(e.status||e.message)&&q(i.value,{status:null!=(t=e.status)?t:"",message:null!=(r=e.message)?r:""}),B((()=>{S.value=!1})))}});O((()=>{var e;H.field&&(null==(e=a.addField)||e.call(a,H))})),w((()=>{var e;H.field&&(null==(e=a.removeField)||e.call(a,H))}));const D=p((()=>[r,`${r}-layout-${a.layout}`,{[`${r}-error`]:k.value,[`${r}-status-${L.value}`]:Boolean(L.value)},e.rowClass])),X=p((()=>[`${r}-label-col`,{[`${r}-label-col-left`]:"left"===a.labelAlign,[`${r}-label-col-flex`]:a.autoLabelWidth||e.labelColFlex}])),J=p((()=>[`${r}-wrapper-col`,{[`${r}-wrapper-col-flex`]:!u.value}]));return{prefixCls:r,cls:D,isRequired:V,isError:k,finalMessage:x,mergedLabelCol:n,mergedWrapperCol:u,labelColCls:X,autoLabelWidth:o,layout:l,mergedLabelStyle:b,wrapperColCls:J,mergedWrapperStyle:g}}}),[["render",function(e,t,r,i,a,o){var l;const s=C("FormItemLabel"),n=C("ArcoCol"),u=C("FormItemMessage"),c=C("ArcoRow");return e.noStyle?v(e.$slots,"default",{key:0}):(b(),x(c,E({key:1,class:[e.cls,{[`${e.prefixCls}-has-help`]:Boolean(null!=(l=e.$slots.help)?l:e.help)}],wrap:!(e.labelColFlex||e.autoLabelWidth),div:"horizontal"!==e.layout||e.hideLabel},e.rowProps),{default:S((()=>[e.hideLabel?L("v-if",!0):(b(),x(n,E({key:0,class:e.labelColCls,style:e.mergedLabelStyle},e.mergedLabelCol),{default:S((()=>[k(s,{required:!e.hideAsterisk&&e.isRequired,"show-colon":e.showColon,"asterisk-position":e.asteriskPosition,component:e.labelComponent,attrs:e.labelAttrs,tooltip:e.tooltip},{default:S((()=>[e.$slots.label||e.label?v(e.$slots,"label",{key:0},(()=>[F(V(e.label),1)])):L("v-if",!0)])),_:3},8,["required","show-colon","asterisk-position","component","attrs","tooltip"])])),_:3},16,["class","style"])),k(n,E({class:e.wrapperColCls,style:e.mergedWrapperStyle},e.mergedWrapperCol),{default:S((()=>[q("div",{class:m(`${e.prefixCls}-content-wrapper`)},[q("div",{class:m([`${e.prefixCls}-content`,{[`${e.prefixCls}-content-flex`]:e.contentFlex},e.contentClass])},[v(e.$slots,"default")],2)],2),e.isError||e.$slots.help||e.help?(b(),x(u,{key:0,error:e.finalMessage,help:e.help},z({_:2},[e.$slots.help?{name:"help",fn:S((()=>[v(e.$slots,"help")]))}:void 0]),1032,["error","help"])):L("v-if",!0),e.$slots.extra||e.extra?(b(),g("div",{key:1,class:m(`${e.prefixCls}-extra`)},[v(e.$slots,"extra",{},(()=>[F(V(e.extra),1)]))],2)):L("v-if",!0)])),_:3},16,["class","style"])])),_:3},16,["class","wrap","div"]))}]]);const Je=Object.assign(ce,{Item:Xe,install:(e,t)=>{s(e,t);const r=n(t);e.component(r+ce.name,ce),e.component(r+Xe.name,Xe)}});export{Xe as F,Je as a,re as s};

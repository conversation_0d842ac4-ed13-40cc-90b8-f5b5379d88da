import{_ as t,g as e,s as o,f as s}from"./index-D-8JbLQk.js";import{T as p}from"./index-DDFSMqsG.js";import{d as n,r as i,c as a,f as r,j as l,y as u,z as c,q as g,l as f,m as b,I as d,J as y}from"./vue-D-10XvVk.js";var C=t(n({name:"Popover",components:{Trigger:p},props:{popupVisible:{type:Boolean,default:void 0},defaultPopupVisible:{type:Boolean,default:!1},title:String,content:String,trigger:{type:[String,Array],default:"hover"},position:{type:String,default:"top"},contentClass:{type:[String,Array,Object]},contentStyle:{type:Object},arrowClass:{type:[String,Array,Object]},arrowStyle:{type:Object},popupContainer:{type:[String,Object]}},emits:{"update:popupVisible":t=>!0,popupVisibleChange:t=>!0},setup(t,{emit:o}){const s=e("popover"),p=i(t.defaultPopupVisible),n=a((()=>{var e;return null!=(e=t.popupVisible)?e:p.value})),r=a((()=>[`${s}-popup-content`,t.contentClass])),l=a((()=>[`${s}-popup-arrow`,t.arrowClass]));return{prefixCls:s,computedPopupVisible:n,contentCls:r,arrowCls:l,handlePopupVisibleChange:t=>{p.value=t,o("update:popupVisible",t),o("popupVisibleChange",t)}}}}),[["render",function(t,e,o,s,p,n){const i=r("trigger");return l(),u(i,{class:f(t.prefixCls),trigger:t.trigger,position:t.position,"popup-visible":t.computedPopupVisible,"popup-offset":10,"content-class":t.contentCls,"content-style":t.contentStyle,"arrow-class":t.arrowCls,"arrow-style":t.arrowStyle,"show-arrow":"","popup-container":t.popupContainer,"animation-name":"zoom-in-fade-out","auto-fit-transform-origin":"",onPopupVisibleChange:t.handlePopupVisibleChange},{content:c((()=>[g("div",{class:f(`${t.prefixCls}-title`)},[b(t.$slots,"title",{},(()=>[d(y(t.title),1)]))],2),g("div",{class:f(`${t.prefixCls}-content`)},[b(t.$slots,"content",{},(()=>[d(y(t.content),1)]))],2)])),default:c((()=>[b(t.$slots,"default")])),_:3},8,["class","trigger","position","popup-visible","content-class","content-style","arrow-class","arrow-style","popup-container","onPopupVisibleChange"])}]]);const m=Object.assign(C,{install:(t,e)=>{o(t,e);const p=s(e);t.component(p+C.name,C)}});export{m as P};

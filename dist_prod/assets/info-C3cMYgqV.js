import"./index-DOhy6BH_.js";import"./index-BEo1tUsK.js";import{F as e,a as o}from"./index-DVDXfQhn.js";import{I as i}from"./index-DDFSMqsG.js";import{_ as r}from"./index-HLwNT5T9.js";import{S as s}from"./index-Cuq5XRs0.js";import"./index-DD6vSYIM.js";import"./index-DQjhgQFu.js";import{u as l}from"./useCommon-BuUbRw8e.js";import{u as a}from"./index-DGnWXJcu.js";import{d,K as t,o as p,j as m,k as n,A as u,z as j,u as f,y as c,p as x,q as h}from"./vue-D-10XvVk.js";import"./index-D-8JbLQk.js";import"./pick-Ccd8Sfcm.js";import"./index-DGtjsHgS.js";import"./ResizeObserver.es-CzGuHLZU.js";import"./index-CuYx5vtf.js";import"./render-function-CAXdZVZM.js";import"./index-CdWxsKz_.js";import"./apiUpload-DpATemHF.js";import"./_plugin-vue_export-helper-BCo6x5W8.js";import"./resize-observer-Dtogi-DJ.js";import"./apiCommon-DcubqwY_.js";import"./index-DdMaxvYa.js";import"./index-DfEXMvnc.js";import"./use-children-components-v8i8lsOx.js";import"./index-DmW4RN1x.js";/* empty css              */import"./index-CUtvFEc_.js";import"./use-index-D_ozg7PK.js";import"./index-CHOaln3D.js";/* empty css              */import"./useLoading-D5mh7tTu.js";import"./usePagination-Dd_EW2BO.js";import"./dayjs.min-Daes5FZc.js";import"./index-O7pr3qsq.js";const b={class:"overflow-y-scroll no-scrollbar"},y=d({__name:"info",props:{type:{},id:{}},setup(d,{expose:y}){const _=d,{allStoreCategoryOptions:g,initAllStoreCategoryOptions:V}=l(),{form:$,detail:v}=a(),I=t("formRef");return p((()=>{V(),"edit"===_.type&&_.id?v(_.id):"add"===_.type&&_.id&&($.parentId=_.id)})),y({formRef:I,form:$}),(l,a)=>{const d=s,t=e,p=r,y=i,_=o;return m(),n("div",b,[u(_,{ref_key:"formRef",ref:I,model:f($),"auto-label-width":""},{default:j((()=>[f($).parentId?(m(),c(t,{key:0,disabled:!!f($).parentId,label:"所属分类",field:"parentId",rules:[{required:!0,message:`${l.$selectPlaceholder}所属分类`}]},{default:j((()=>[u(d,{modelValue:f($).parentId,"onUpdate:modelValue":a[0]||(a[0]=e=>f($).parentId=e),options:f(g),placeholder:l.$selectPlaceholder},null,8,["modelValue","options","placeholder"])])),_:1},8,["disabled","rules"])):x("",!0),u(t,{"show-colon":"",label:"分类图标",field:"icon",rules:[{required:!0,message:`${l.$uploadPlaceholder}分类图标`}]},{extra:j((()=>a[3]||(a[3]=[h("div",null,"请上传200px*200px的.png图片",-1)]))),default:j((()=>[u(p,{modelValue:f($).icon,"onUpdate:modelValue":a[1]||(a[1]=e=>f($).icon=e),accept:"image/*",title:"上传分类图标"},null,8,["modelValue"])])),_:1},8,["rules"]),u(t,{label:"分类名称",field:"name",rules:[{required:!0,message:`${l.$inputPlaceholder}分类名称`}]},{default:j((()=>[u(y,{modelValue:f($).name,"onUpdate:modelValue":a[2]||(a[2]=e=>f($).name=e),placeholder:l.$inputPlaceholder},null,8,["modelValue","placeholder"])])),_:1},8,["rules"])])),_:1},8,["model"])])}}});export{y as default};

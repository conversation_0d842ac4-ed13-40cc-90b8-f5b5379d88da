import"./index-DOhy6BH_.js";import{S as s,B as i}from"./index-DGtjsHgS.js";import{d as a,k as t,A as e,z as l,j as o,q as r,u as n,I as d}from"./vue-D-10XvVk.js";import{_ as p}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-D-8JbLQk.js";const u={class:"not-permission-container"},_=["src"],c={class:"bullshit"},m=p(a({__name:"401",setup(a){const p=()=>{location.href="/"};return(a,m)=>{const f=i,v=s;return o(),t("div",u,[e(v,{size:30,fill:""},{default:l((()=>[r("img",{width:400,src:n("/assets/401-C5CKrwYm.svg")},null,8,_),r("div",c,[m[1]||(m[1]=r("div",{class:"bullshit__oops"},"401错误！",-1)),m[2]||(m[2]=r("div",{class:"bullshit__headline"},"您没有访问权限！",-1)),m[3]||(m[3]=r("div",{class:"bullshit__info"},"对不起，您没有访问权限，请不要进行非法操作！",-1)),e(f,{type:"primary",onClick:p},{default:l((()=>m[0]||(m[0]=[d("返回首页")]))),_:1})])])),_:1})])}}}),[["__scopeId","data-v-6936e7d5"]]);export{m as default};

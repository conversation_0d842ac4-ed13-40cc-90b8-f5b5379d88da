import"./index-DOhy6BH_.js";import{S as s,B as i}from"./index-DGtjsHgS.js";import{d as t,k as a,A as l,z as e,j as o,q as r,u as n,I as d}from"./vue-D-10XvVk.js";import{_ as u}from"./_plugin-vue_export-helper-BCo6x5W8.js";import"./index-D-8JbLQk.js";const c={class:"not-fount-container"},_=["src"],p={class:"bullshit"},m=u(t({__name:"404",setup(t){const u=()=>{location.href="/"};return(t,m)=>{const f=i,v=s;return o(),a("div",c,[l(v,{size:30,fill:""},{default:e((()=>[r("img",{width:400,src:n("/assets/404-DySliAtV.svg")},null,8,_),r("div",p,[m[1]||(m[1]=r("div",{class:"bullshit__oops"},"404错误！",-1)),m[2]||(m[2]=r("div",{class:"bullshit__headline"},"找不到网页！",-1)),m[3]||(m[3]=r("div",{class:"bullshit__info"},"对不起，您正在寻找的页面不存在。尝试检查URL的错误，然后按浏览器上的刷新按钮或尝试在我们的应用程序中找到其他内容。",-1)),l(f,{type:"primary",onClick:u},{default:e((()=>m[0]||(m[0]=[d("返回首页")]))),_:1})])])),_:1})])}}}),[["__scopeId","data-v-7c229444"]]);export{m as default};

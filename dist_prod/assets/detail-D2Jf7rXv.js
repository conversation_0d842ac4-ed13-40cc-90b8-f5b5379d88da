import"./index-DOhy6BH_.js";import{D as e,a as s}from"./index-DIKBiUsz.js";import{u as i}from"./index-Cioag8eE.js";import{d as r,o,j as t,k as a,A as n,z as m,I as d,J as p,u as l,q as j}from"./vue-D-10XvVk.js";import"./index-D-8JbLQk.js";import"./index-BEo1tUsK.js";import"./pick-Ccd8Sfcm.js";import"./use-index-D_ozg7PK.js";import"./index-DDFSMqsG.js";import"./index-DGtjsHgS.js";import"./ResizeObserver.es-CzGuHLZU.js";import"./index-Cuq5XRs0.js";import"./resize-observer-Dtogi-DJ.js";import"./index-DD6vSYIM.js";import"./index-CdWxsKz_.js";import"./index-DQjhgQFu.js";import"./index-DdMaxvYa.js";import"./index-DfEXMvnc.js";import"./use-children-components-v8i8lsOx.js";import"./index-DmW4RN1x.js";import"./index-DVDXfQhn.js";import"./index-dpn1_5z1.js";import"./dayjs.min-Daes5FZc.js";import"./render-function-CAXdZVZM.js";/* empty css              */import"./useCommon-BuUbRw8e.js";import"./apiCommon-DcubqwY_.js";import"./useLoading-D5mh7tTu.js";import"./usePagination-Dd_EW2BO.js";import"./index-O7pr3qsq.js";const u={class:"max-h-[700px] overflow-y-scroll no-scrollbar"},x=["innerHTML"],c=r({__name:"detail",props:{id:{}},setup(r){const c=r,{form:f,detail:b}=i();return o((()=>{b(c.id)})),(i,r)=>{const o=e,c=s;return t(),a("div",u,[n(c,{bordered:"",size:"large",column:2},{default:m((()=>[n(o,{label:"公告标题"},{default:m((()=>[d(p(l(f).title),1)])),_:1}),n(o,{label:"店铺分类"},{default:m((()=>[d(p(l(f).storeCategoryName)+" - "+p(l(f).storeSubCategoryName),1)])),_:1}),n(o,{label:"店铺名称"},{default:m((()=>[d(p(l(f).storeName),1)])),_:1}),n(o,{label:"创建人"},{default:m((()=>[d(p(l(f).createBy),1)])),_:1}),n(o,{label:"创建时间",span:2},{default:m((()=>[d(p(l(f).createTime),1)])),_:1}),n(o,{label:"公告内容",span:2},{default:m((()=>[j("div",{innerHTML:l(f).content},null,8,x)])),_:1})])),_:1})])}}});export{c as default};

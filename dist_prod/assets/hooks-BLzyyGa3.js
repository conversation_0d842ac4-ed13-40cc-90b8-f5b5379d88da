import{u as e}from"./useLoading-D5mh7tTu.js";import{u as a}from"./usePagination-Dd_EW2BO.js";import{d as o,e as t,f as r,g as i,h as d,i as s,j as n}from"./apiStore-vHDpafS2.js";import{d as c}from"./dayjs.min-Daes5FZc.js";import{$ as v,a0 as m}from"./index-D-8JbLQk.js";import{B as l,r as u,c as g}from"./vue-D-10XvVk.js";const C=C=>{const{loading:N,setLoading:b}=e(),{pagination:y}=a(),p=l({name:void 0,businessState:void 0,contactName:void 0,contactNumber:void 0,storeCategoryId:void 0,storeSubCategoryId:void 0,provinceCode:void 0,cityCode:void 0,areaCode:void 0,createTime:void 0}),P=u(void 0),h=g((()=>P.value?I.value.find((e=>e.id===P.value)):null)),k=u([]),S=g((()=>k.value.length?I.value.filter((e=>k.value.includes(e.id))):[])),I=u([]),f=async()=>{var e;b(!0),P.value=void 0,k.value=[];try{const{data:a}=await o({...p,...C,contactNumber:null==(e=p.contactNumber)?void 0:e.toString(),pageNum:y.current,pageSize:y.pageSize});I.value=a.rows.map((e=>(e.createTime=c(e.createTime).format("YYYY-MM-DD HH:mm:ss"),e.delTime=e.delTime?c(e.createTime).format("YYYY-MM-DD HH:mm:ss"):"",e))),y.total=a.total,b(!1)}catch(a){I.value=[],y.total=0,b(!1)}},w=l({logo:void 0,storeBackgroundPictures:[],storePropagandaPictures:[],storeIntroduce:void 0,storeNo:void 0,name:void 0,businessState:1,storeCategoryId:void 0,storeCategoryName:void 0,storeSubCategoryId:void 0,storeSubCategoryName:void 0,provinceCode:void 0,provinceName:void 0,cityCode:void 0,cityName:void 0,areaCode:void 0,areaName:void 0,detailAddress:void 0,longitude:void 0,latitude:void 0,invoiceTitle:void 0,invoiceTaxNumber:void 0,bankName:void 0,bankBranchName:void 0,bankAccount:void 0,bankReservePhone:void 0,bankCode:void 0,bankBranchId:void 0,bankProvinceName:void 0,bankCityName:void 0,license:void 0,enterpriseName:void 0,enterpriseNature:void 0,legalPerson:void 0,legalPersonIdentityCard:void 0,legalPersonPhone:void 0,legalPersonIdCardFront:void 0,legalPersonIdCardBack:void 0,unifiedSocialCreditCode:void 0,registeredAddress:void 0,contactName:void 0,contactNumber:void 0,contactNumber2:void 0,merchNo:void 0,businessScope:void 0,platformCommissionRate:void 0,mobile:void 0});return{loading:N,queryParams:p,pagination:y,rows:I,selectedId:P,selectedRow:h,selectedIds:k,selectedRows:S,selectAll:e=>{k.value=e?I.value.map((e=>e.id)):[]},rowSelect:(e,a,o)=>{k.value.includes(o.id)?k.value.splice(k.value.indexOf(o.id),1):k.value.push(o.id)},rowClick:e=>{k.value.includes(e.id)?k.value.splice(k.value.indexOf(e.id),1):k.value.push(e.id)},query:f,reset:()=>{y.current=1,Object.assign(p,{name:void 0,businessState:void 0,contactName:void 0,contactNumber:void 0,storeCategoryId:void 0,storeSubCategoryId:void 0,provinceCode:void 0,cityCode:void 0,areaCode:void 0,createTime:void 0}),f()},pageChange:async e=>{y.current=e,f()},pageSizeChange:async e=>{y.current=1,y.pageSize=e,f()},form:w,add:async e=>{try{const{storeCategoryName:a,storeSubCategoryName:o,...r}=e;await t({...r})}catch(a){throw a}},detail:async e=>{try{const{data:a}=await r(e),{logo:o,storeBackgroundPictures:t,storePropagandaPictures:i,storeIntroduce:d,storeNo:s,name:n,storeMccCode:c,businessState:m,storeCategoryId:l,storeCategoryName:u,storeSubCategoryId:g,storeSubCategoryName:C,provinceCode:N,provinceName:b,cityCode:y,cityName:p,areaCode:P,areaName:h,detailAddress:k,longitude:S,latitude:I,invoiceTitle:f,invoiceTaxNumber:B,bankName:T,bankBranchName:j,bankAccount:A,bankReservePhone:R,bankCode:x,bankBranchId:Y,bankProvinceName:M,bankCityName:z,license:D,enterpriseName:H,enterpriseNature:O,legalPerson:F,legalPersonIdentityCard:q,legalPersonPhone:L,legalPersonIdCardFront:$,legalPersonIdCardBack:_,unifiedSocialCreditCode:E,registeredAddress:G,contactName:J,contactNumber:K,contactNumber2:Q,merchNo:U,businessScope:V,platformCommissionRate:W}=a;Object.assign(w,{logo:o,storeBackgroundPictures:t,storePropagandaPictures:i,storeIntroduce:d,storeNo:s,name:n,storeMccCode:c,businessState:m,storeCategoryId:l,storeCategoryName:u,storeSubCategoryId:g,storeSubCategoryName:C,provinceCode:N,provinceName:b,cityCode:y,cityName:p,areaCode:P,areaName:h,detailAddress:k,longitude:S,latitude:I,invoiceTitle:f,invoiceTaxNumber:B,bankName:T,bankBranchName:j,bankAccount:A,bankReservePhone:Number(R)||void 0,bankCode:x,bankBranchId:Y,bankProvinceName:M,bankCityName:z,license:D,enterpriseName:H,enterpriseNature:O,legalPerson:F,legalPersonIdentityCard:q,legalPersonPhone:Number(L)||void 0,legalPersonIdCardFront:$,legalPersonIdCardBack:_,unifiedSocialCreditCode:E,registeredAddress:G,contactName:J,contactNumber:Number(K)||void 0,contactNumber2:Number(Q)||void 0,merchNo:U,businessScope:V,platformCommissionRate:v(W,100)})}catch(a){throw a}},edit:async(e,a)=>{try{const{storeCategoryName:o,storeSubCategoryName:t,platformCommissionRate:r,...d}=a;await i(e,{...d,platformCommissionRate:m(r,100)})}catch(o){throw o}},del:async e=>{try{await d(e)}catch(a){throw a}},approve:async(e,a)=>{try{await s(e,a)}catch(o){throw o}},exports:async()=>{try{await n()}catch(e){throw e}}}};export{C as u};

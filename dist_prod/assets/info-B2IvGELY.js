import"./index-DOhy6BH_.js";import{C as e,R as o}from"./index-BEo1tUsK.js";import{F as l,a as t}from"./index-DVDXfQhn.js";import{_ as r}from"./index-DkZuZOQi.js";import{I as s}from"./index-DDFSMqsG.js";import{S as a}from"./index-Cuq5XRs0.js";import"./index-DD6vSYIM.js";import"./index-DQjhgQFu.js";import{u as i}from"./useCommon-BuUbRw8e.js";import{u as d}from"./index-Cioag8eE.js";import{d as n,o as p,K as m,j as u,k as c,A as f,z as j,u as h}from"./vue-D-10XvVk.js";import"./index-D-8JbLQk.js";import"./pick-Ccd8Sfcm.js";import"./index-DGtjsHgS.js";import"./index-CdWxsKz_.js";import"./apiUpload-DpATemHF.js";import"./_plugin-vue_export-helper-BCo6x5W8.js";import"./ResizeObserver.es-CzGuHLZU.js";import"./resize-observer-Dtogi-DJ.js";import"./apiCommon-DcubqwY_.js";import"./index-DdMaxvYa.js";import"./index-DfEXMvnc.js";import"./use-children-components-v8i8lsOx.js";import"./index-DmW4RN1x.js";import"./index-dpn1_5z1.js";import"./dayjs.min-Daes5FZc.js";import"./render-function-CAXdZVZM.js";/* empty css              */import"./useLoading-D5mh7tTu.js";import"./usePagination-Dd_EW2BO.js";import"./index-O7pr3qsq.js";const g={class:"overflow-y-scroll no-scrollbar"},x=n({__name:"info",props:{id:{}},setup(n,{expose:x}){const y=n,{storeOptions:C,initStoreOptions:b,storeCategoryOptions:I,initStoreCategoryOptions:_,storeSubCategoryOptions:V,initStoreSubCategoryOptions:$}=i(),{form:w,detail:S}=d();p((async()=>{_(),b(),y.id&&(await S(y.id),$(w.storeCategoryId))}));const v=m("formRef");return x({formRef:v,form:w}),(i,d)=>{const n=a,p=l,m=e,x=o,y=s,_=r,S=t;return u(),c("div",g,[f(S,{ref_key:"formRef",ref:v,model:h(w),"auto-label-width":""},{default:j((()=>[f(x,{gutter:16},{default:j((()=>[f(m,{span:12},{default:j((()=>[f(p,{"show-colon":"",label:"一级分类",field:"storeCategoryId"},{default:j((()=>[f(n,{modelValue:h(w).storeCategoryId,"onUpdate:modelValue":d[0]||(d[0]=e=>h(w).storeCategoryId=e),options:h(I),placeholder:`${i.$selectPlaceholder}一级分类`,onChange:d[1]||(d[1]=e=>{h(w).storeSubCategoryId=void 0,V.value=[],h($)(e),h(b)({storeCategoryId:e})})},null,8,["modelValue","options","placeholder"])])),_:1})])),_:1}),f(m,{span:12},{default:j((()=>[f(p,{"show-colon":"",label:"二级分类",field:"storeSubCategoryId"},{default:j((()=>[f(n,{modelValue:h(w).storeSubCategoryId,"onUpdate:modelValue":d[2]||(d[2]=e=>h(w).storeSubCategoryId=e),options:h(V),placeholder:`${i.$selectPlaceholder}二级分类`,onChange:d[3]||(d[3]=e=>{h(b)({storeCategoryId:h(w).storeCategoryId,storeSubCategoryId:e})})},null,8,["modelValue","options","placeholder"])])),_:1})])),_:1})])),_:1}),f(p,{"show-colon":"",label:"店铺名称",field:"storeId"},{default:j((()=>[f(n,{modelValue:h(w).storeId,"onUpdate:modelValue":d[4]||(d[4]=e=>h(w).storeId=e),options:h(C),placeholder:`${i.$selectPlaceholder}店铺名称`,"allow-search":"","allow-clear":""},null,8,["modelValue","options","placeholder"])])),_:1}),f(p,{"show-colon":"",label:"公告标题",field:"title",rules:[{required:!0,message:`${i.$inputPlaceholder}公告标题`}]},{default:j((()=>[f(y,{modelValue:h(w).title,"onUpdate:modelValue":d[5]||(d[5]=e=>h(w).title=e),placeholder:`${i.$inputPlaceholder}公告标题`,"max-length":50,"show-word-limit":""},null,8,["modelValue","placeholder"])])),_:1},8,["rules"]),f(p,{"show-colon":"",label:"公告内容",field:"content",rules:[{required:!0,message:`${i.$inputPlaceholder}公告内容`}]},{default:j((()=>[f(_,{modelValue:h(w).content,"onUpdate:modelValue":d[6]||(d[6]=e=>h(w).content=e),placeholder:`${i.$inputPlaceholder}公告内容`},null,8,["modelValue","placeholder"])])),_:1},8,["rules"])])),_:1},8,["model"])])}}});export{x as default};

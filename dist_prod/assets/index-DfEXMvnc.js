import{g as e,k as l,i as a,U as u,r as n,aw as t,V as o,s as r,f as s}from"./index-D-8JbLQk.js";import{j as i,K as d,I as p,i as v}from"./index-DDFSMqsG.js";import{u as m,B as c}from"./index-DGtjsHgS.js";import{u as f}from"./index-DOhy6BH_.js";import{d as b,t as y,r as x,c as g,w as h,A as w,F as O}from"./vue-D-10XvVk.js";var $=Object.defineProperty,M=Object.getOwnPropertySymbols,N=Object.prototype.hasOwnProperty,j=Object.prototype.propertyIsEnumerable,B=(e,l,a)=>l in e?$(e,l,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[l]=a,V=(e,l)=>{for(var a in l||(l={}))N.call(l,a)&&B(e,a,l[a]);if(M)for(var a of M(l))j.call(l,a)&&B(e,a,l[a]);return e};v.enableBoundaryChecking(!1);var z=b({name:"InputNumber",props:{modelValue:Number,defaultValue:Number,mode:{type:String,default:"embed"},precision:Number,step:{type:Number,default:1},disabled:{type:Boolean,default:!1},error:{type:Boolean,default:!1},max:{type:Number,default:1/0},min:{type:Number,default:-1/0},formatter:{type:Function},parser:{type:Function},placeholder:String,hideButton:{type:Boolean,default:!1},size:{type:String},allowClear:{type:Boolean,default:!1},modelEvent:{type:String,default:"change"},readOnly:{type:Boolean,default:!1},inputAttrs:{type:Object}},emits:{"update:modelValue":e=>!0,change:(e,l)=>!0,focus:e=>!0,blur:e=>!0,clear:e=>!0,input:(e,l,a)=>!0,keydown:e=>!0},setup(r,{emit:s,slots:b}){var $;const{size:M,disabled:N}=y(r),j=e("input-number"),B=x(),{mergedSize:z,mergedDisabled:S,eventHandlers:C}=m({size:M,disabled:N}),{mergedSize:R}=f(z),A=g((()=>{if(l(r.precision)){const e=`${r.step}`.split(".")[1],l=e&&e.length||0;return Math.max(l,r.precision)}})),_=e=>{var a,u;if(!l(e))return"";const n=A.value?e.toFixed(A.value):String(e);return null!=(u=null==(a=r.formatter)?void 0:a.call(r,n))?u:n},k=x(_(null!=($=r.modelValue)?$:r.defaultValue)),D=g((()=>{var e,l;if(!k.value)return;const a=Number(null!=(l=null==(e=r.parser)?void 0:e.call(r,k.value))?l:k.value);return Number.isNaN(a)?void 0:a})),E=x(l(D.value)&&D.value<=r.min),F=x(l(D.value)&&D.value>=r.max);let I=0;const P=()=>{I&&(window.clearTimeout(I),I=0)},W=e=>{if(!a(e))return l(r.min)&&e<r.min&&(e=r.min),l(r.max)&&e>r.max&&(e=r.max),l(A.value)?v.round(e,A.value):e},K=e=>{let a=!1,u=!1;l(e)&&(e<=r.min&&(a=!0),e>=r.max&&(u=!0)),F.value!==u&&(F.value=u),E.value!==a&&(E.value=a)},T=()=>{const e=W(D.value),l=_(e);e===D.value&&k.value===l||(k.value=l),s("update:modelValue",e)};h((()=>[r.max,r.min]),(()=>{T(),K(D.value)}));const U=(e,a)=>{if(S.value||"plus"===e&&F.value||"minus"===e&&E.value)return;let u;u=l(D.value)?W(v[e](D.value,r.step)):r.min===-1/0?0:r.min,k.value=_(u),K(u),s("update:modelValue",u),s("change",u,a)},H=(e,l,a=!1)=>{var u;e.preventDefault(),r.readOnly||(null==(u=B.value)||u.focus(),U(l,e),a&&(I=window.setTimeout((()=>e.target.dispatchEvent(e)),I?150:800)))},q=(e,a)=>{var u,n,t,o;e=e.trim().replace(/。/g,"."),e=null!=(n=null==(u=r.parser)?void 0:u.call(r,e))?n:e,(l(Number(e))||/^(\.|-)$/.test(e))&&(k.value=null!=(o=null==(t=r.formatter)?void 0:t.call(r,e))?o:e,K(D.value),s("input",D.value,k.value,a),"input"===r.modelEvent&&(s("update:modelValue",D.value),s("change",D.value,a)))},G=e=>{s("focus",e)},J=(e,l)=>{l instanceof MouseEvent&&!e||(T(),s("change",D.value,l))},L=e=>{s("blur",e)},Q=e=>{var l,a;k.value="",s("update:modelValue",void 0),s("change",void 0,e),null==(a=null==(l=C.value)?void 0:l.onChange)||a.call(l,e),s("clear",e)},X=i(new Map([[d.ARROW_UP,e=>{e.preventDefault(),!r.readOnly&&U("plus",e)}],[d.ARROW_DOWN,e=>{e.preventDefault(),!r.readOnly&&U("minus",e)}]])),Y=e=>{s("keydown",e),e.defaultPrevented||X(e)};h((()=>r.modelValue),(e=>{e!==D.value&&(k.value=_(e),K(e))}));const Z=()=>{var e,l,a;return r.readOnly?null:w(O,null,[b.suffix&&w("div",{class:`${j}-suffix`},[null==(e=b.suffix)?void 0:e.call(b)]),w("div",{class:`${j}-step`},[w("button",{class:[`${j}-step-button`,{[`${j}-step-button-disabled`]:S.value||F.value}],type:"button",tabindex:"-1",disabled:S.value||F.value,onMousedown:e=>H(e,"plus",!0),onMouseup:P,onMouseleave:P},[b.plus?null==(l=b.plus)?void 0:l.call(b):w(u,null,null)]),w("button",{class:[`${j}-step-button`,{[`${j}-step-button-disabled`]:S.value||E.value}],type:"button",tabindex:"-1",disabled:S.value||E.value,onMousedown:e=>H(e,"minus",!0),onMouseup:P,onMouseleave:P},[b.minus?null==(a=b.minus)?void 0:a.call(b):w(n,null,null)])])])},ee=g((()=>[j,`${j}-mode-${r.mode}`,`${j}-size-${R.value}`,{[`${j}-readonly`]:r.readOnly}])),le=()=>w(c,{size:R.value,tabindex:"-1",class:`${j}-step-button`,disabled:S.value||E.value,onMousedown:e=>H(e,"minus",!0),onMouseup:P,onMouseleave:P},{icon:()=>w(t,null,null)}),ae=()=>w(c,{size:R.value,tabindex:"-1",class:`${j}-step-button`,disabled:S.value||F.value,onMousedown:e=>H(e,"plus",!0),onMouseup:P,onMouseleave:P},{icon:()=>w(o,null,null)});return{inputRef:B,render:()=>{const e="embed"===r.mode?{prepend:b.prepend,prefix:b.prefix,suffix:r.hideButton?b.suffix:Z,append:b.append}:{prepend:r.hideButton?b.prepend:le,prefix:b.prefix,suffix:b.suffix,append:r.hideButton?b.append:ae};return w(p,{key:`__arco__${r.mode}`,ref:B,class:ee.value,type:"text",allowClear:r.allowClear,size:R.value,modelValue:k.value,placeholder:r.placeholder,disabled:S.value,readonly:r.readOnly,error:r.error,inputAttrs:V({role:"spinbutton","aria-valuemax":r.max,"aria-valuemin":r.min,"aria-valuenow":k.value},r.inputAttrs),onInput:q,onFocus:G,onBlur:L,onClear:Q,onChange:J,onKeydown:Y},e)}}},methods:{focus(){var e;null==(e=this.inputRef)||e.focus()},blur(){var e;null==(e=this.inputRef)||e.blur()}},render(){return this.render()}});const S=Object.assign(z,{install:(e,l)=>{r(e,l);const a=s(l);e.component(a+z.name,z)}});export{S as I};

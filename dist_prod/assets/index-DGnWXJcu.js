const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/info-C3cMYgqV.js","assets/index-DOhy6BH_.js","assets/index-D-8JbLQk.js","assets/vue-D-10XvVk.js","assets/index-DxPaQOvH.css","assets/index-DB09tZwb.css","assets/index-BEo1tUsK.js","assets/pick-Ccd8Sfcm.js","assets/index-6rnfXikd.css","assets/index-DVDXfQhn.js","assets/index-DGtjsHgS.js","assets/index-BJBnsrKF.css","assets/index-DDFSMqsG.js","assets/ResizeObserver.es-CzGuHLZU.js","assets/index-RZyF5P1Y.css","assets/index-8er2yjEK.css","assets/index-HLwNT5T9.js","assets/index-DD6vSYIM.js","assets/index-C0ni2jp2.css","assets/index-CuYx5vtf.js","assets/render-function-CAXdZVZM.js","assets/index-BZi9bKOJ.css","assets/index-CdWxsKz_.js","assets/index-CX9L_GU1.css","assets/apiUpload-DpATemHF.js","assets/_plugin-vue_export-helper-BCo6x5W8.js","assets/index-IkV6U84s.css","assets/index-Cuq5XRs0.js","assets/resize-observer-Dtogi-DJ.js","assets/index-DQjhgQFu.js","assets/index-Db7LPRu1.css","assets/index-Dbgee0nK.css","assets/useCommon-BuUbRw8e.js","assets/apiCommon-DcubqwY_.js","assets/index-DdMaxvYa.js","assets/index-DfEXMvnc.js","assets/use-children-components-v8i8lsOx.js","assets/index-B5FzkxT_.css","assets/index-DmW4RN1x.js","assets/index-Bl_vBcmJ.css","assets/index-CUtvFEc_.js","assets/use-index-D_ozg7PK.js","assets/index-CHOaln3D.js","assets/index-komh9C6_.css","assets/useLoading-D5mh7tTu.js","assets/usePagination-Dd_EW2BO.js","assets/dayjs.min-Daes5FZc.js","assets/index-O7pr3qsq.js","assets/index-BlQqQ5bI.css","assets/index-CJ6Fn8S6.css"])))=>i.map(i=>d[i]);
import{O as e,N as a,m as t}from"./index-D-8JbLQk.js";import"./index-DOhy6BH_.js";import"./index-DDFSMqsG.js";import{S as l,L as o}from"./index-Cuq5XRs0.js";import"./index-DD6vSYIM.js";import"./index-DQjhgQFu.js";import{T as i,a as n,P as s}from"./index-DdMaxvYa.js";import{B as d,S as r}from"./index-DGtjsHgS.js";import{C as c}from"./index-CdWxsKz_.js";import{D as u}from"./index-DmW4RN1x.js";/* empty css              */import{A as p}from"./index-CUtvFEc_.js";import{C as m,R as f}from"./index-BEo1tUsK.js";import{F as g,a as h}from"./index-DVDXfQhn.js";/* empty css              */import{B as v,r as y,c as w,d as _,K as x,o as C,k,A as b,z as S,u as j,e as I,q as z,N as O,f as P,j as V,I as R,y as T,p as $,J as Y,af as M,M as D,h as U}from"./vue-D-10XvVk.js";import{u as q}from"./useCommon-BuUbRw8e.js";import{u as A}from"./useLoading-D5mh7tTu.js";import{u as B}from"./usePagination-Dd_EW2BO.js";import{d as E}from"./dayjs.min-Daes5FZc.js";import{M as H}from"./index-O7pr3qsq.js";import{I as L}from"./index-DfEXMvnc.js";const N=()=>{const{loading:a,setLoading:t}=A(),{pagination:l}=B(),o=v({parentId:void 0,id:void 0}),i=y(void 0),n=w((()=>i.value?r.value.find((e=>e.id===i.value)):null)),s=y([]),d=w((()=>s.value.length?r.value.filter((e=>s.value.includes(e.id))):[])),r=y([]),c=async()=>{t(!0),i.value=void 0,s.value=[];try{const{data:a}=await(a=>e({url:"/admin/platform/storeCategory/list",method:"post",data:a}))({...o,pageNum:l.current,pageSize:l.pageSize});r.value=a.rows.map((e=>{var a;return e.createTime=E(e.createTime).format("YYYY-MM-DD HH:mm:ss"),e.children=null==(a=e.children)?void 0:a.map((e=>(e.createTime=E(e.createTime).format("YYYY-MM-DD HH:mm:ss"),e))),e})),l.total=a.total,t(!1)}catch(a){r.value=[],l.total=0,t(!1)}},u=v({parentId:void 0,icon:void 0,name:void 0});return{loading:a,queryParams:o,pagination:l,rows:r,selectedId:i,selectedRow:n,selectedIds:s,selectedRows:d,selectAll:e=>{s.value=e?r.value.map((e=>e.id)):[]},rowSelect:(e,a,t)=>{s.value.includes(t.id)?s.value.splice(s.value.indexOf(t.id),1):s.value.push(t.id)},rowClick:e=>{s.value.includes(e.id)?s.value.splice(s.value.indexOf(e.id),1):s.value.push(e.id)},query:c,reset:()=>{l.current=1,Object.assign(o,{parentId:void 0,id:void 0}),c()},pageChange:async e=>{l.current=e,c()},pageSizeChange:async e=>{l.current=1,l.pageSize=e,c()},form:u,add:async a=>{try{await(a=>e({url:"/admin/platform/storeCategory/add",method:"put",data:a}))({...a})}catch(t){throw t}},detail:async a=>{try{const{data:t}=await(a=>e({url:`/admin/platform/storeCategory/detail/${a}`,method:"get"}))(a),{parentId:l,icon:o,name:i}=t;Object.assign(u,{parentId:l||void 0,icon:o,name:i})}catch(t){throw t}},edit:async(a,t)=>{try{await((a,t)=>e({url:`/admin/platform/storeCategory/edit/${a}`,method:"put",data:t}))(a,t)}catch(l){throw l}},del:async a=>{try{await(a=>e({url:`/admin/platform/storeCategory/del/${a}`,method:"delete"}))(a)}catch(t){throw t}},setSortIndex:async(a,t)=>{try{await((a,t)=>e({url:`/admin/platform/storeCategory/setSortIndex/${a}`,method:"put",data:{sortIndex:t}}))(a,t)}catch(l){throw l}}}},F={class:"page-container"},J={class:"h-full flex flex-col gap-[18px]"},K={key:0},G={key:1},Q=["src"],W={key:1},X=_({__name:"index",setup(e){const v=D((()=>t((()=>import("./info-C3cMYgqV.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49])))),{storeCategoryOptions:w,initStoreCategoryOptions:_,storeSubCategoryOptions:A,initStoreSubCategoryOptions:B}=q(),{loading:E,queryParams:X,pagination:Z,rows:ee,selectedId:ae,selectedIds:te,selectAll:le,rowSelect:oe,rowClick:ie,query:ne,reset:se,pageChange:de,pageSizeChange:re,add:ce,edit:ue,del:pe,setSortIndex:me}=N(),fe=x("addRef"),ge=y(!1),he=async e=>{var t,l;try{if(await(null==(l=null==(t=fe.value)?void 0:t.formRef)?void 0:l.validate()))throw new Error("校验失败");await ce(O(fe.value.form)),a.success({title:"成功提示",content:"已添加店铺分类",duration:1500}),e(!0),ne()}catch(o){e(!1)}},ve=x("editRef"),ye=y(!1),we=async e=>{var t,l;try{if(await(null==(l=null==(t=ve.value)?void 0:t.formRef)?void 0:l.validate()))throw new Error("校验失败");await ue(ae.value,O(ve.value.form)),a.success({title:"成功提示",content:"已修改店铺分类",duration:1500}),e(!0),ne()}catch(o){e(!1)}};return C((()=>{_(),de(1)})),(e,t)=>{const y=H,_=l,x=g,C=m,O=f,D=P("icon-search"),q=d,N=P("icon-refresh"),te=r,le=h,oe=c,ie=P("icon-plus"),ce=i,ue=p,_e=L,xe=P("icon-check"),Ce=P("icon-close"),ke=u,be=o,Se=n,je=s;return V(),k("div",F,[b(y,{visible:j(ge),"onUpdate:visible":t[0]||(t[0]=e=>I(ge)?ge.value=e:null),"title-align":"start",title:"添加店铺分类","cancel-button-props":{type:"outline"},"unmount-on-close":"","mask-closable":!1,"esc-to-close":!1,"on-before-ok":he,onCancel:t[1]||(t[1]=e=>ge.value=!1)},{default:S((()=>[b(j(v),{ref_key:"addRef",ref:fe,type:"add",id:j(ae)},null,8,["id"])])),_:1},8,["visible"]),b(y,{visible:j(ye),"onUpdate:visible":t[2]||(t[2]=e=>I(ye)?ye.value=e:null),"title-align":"start",title:"修改店铺分类","cancel-button-props":{type:"outline"},"unmount-on-close":"","mask-closable":!1,"esc-to-close":!1,"on-before-ok":we,onCancel:t[3]||(t[3]=e=>ye.value=!1)},{default:S((()=>[b(j(v),{ref_key:"editRef",ref:ve,type:"edit",id:j(ae)},null,8,["id"])])),_:1},8,["visible"]),z("div",J,[b(oe,{bordered:!1},{default:S((()=>[b(le,{model:j(X),"auto-label-width":""},{default:S((()=>[b(O,{gutter:16},{default:S((()=>[b(C,{span:6},{default:S((()=>[b(x,{"show-colon":"",label:"店铺分类"},{default:S((()=>[b(O,{align:"center",class:"w-full"},{default:S((()=>[b(C,{span:11},{default:S((()=>[b(x,{"no-style":"",field:"parentId"},{default:S((()=>[b(_,{modelValue:j(X).parentId,"onUpdate:modelValue":t[4]||(t[4]=e=>j(X).parentId=e),options:j(w),placeholder:`${e.$selectPlaceholder}一级分类`,onChange:t[5]||(t[5]=e=>{j(X).id=void 0,A.value=[],j(B)(e)}),"allow-clear":""},null,8,["modelValue","options","placeholder"])])),_:1})])),_:1}),b(C,{span:2,class:"text-center"},{default:S((()=>t[9]||(t[9]=[R("-")]))),_:1}),b(C,{span:11},{default:S((()=>[b(x,{"no-style":"",field:"id"},{default:S((()=>[b(_,{modelValue:j(X).id,"onUpdate:modelValue":t[6]||(t[6]=e=>j(X).id=e),options:j(A),placeholder:`${e.$selectPlaceholder}二级分类`,"allow-clear":""},null,8,["modelValue","options","placeholder"])])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),b(C,{span:6},{default:S((()=>[b(x,{"hide-label":""},{default:S((()=>[b(te,{size:18},{default:S((()=>[b(q,{type:"primary",onClick:t[7]||(t[7]=e=>j(de)(1))},{icon:S((()=>[b(D)])),default:S((()=>[t[10]||(t[10]=R(" 查询 "))])),_:1}),b(q,{type:"outline",onClick:j(se)},{icon:S((()=>[b(N)])),default:S((()=>[t[11]||(t[11]=R(" 重置 "))])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),b(oe,{bordered:!1,class:"flex-1 overflow-y-hidden","body-style":{height:"100%"}},{actions:S((()=>[j(Z).total?(V(),T(je,{key:0,current:j(Z).current,"page-size":j(Z).pageSize,"show-total":j(Z).showTotal,"show-page-size":j(Z).showPageSize,"page-size-options":j(Z).pageSizeOptions,total:j(Z).total,onChange:j(de),onPageSizeChange:j(re)},null,8,["current","page-size","show-total","show-page-size","page-size-options","total","onChange","onPageSizeChange"])):$("",!0)])),default:S((()=>[b(O,{class:"mb-[12px]"},{default:S((()=>[b(C,{span:16},{default:S((()=>[b(te,null,{default:S((()=>[b(q,{type:"primary",onClick:t[8]||(t[8]=()=>{ae.value=void 0,ge.value=!0})},{icon:S((()=>[b(ie)])),default:S((()=>[t[12]||(t[12]=R(" 添加一级分类 "))])),_:1})])),_:1})])),_:1})])),_:1}),b(Se,{size:"large","row-key":"id",loading:j(E),pagination:!1,data:j(ee),bordered:{cell:!0},scroll:{y:"calc(100% - 96px)"}},{columns:S((()=>[b(ce,{align:"center",title:"序号",width:80},{cell:S((({record:e,rowIndex:a})=>[e.children?(V(),k("span",K,Y(j(Z).pageSize*(j(Z).current-1)+a+1),1)):(V(),k("span",G,Y(a+1),1))])),_:1}),b(ce,{align:"center",title:"店铺分类名称",width:200,ellipsis:"",tooltip:""},{cell:S((({record:e})=>[b(te,null,{default:S((()=>[b(ue,{size:32},{default:S((()=>[z("img",{src:e.icon},null,8,Q)])),_:2},1024),z("span",null,Y(e.name),1)])),_:2},1024)])),_:1}),b(ce,{align:"center",title:"排序",width:160},{cell:S((({record:e})=>[z("div",null,[e.showSetSortIndex?(V(),T(te,{key:0},{default:S((()=>[b(_e,{modelValue:e.sortIndex,"onUpdate:modelValue":a=>e.sortIndex=a,"hide-button":"",min:1,max:9999,disabled:!e.showSetSortIndex},null,8,["modelValue","onUpdate:modelValue","disabled"]),b(q,{type:"primary",onClick:M((t=>(async e=>{try{await me(e.id,e.sortIndex),a.success({title:"成功提示",content:"已设置店铺分类顺序",duration:1500}),ne()}catch(t){}})(e)),["stop"])},{icon:S((()=>[b(xe)])),_:2},1032,["onClick"]),b(q,{type:"primary",status:"warning",onClick:M(j(ne),["stop"])},{icon:S((()=>[b(Ce)])),_:1},8,["onClick"])])),_:2},1024)):(V(),k("span",W,Y(e.sortIndex),1))])])),_:1}),b(ce,{align:"center",title:"创建时间",width:180,"data-index":"createTime"}),b(ce,{align:"center",title:"操作",width:400,fixed:"right"},{cell:S((({record:e})=>[b(te,null,{split:S((()=>[b(ke,{direction:"vertical"})])),default:S((()=>[e.children?(V(),T(be,{key:0,onClick:M((()=>{ae.value=e.id,ge.value=!0}),["stop"])},{default:S((()=>t[13]||(t[13]=[R(" 添加二级分类 ")]))),_:2},1032,["onClick"])):$("",!0),b(be,{onClick:M((()=>{ae.value=e.id,ye.value=!0}),["stop"])},{default:S((()=>t[14]||(t[14]=[R(" 编辑 ")]))),_:2},1032,["onClick"]),b(be,{disabled:j(ee).some((e=>e.showSetSortIndex)),onClick:M((()=>{e.showSetSortIndex=!0}),["stop"])},{default:S((()=>t[15]||(t[15]=[R(" 排序 ")]))),_:2},1032,["disabled","onClick"]),b(be,{status:"danger",onClick:M((t=>(e=>{try{H.warning({title:"提示",content:()=>U("div",{class:"text-center"},`确定删除【${e.name}】？`),maskClosable:!1,escToClose:!1,hideCancel:!1,cancelButtonProps:{type:"outline"},onBeforeOk:async t=>{try{await pe(e.id),a.success({title:"成功提示",content:"已删除店铺分类",duration:1500}),t(!0),ne()}catch(l){t(!1)}}})}catch(t){}})(e)),["stop"])},{default:S((()=>t[16]||(t[16]=[R("删除")]))),_:2},1032,["onClick"])])),_:2},1024)])),_:1})])),_:1},8,["loading","data"])])),_:1})])])}}}),Z=Object.freeze(Object.defineProperty({__proto__:null,default:X},Symbol.toStringTag,{value:"Module"}));export{Z as i,N as u};

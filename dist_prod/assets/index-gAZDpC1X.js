import"./index-DOhy6BH_.js";import{C as e,S as t}from"./index-CdWxsKz_.js";import{C as a,R as o}from"./index-BEo1tUsK.js";import{i as l,I as n,e as i,a as s,b as r,g as u,o as p,r as d}from"./index-DDFSMqsG.js";import{E as c}from"./index-DD6vSYIM.js";import{_ as f,g as v,k as m,i as y,s as h,f as x,K as g,L as b,M as w,h as C,O as S,D}from"./index-D-8JbLQk.js";import{d as E,c as k,r as _,t as $,o as Y,w as T,j,k as M,l as O,m as B,I as F,J as L,p as P,q as N,O as V,F as A,b as I,f as R,A as z,x as U,y as H,z as W,af as q,v as G,C as K,a2 as J,B as Z,G as Q,S as X,H as ee,ab as te,L as ae,u as oe,n as le,ai as ne,e as ie,a6 as se}from"./vue-D-10XvVk.js";import{R as re}from"./resize-observer-Dtogi-DJ.js";import{P as ue}from"./index-CHOaln3D.js";import{d as pe}from"./dayjs.min-Daes5FZc.js";import{T as de,R as ce}from"./index-dpn1_5z1.js";import{a as fe,G as ve}from"./index-C7OqXj1S.js";import{_ as me}from"./_plugin-vue_export-helper-BCo6x5W8.js";import{_ as ye,u as he}from"./index.vue_vue_type_script_setup_true_lang-DVPVBV8s.js";import{u as xe,i as ge,a as be,b as we,c as Ce,d as Se,e as De,f as Ee,g as ke,h as _e,E as $e,L as Ye}from"./chart-CZSEulei.js";import{R as Te,b as je,S as Me}from"./index-Cuq5XRs0.js";import{u as Oe}from"./useLoading-D5mh7tTu.js";import{S as Be}from"./index-DGtjsHgS.js";import"./index-DQjhgQFu.js";import{u as Fe}from"./useCommon-BuUbRw8e.js";import"./pick-Ccd8Sfcm.js";import"./ResizeObserver.es-CzGuHLZU.js";import"./render-function-CAXdZVZM.js";import"./use-index-D_ozg7PK.js";import"./apiCommon-DcubqwY_.js";const Le=E({name:"Statistic",props:{title:String,value:{type:[Number,Object]},format:{type:String,default:"HH:mm:ss"},extra:String,start:{type:Boolean,default:!0},precision:{type:Number,default:0},separator:String,showGroupSeparator:{type:Boolean,default:!1},animation:{type:Boolean,default:!1},animationDuration:{type:Number,default:2e3},valueFrom:{type:Number,default:void 0},placeholder:{type:String},valueStyle:{type:Object}},setup(e){var t;const a=v("statistic"),o=k((()=>m(e.value)?e.value:0)),n=_(null!=(t=e.valueFrom)?t:e.value),i=_(null),{value:s}=$(e),r=k((()=>y(e.value))),u=(t=(t=>null!=(t=e.valueFrom)?t:0)(),a=o.value)=>{var l;t!==a&&(i.value=new de({from:{value:t},to:{value:a},duration:e.animationDuration,easing:"quartOut",onUpdate:e=>{n.value=e.value},onFinish:()=>{n.value=a}}),null==(l=i.value)||l.start())},p=k((()=>{let t=n.value;if(m(t)){m(e.precision)&&(t=l.round(t,e.precision).toFixed(e.precision));const a=String(t).split(".");return{isNumber:!0,integer:e.showGroupSeparator?Number(a[0]).toLocaleString("en-US"):a[0],decimal:a[1]}}return e.format&&(t=pe(t).format(e.format)),{isNumber:!1,value:t}}));return Y((()=>{e.animation&&e.start&&u()})),T((()=>e.start),(t=>{t&&e.animation&&!i.value&&u()})),T(s,(t=>{var a;i.value&&(null==(a=i.value)||a.stop(),i.value=null),n.value=t,e.animation&&e.start&&u()})),{prefixCls:a,showPlaceholder:r,formatValue:p}}}),Pe={key:0};var Ne=f(Le,[["render",function(e,t,a,o,l,n){return j(),M("div",{class:O(e.prefixCls)},[e.title||e.$slots.title?(j(),M("div",{key:0,class:O(`${e.prefixCls}-title`)},[B(e.$slots,"title",{},(()=>[F(L(e.title),1)]))],2)):P("v-if",!0),N("div",{class:O(`${e.prefixCls}-content`)},[N("div",{class:O(`${e.prefixCls}-value`),style:V(e.valueStyle)},[e.showPlaceholder?(j(),M("span",Pe,L(e.placeholder),1)):(j(),M(A,{key:1},[e.$slots.prefix?(j(),M("span",{key:0,class:O(`${e.prefixCls}-prefix`)},[B(e.$slots,"prefix")],2)):P("v-if",!0),e.formatValue.isNumber?(j(),M(A,{key:1},[N("span",{class:O(`${e.prefixCls}-value-integer`)},L(e.formatValue.integer),3),e.formatValue.decimal?(j(),M("span",{key:0,class:O(`${e.prefixCls}-value-decimal`)}," ."+L(e.formatValue.decimal),3)):P("v-if",!0)],64)):(j(),M(A,{key:2},[F(L(e.formatValue.value),1)],2112)),e.$slots.suffix?(j(),M("span",{key:3,class:O(`${e.prefixCls}-suffix`)},[B(e.$slots,"suffix")],2)):P("v-if",!0)],64))],6),e.extra||e.$slots.extra?(j(),M("div",{key:0,class:O(`${e.prefixCls}-extra`)},[B(e.$slots,"extra",{},(()=>[F(L(e.extra),1)]))],2)):P("v-if",!0)],2)],2)}]]);const Ve=[["Y",31536e6],["M",2592e6],["D",864e5],["H",36e5],["m",6e4],["s",1e3],["S",1]];function Ae(e,t){let a=e;return Ve.reduce(((e,[t,o])=>{if(-1!==e.indexOf(t)){const l=Math.floor(a/o);return a-=l*o,e.replace(new RegExp(`${t}+`,"g"),(e=>{const t=e.length;return String(l).padStart(t,"0")}))}return e}),t)}var Ie=f(E({name:"Countdown",props:{title:String,value:{type:Number,default:()=>Date.now()+3e5},now:{type:Number,default:()=>Date.now()},format:{type:String,default:"HH:mm:ss"},start:{type:Boolean,default:!0},valueStyle:{type:Object}},emits:{finish:()=>!0},setup(e,{emit:t}){const a=v("statistic"),{start:o,value:l,now:n,format:i}=$(e),s=_(Ae(Math.max(pe(e.value).diff(pe(e.now),"millisecond"),0),e.format));T([l,n,i],(()=>{const t=Ae(Math.max(pe(e.value).diff(pe(e.now),"millisecond"),0),e.format);t!==s.value&&(s.value=t)}));const r=_(0),u=()=>{r.value&&(window.clearInterval(r.value),r.value=0)},p=()=>{pe(e.value).valueOf()<Date.now()||(r.value=window.setInterval((()=>{const a=pe(e.value).diff(pe(),"millisecond");a<=0&&(u(),t("finish")),s.value=Ae(Math.max(a,0),e.format)}),1e3/30))};return Y((()=>{e.start&&p()})),I((()=>{u()})),T(o,(e=>{e&&!r.value&&p()})),{prefixCls:a,displayValue:s}}}),[["render",function(e,t,a,o,l,n){return j(),M("div",{class:O([`${e.prefixCls}`,`${e.prefixCls}-countdown`])},[e.title||e.$slots.title?(j(),M("div",{key:0,class:O(`${e.prefixCls}-title`)},[B(e.$slots,"title",{},(()=>[F(L(e.title),1)]))],2)):P("v-if",!0),N("div",{class:O(`${e.prefixCls}-content`)},[N("div",{class:O(`${e.prefixCls}-value`),style:V(e.valueStyle)},L(e.displayValue),7)],2)],2)}]]);const Re=Object.assign(Ne,{Countdown:Ie,install:(e,t)=>{h(e,t);const a=x(t);e.component(a+Ne.name,Ne),e.component(a+Ie.name,Ie)}});var ze=f(E({name:"TypographyEditContent",components:{Input:n},props:{text:{type:String,required:!0}},emits:["change","end","update:text"],setup(e,{emit:t}){const a=[`${v("typography")}-edit-content`],o=_();function l(){t("end")}return Y((()=>{if(!o.value||!o.value.$el)return;const e=o.value.$el.querySelector("input");if(!e)return;e.focus&&e.focus();const{length:t}=e.value;e.setSelectionRange(t,t)})),{classNames:a,inputRef:o,onBlur:l,onChange:function(e){t("update:text",e),t("change",e)},onEnd:l}}}),[["render",function(e,t,a,o,l,n){const i=R("Input");return j(),M("div",{class:O(e.classNames)},[z(i,{ref:"inputRef","auto-size":"","model-value":e.text,onBlur:e.onBlur,onInput:e.onChange,onKeydown:U(e.onEnd,["enter"])},null,8,["model-value","onBlur","onInput","onKeydown"])],2)}]]);var Ue=f(E({name:"TypographyOperations",components:{Tooltip:i,IconCheckCircleFill:g,IconCopy:b,IconEdit:w},props:{editable:Boolean,copyable:Boolean,expandable:Boolean,isCopied:Boolean,isEllipsis:Boolean,expanded:Boolean,forceRenderExpand:Boolean,editTooltipProps:Object,copyTooltipProps:Object},emits:{edit:()=>!0,copy:()=>!0,expand:()=>!0},setup(e,{emit:t}){const a=v("typography"),o=k((()=>e.forceRenderExpand||e.expandable&&e.isEllipsis)),{t:l}=s();return{prefixCls:a,showExpand:o,t:l,onEditClick(){t("edit")},onCopyClick(){t("copy")},onExpandClick(){t("expand")}}}}),[["render",function(e,t,a,o,l,n){const i=R("IconEdit"),s=R("Tooltip"),r=R("IconCheckCircleFill"),u=R("IconCopy");return j(),M(A,null,[e.editable?(j(),H(s,G({key:0,content:e.t("typography.edit")},e.editTooltipProps),{default:W((()=>[N("span",{class:O(`${e.prefixCls}-operation-edit`),onClick:t[0]||(t[0]=q(((...t)=>e.onEditClick&&e.onEditClick(...t)),["stop"]))},[z(i)],2)])),_:1},16,["content"])):P("v-if",!0),e.copyable?(j(),H(s,K(G({key:1},e.copyTooltipProps)),{content:W((()=>[B(e.$slots,"copy-tooltip",{copied:e.isCopied},(()=>[F(L(e.isCopied?e.t("typography.copied"):e.t("typography.copy")),1)]))])),default:W((()=>[N("span",{class:O({[`${e.prefixCls}-operation-copied`]:e.isCopied,[`${e.prefixCls}-operation-copy`]:!e.isCopied}),onClick:t[1]||(t[1]=q(((...t)=>e.onCopyClick&&e.onCopyClick(...t)),["stop"]))},[B(e.$slots,"copy-icon",{copied:e.isCopied},(()=>[e.isCopied?(j(),H(r,{key:0})):(j(),H(u,{key:1}))]))],2)])),_:3},16)):P("v-if",!0),e.showExpand?(j(),M("a",{key:2,class:O(`${e.prefixCls}-operation-expand`),onClick:t[2]||(t[2]=q(((...t)=>e.onExpandClick&&e.onExpandClick(...t)),["stop"]))},[B(e.$slots,"expand-node",{expanded:e.expanded},(()=>[F(L(e.expanded?e.t("typography.collapse"):e.t("typography.expand")),1)]))],2)):P("v-if",!0)],64)}]]);let He;function We(e){if(!e)return 0;const t=e.match(/^\d*(\.\d*)?/);return t?Number(t[0]):0}var qe=(e,t,a,o)=>{He||(He=document.createElement("div"),document.body.appendChild(He));const{rows:l,suffix:n,ellipsisStr:i}=t,s=window.getComputedStyle(e),r=(u=s,Array.prototype.slice.apply(u).map((e=>`${e}: ${u.getPropertyValue(e)};`)).join(""));var u;const p=We(s.lineHeight),d=Math.round(p*l+We(s.paddingTop)+We(s.paddingBottom));He.setAttribute("style",r),He.setAttribute("aria-hidden","true"),He.style.height="auto",He.style.minHeight="auto",He.style.maxHeight="auto",He.style.position="fixed",He.style.left="0",He.style.top="-99999999px",He.style.zIndex="-200",He.style.whiteSpace="normal";const c=J({render:()=>z("span",null,[a])});c.mount(He);const f=Array.prototype.slice.apply(He.childNodes[0].cloneNode(!0).childNodes);c.unmount(),He.innerHTML="";const v=document.createTextNode(`${i}${n}`);He.appendChild(v),f.forEach((e=>{He.appendChild(e)}));const m=document.createTextNode(o);function y(){return He.offsetHeight<=d}if(He.insertBefore(m,v),y())return{ellipsis:!1,text:o};return function e(t,a=0,l=o.length,n=0){const i=Math.floor((a+l)/2),s=o.slice(0,i);if(t.textContent=s,a>=l-1)for(let r=l;r>=a;r-=1){const e=o.slice(0,r);if(t.textContent=e,y()||!e)return}y()?e(t,i,l,i):e(t,a,i,n)}(m),{text:m.textContent,ellipsis:!0}};let Ge;var Ke=Object.defineProperty,Je=Object.defineProperties,Ze=Object.getOwnPropertyDescriptors,Qe=Object.getOwnPropertySymbols,Xe=Object.prototype.hasOwnProperty,et=Object.prototype.propertyIsEnumerable,tt=(e,t,a)=>t in e?Ke(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a;function at(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!ee(e)}function ot(e,t){const{mark:a}=e,o=function(e){const{bold:t,mark:a,underline:o,delete:l,code:n}=e,i=[];return t&&i.push("b"),o&&i.push("u"),l&&i.push("del"),n&&i.push("code"),a&&i.push("mark"),i}(e),l=C(a)&&a.color?{backgroundColor:a.color}:{};return o.reduce(((e,t)=>z(t,"mark"===t?{style:l}:{},at(e)?e:{default:()=>[e]})),t)}function lt(e){const t=!!e.showTooltip,a=C(e.showTooltip)&&"popover"===e.showTooltip.type?ue:i,o=C(e.showTooltip)&&e.showTooltip.props||{};return l=((e,t)=>{for(var a in t||(t={}))Xe.call(t,a)&&tt(e,a,t[a]);if(Qe)for(var a of Qe(t))et.call(t,a)&&tt(e,a,t[a]);return e})({rows:1,suffix:"",ellipsisStr:"...",expandable:!1,css:!1},p(e,["showTooltip"])),Je(l,Ze({showTooltip:t,TooltipComponent:a,tooltipProps:o}));var l}var nt=E({name:"TypographyBase",inheritAttrs:!1,props:{component:{type:String,required:!0},type:{type:String},bold:{type:Boolean},mark:{type:[Boolean,Object],default:!1},underline:{type:Boolean},delete:{type:Boolean},code:{type:Boolean},disabled:{type:Boolean},editable:{type:Boolean},editing:{type:Boolean,default:void 0},defaultEditing:{type:Boolean},editText:{type:String},copyable:{type:Boolean},copyText:{type:String},copyDelay:{type:Number,default:3e3},ellipsis:{type:[Boolean,Object],default:!1},editTooltipProps:{type:Object},copyTooltipProps:{type:Object}},emits:{editStart:()=>!0,change:e=>!0,"update:editText":e=>!0,editEnd:()=>!0,"update:editing":e=>!0,copy:e=>!0,ellipsis:e=>!0,expand:e=>!0},setup(e,{slots:t,emit:a,attrs:o}){const{editing:l,defaultEditing:n,ellipsis:i,copyable:s,editable:p,copyText:c,editText:f,copyDelay:m,component:y}=$(e),h=v("typography"),x=k((()=>[h,{[`${h}-${e.type}`]:e.type,[`${h}-disabled`]:e.disabled}])),g=_(),b=_(""),[w,S]=r(n.value,Z({value:l})),D=k((()=>p.value&&w.value));function E(){a("update:editing",!0),a("editStart"),S(!0)}function j(){w.value&&(a("update:editing",!1),a("editEnd"),S(!1))}const M=_(!1);let O=null;function B(){var e;const t=null!=(e=c.value)?e:b.value;(async e=>{var t;if(null==(t=navigator.clipboard)?void 0:t.writeText)try{return void(await navigator.clipboard.writeText(e))}catch(n){console.error(null!=n?n:new DOMException("The request is not allowed","NotAllowedError"))}const a=document.createElement("span");a.textContent=e,a.style.whiteSpace="pre",document.body.appendChild(a);const o=window.getSelection(),l=window.document.createRange();null==o||o.removeAllRanges(),l.selectNode(a),null==o||o.addRange(l);try{window.document.execCommand("copy")}catch(n){console.error(`execCommand Error: ${n}`)}null==o||o.removeAllRanges(),window.document.body.removeChild(a)})(t||""),M.value=!0,a("copy",t),O=setTimeout((()=>{M.value=!1}),m.value)}Q((()=>{O&&clearTimeout(O),O=null}));const F=_(!1),L=_(!1),P=_(""),N=k((()=>lt(C(i.value)&&i.value||{})));let V=null;function A(){const e=!L.value;L.value=e,a("expand",e)}function I(a=!1){return N.value.css?z(Ue,{editable:p.value,copyable:s.value,expandable:N.value.expandable,isCopied:M.value,isEllipsis:q.value,expanded:L.value,forceRenderExpand:a||L.value,editTooltipProps:e.editTooltipProps,copyTooltipProps:e.copyTooltipProps,onEdit:E,onCopy:B,onExpand:A},{"copy-tooltip":t["copy-tooltip"],"copy-icon":t["copy-icon"],"expand-node":t["expand-node"]}):z(Ue,{editable:p.value,copyable:s.value,expandable:N.value.expandable,isCopied:M.value,isEllipsis:F.value,expanded:L.value,forceRenderExpand:a,editTooltipProps:e.editTooltipProps,copyTooltipProps:e.copyTooltipProps,onEdit:E,onCopy:B,onExpand:A},{"copy-tooltip":t["copy-tooltip"],"copy-icon":t["copy-icon"],"expand-node":t["expand-node"]})}function R(){i.value&&!L.value&&(u(V),V=d((()=>{!function(){if(!g.value)return;const{ellipsis:e,text:t}=qe(g.value,N.value,I(!!N.value.expandable),b.value);F.value!==e&&(F.value=e,N.value.css||a("ellipsis",e)),P.value!==t&&(P.value=t||"")}()})))}Q((()=>{u(V)})),T((()=>N.value.rows),(()=>{R()})),T(i,(e=>{e?R():F.value=!1}));let U=[];const H=()=>{if(i.value||s.value||p.value){const e=function(e){if(!e)return"";Ge||(Ge=document.createElement("div"),Ge.setAttribute("aria-hidden","true"),document.body.appendChild(Ge));const t=J({render:()=>z("div",null,[e])});t.mount(Ge);const a=Ge.innerText;return t.unmount(),a}(U);e!==b.value&&(b.value=e,R())}};Y(H),X(H);const W=_(),q=_(!1),K=()=>{if(g.value&&W.value){const e=W.value.offsetHeight>g.value.offsetHeight;e!==q.value&&(q.value=e,a("ellipsis",e))}},ee=k((()=>L.value?{}:{overflow:"hidden","text-overflow":"ellipsis",display:"-webkit-box","-webkit-line-clamp":N.value.rows,"-webkit-box-orient":"vertical"}));return()=>{var l,n;if(U=(null==(l=t.default)?void 0:l.call(t))||[],D.value){const e=null!=(n=f.value)?n:b.value;return z(ze,{text:e,onChange:t=>{t!==e&&function(e){a("update:editText",e),a("change",e)}(t)},onEnd:j},null)}const{suffix:i,ellipsisStr:s,showTooltip:r,tooltipProps:u,TooltipComponent:p}=N.value,d=F.value&&!L.value,c=d&&!r?{title:b.value}:{},v=y.value;if(N.value.css){const t=ot(e,U),a=z(v,G({class:x.value,ref:g,style:ee.value},c,o),{default:()=>[z("span",{ref:W},[t])]});return q.value?z(p,G(u,{onResize:()=>K()}),{default:()=>[a],content:()=>b.value}):z(re,{onResize:()=>{K()}},at(a)?a:{default:()=>[a]})}const m=ot(e,d?P.value:U);return z(re,{onResize:()=>R()},{default:()=>[z(v,G({class:x.value,ref:g},c,o),{default:()=>[d&&r?z(p,u,{default:()=>[z("span",null,[m])],content:()=>b.value}):m,d?s:null,i,I()]})]})}}}),it=E({name:"TypographyTitle",inheritAttrs:!1,props:{heading:{type:Number,default:1}},setup(e){const{heading:t}=$(e);return{component:k((()=>`h${null==t?void 0:t.value}`))}},render(){const{component:e}=this;return z(nt,G(this.$attrs,{component:e}),this.$slots)}});const st={class:"icon"},rt={class:"text"},ut=me(E({__name:"fastEnter",setup(t){const l=te(),n=[{title:"店铺审核",icon:"icon-building",name:"StoreApprove"},{title:"商品审核",icon:"gift",name:"CommodityApprove"},{title:"订单管理",icon:"bookmark",name:"Orders"},{title:"公告发布",icon:"folder",name:"Announcements"}];return(t,i)=>{const s=it,r=ye,u=a,p=o,d=e;return j(),H(d,{bordered:!1},{title:W((()=>[z(s,{style:{margin:"6px 0",fontSize:"18px"},heading:5},{default:W((()=>i[0]||(i[0]=[F("快捷入口")]))),_:1})])),default:W((()=>[z(p,{gutter:16},{default:W((()=>[(j(),M(A,null,ae(n,((e,t)=>z(u,{key:t,span:6,class:"item",onClick:()=>{oe(l).push({name:e.name})}},{default:W((()=>[N("div",st,[z(r,{icon:e.icon},null,8,["icon"])]),N("p",rt,L(e.title),1)])),_:2},1032,["onClick"]))),64))])),_:1})])),_:1})}}}),[["__scopeId","data-v-0b8d69d0"]]),pt=E({__name:"index",props:{options:{},autoResize:{type:Boolean,default:!0},width:{default:"100%"},height:{default:"100%"}},emits:["mouseover","mouseout"],setup(e,{expose:t,emit:a}){xe([ge,be,we,Ce,Se,De,Ee,ke,_e]);const o=a,l=_(!1);le((()=>{l.value=!0}));const n=_();return t({vChartRef:n}),(e,t)=>l.value?(j(),H(oe($e),{key:0,ref_key:"vChartRef",ref:n,option:e.options,autoresize:e.autoResize,style:V({width:e.width,height:e.height}),onMouseover:t[0]||(t[0]=e=>o("mouseover",e)),onMouseout:t[1]||(t[1]=e=>o("mouseout",e))},null,8,["option","autoresize","style"])):P("",!0)}});function dt(e){const t=he(),a=k((()=>"dark"===t.theme));return{chartOption:k((()=>e(a.value)))}}const{lastWeekStartDate:ct,lastWeekEndDate:ft,weekStartDate:vt,weekEndDate:mt,lastMonthStartDate:yt,lastMonthEndDate:ht,monthStartDate:xt,monthEndDate:gt}=(()=>{const e=new Date,t=e.getDay(),a=e.getDate(),o=e.getMonth();let l=e.getFullYear();l+=l<2e3?1900:0;let n=new Date;n.setDate(1),n.setMonth(n.getMonth()-1);const i=n.getFullYear(),s=n.getMonth(),r=e=>{const t=new Date(l,e,1).getTime();return(new Date(l,e+1,1).getTime()-t)/864e5},u=()=>{var e=0;return o<3&&(e=0),2<o&&o<6&&(e=3),5<o&&o<9&&(e=6),o>8&&(e=9),e};return{weekStartDate:new Date(l,o,a-t),weekEndDate:new Date(l,o,a+(6-t)),lastWeekStartDate:new Date(l,o,a-t-7),lastWeekEndDate:new Date(l,o,a-t-1),monthStartDate:new Date(l,o,1),monthEndDate:new Date(l,o,r(o)),lastMonthStartDate:new Date(i,s,1),lastMonthEndDate:new Date(i,s,r(s)),quarterStartDate:new Date(l,u(),1),quarterEndDate:new Date(l,u()+2,r(u()+2))}})(),bt=()=>{const e=_([]),t=_(void 0),a=_([ne(ct,"YYYY-MM-DD").value,ne(ft,"YYYY-MM-DD").value]),o=_(0),l=_(!0);return{statisticList:e,getStatistics:async()=>{try{const t=await S({url:"/admin/platform/statistic",method:"get"});e.value=t.data}catch(t){}},storeId:t,rangeDate:a,getOrderChart:()=>new Promise((async(e,t)=>{try{const t=await S({url:"/admin/platform/orderChart",method:"get"});l.value=!1,e(t.data)}catch(a){t()}})),getUserChart:()=>new Promise((async(e,t)=>{try{const t=await S({url:"/admin/platform/userChart",method:"get"});l.value=!1,e({totalUserCount:t.data.totalUserCount,onlineUserCount:t.data.onlineUserCount,offlineUserCount:t.data.totalUserCount-t.data.onlineUserCount})}catch(a){t()}})),getFinanceChart:e=>{switch(e){case 0:a.value[0]=pe().subtract(30,"day").format("YYYY-MM-DD"),a.value[1]=pe().format("YYYY-MM-DD");break;case 1:a.value[0]=pe().subtract(12,"month").format("YYYY-MM-DD"),a.value[1]=pe().format("YYYY-MM-DD")}return new Promise((async(e,o)=>{try{const o=[],i=await(n={storeId:t.value,startDate:a.value[0],endDate:a.value[1]},S({url:"/admin/platform/financeChart",method:"get",params:n}));i.data.forEach((e=>{o.push({x:e.label,y:e.value})})),l.value=!o.length,e(o)}catch(i){o()}var n}))},getVisitorChart:e=>{switch(e){case 0:a.value[0]=ne(ct,"YYYY-MM-DD").value,a.value[1]=ne(ft,"YYYY-MM-DD").value;break;case 1:a.value[0]=ne(vt,"YYYY-MM-DD").value,a.value[1]=ne(mt,"YYYY-MM-DD").value;break;case 2:a.value[0]=ne(yt,"YYYY-MM-DD").value,a.value[1]=ne(ht,"YYYY-MM-DD").value;break;case 3:a.value[0]=ne(xt,"YYYY-MM-DD").value,a.value[1]=ne(gt,"YYYY-MM-DD").value}return new Promise((async(e,t)=>{try{const t=[],n=await(o={startDate:a.value[0],endDate:a.value[1]},S({url:"/admin/platform/visitorChart",method:"get",params:o}));n.data.forEach((e=>{t.push({x:e.label,y:e.value})})),l.value=!t.length,e(t)}catch(n){t()}var o}))},checked:o,emptyChart:l}},wt=me(E({__name:"financeChart",setup(a){const{storeOptions:o,initStoreOptions:l}=Fe(),{storeId:n,rangeDate:i,checked:s,getFinanceChart:r,emptyChart:u}=bt();function p(e){return{type:"text",bottom:"8",...e,style:{text:"",textAlign:"center",fill:"#4E5969",fontSize:12}}}const{loading:d,setLoading:f}=Oe(!0),v=_([]),m=_([]),y=_([p({left:"2.6%"}),p({right:0})]),{chartOption:h}=dt((()=>({grid:{left:"2.6%",right:"0",top:"10",bottom:"30"},xAxis:{type:"category",offset:2,data:v.value,boundaryGap:!1,axisLabel:{color:"#4E5969",formatter:(e,t)=>0===t||t===v.value.length-1?"":`${e}`},axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!0,interval:e=>0!==e&&e!==v.value.length-1,lineStyle:{color:"#E5E8EF"}},axisPointer:{show:!0,lineStyle:{color:"#23ADFF",width:2}}},yAxis:{type:"value",axisLine:{show:!1},axisLabel:{formatter:(e,t)=>0===t?e:`${e}`},splitLine:{show:!0,lineStyle:{type:"dashed",color:"#E5E8EF"}}},tooltip:{trigger:"axis",formatter(e){const[t]=e;return`<div>\n            <p class="tooltip-title">${t.axisValueLabel}</p>\n            <div class="content-panel"><span>营收</span><span class="tooltip-value">${Number(t.value).toLocaleString()} 元</span></div>\n          </div>`},className:"echarts-tooltip-diy"},graphic:{elements:y.value},series:[{data:m.value,type:"line",smooth:!0,symbolSize:12,emphasis:{focus:"series",itemStyle:{borderWidth:2}},lineStyle:{width:3,color:new Ye(0,0,1,0,[{offset:0,color:"rgba(30, 231, 255, 1)"},{offset:.5,color:"rgba(36, 154, 255, 1)"},{offset:1,color:"rgba(111, 66, 251, 1)"}])},showSymbol:!1,areaStyle:{opacity:.8,color:new Ye(0,0,0,1,[{offset:0,color:"rgba(17, 126, 255, 0.16)"},{offset:1,color:"rgba(17, 128, 255, 0)"}])}}]}))),x=e=>{v.value=[],m.value=[],e.forEach(((t,a)=>{v.value.push(t.x),m.value.push(t.y),0===a&&(y.value[0].style.text=t.x),a===e.length-1&&(y.value[1].style.text=t.x)}))},g=async e=>{f(!0);try{const t=await r(e);x(t)}finally{f(!1)}},b=async()=>{s.value=-1,f(!0);try{const e=await r();x(e)}finally{f(!1)}};return g(0),Y((()=>{l()})),(a,l)=>{const r=Te,p=je,f=Me,v=ce,m=Be,y=c,x=pt,w=e,C=t;return j(),H(C,{loading:oe(d)},{default:W((()=>[z(w,{bordered:!1,title:"营收趋势图"},{extra:W((()=>[z(m,null,{default:W((()=>[z(p,{type:"button",modelValue:oe(s),"onUpdate:modelValue":l[0]||(l[0]=e=>ie(s)?s.value=e:null),onChange:g},{default:W((()=>[z(r,{value:0},{default:W((()=>l[3]||(l[3]=[F("近30天")]))),_:1}),z(r,{value:1},{default:W((()=>l[4]||(l[4]=[F("近12个月")]))),_:1})])),_:1},8,["modelValue"]),z(f,{style:{width:"250px"},modelValue:oe(n),"onUpdate:modelValue":l[1]||(l[1]=e=>ie(n)?n.value=e:null),options:oe(o),placeholder:`${a.$selectPlaceholder}店铺名称`,onChange:b,"allow-search":"","allow-clear":""},null,8,["modelValue","options","placeholder"]),z(v,{style:{width:"380px"},modelValue:oe(i),"onUpdate:modelValue":l[2]||(l[2]=e=>ie(i)?i.value=e:null),onChange:b,"allow-clear":!1},null,8,["modelValue"])])),_:1})])),default:W((()=>[z(se,{name:"fade",mode:"out-in"},{default:W((()=>[oe(u)?(j(),H(y,{key:0})):(j(),H(x,{key:1,height:"289px",option:oe(h)},null,8,["option"]))])),_:1})])),_:1})])),_:1},8,["loading"])}}}),[["__scopeId","data-v-24affce9"]]),Ct=me(E({__name:"orderChart",setup(a){const{getOrderChart:o,emptyChart:l}=bt(),{loading:n,setLoading:i}=Oe(!1),s=_([]),r=_([]),u=_([]),{chartOption:p}=dt((e=>({grid:{left:"4%",right:0,top:"20",bottom:"20"},xAxis:{type:"category",data:s.value,axisLine:{lineStyle:{color:e?"#3f3f3f":"#A9AEB8"}},axisTick:{show:!0,alignWithLabel:!0,lineStyle:{color:"#86909C"}},axisLabel:{color:"#86909C"}},yAxis:{type:"value",axisLabel:{color:"#86909C",formatter:(e,t)=>`${e} 单`},splitLine:{lineStyle:{color:e?"#3F3F3F":"#E5E6EB"}}},tooltip:{show:!0,trigger:"axis",formatter(e){const[t]=e;return`<div>\n            <p class="tooltip-title">${t.axisValueLabel}</p>\n            ${a=e,a.map((e=>`\n          <div class="content-panel">\n            <p>\n              <span style="background-color: ${e.color}" class="tooltip-item-icon"></span>\n              <span>\n                ${0===e.seriesIndex?"快递发货":"线下核销"}\n              </span>\n            </p>\n            <span class="tooltip-value">\n              ${Number(e.value).toLocaleString()} 单\n            </span>\n          </div>\n        `)).join("")}\n          </div>`;var a},className:"echarts-tooltip-diy"},series:[{name:"7日订单统计",data:r.value,type:"bar",barWidth:32,color:e?"#085FEF":"#00B2FF"},{name:"7日订单统计",data:u.value,type:"bar",barWidth:32,color:e?"#00B2FF":"#085FEF"}]})));return(async()=>{i(!0);try{const t=await o();e=t,s.value=e.map((e=>e.name)),r.value=e.map((e=>e.value1)),u.value=e.map((e=>e.value2))}finally{i(!1)}var e})(),(a,o)=>{const i=c,s=pt,r=e,u=t;return j(),H(u,{loading:oe(n)},{default:W((()=>[z(r,{bordered:!1,title:"7日订单统计"},{default:W((()=>[z(se,{name:"fade",mode:"out-in"},{default:W((()=>[oe(l)?(j(),H(i,{key:0})):(j(),H(s,{key:1,height:"289px",option:oe(p)},null,8,["option"]))])),_:1})])),_:1})])),_:1},8,["loading"])}}}),[["__scopeId","data-v-019a932b"]]),St={class:"unit"},Dt=me(E({__name:"statistic",props:{list:{}},setup(t){const a=D(),o=k((()=>a.getNickName));return(t,a)=>{const l=it,n=Re,i=fe,s=ve,r=e;return j(),H(r,{bordered:!1},{title:W((()=>[z(l,{style:{margin:"6px 0",fontSize:"18px"},heading:5},{default:W((()=>[F("欢迎回来！"+L(oe(o)),1)])),_:1})])),default:W((()=>[z(s,{cols:5,"row-gap":16,class:"panel"},{default:W((()=>[(j(!0),M(A,null,ae(t.list,((e,t)=>(j(),H(i,{class:"panel-col",span:{xs:5,sm:5,lg:1},key:t},{default:W((()=>[z(n,{title:e.label,value:e.value,"value-from":0,animation:"","show-group-separator":""},{suffix:W((()=>[N("span",St,L(e.unit),1)])),_:2},1032,["title","value"])])),_:2},1024)))),128))])),_:1})])),_:1})}}}),[["__scopeId","data-v-f6012075"]]),Et=me(E({__name:"userChart",setup(t){const{getUserChart:a,emptyChart:o}=bt(),{loading:l,setLoading:n}=Oe(!0),i=_(0),s=_(0),r=_(0),{chartOption:u}=dt((()=>({legend:{left:"center",data:["活跃用户数","非活跃用户数"],bottom:0,icon:"circle",itemWidth:8,textStyle:{color:"#4E5969"},itemStyle:{borderWidth:0}},tooltip:{show:!0,trigger:"item"},graphic:{elements:[{type:"text",left:"center",top:"45%",style:{text:"用户总数",textAlign:"center",fill:"#4E5969",fontSize:14}},{type:"text",left:"center",top:"55%",style:{text:`${i.value}`,textAlign:"center",fill:"#1D2129",fontSize:18,fontWeight:500}}]},series:[{type:"pie",radius:["50%","70%"],center:["50%","50%"],label:{formatter:"{d}%",fontSize:14,color:"#4E5969"},itemStyle:{borderColor:"#fff",borderWidth:1},data:[{value:[r.value],name:"非活跃用户数",itemStyle:{color:"#21CCFF"}},{value:[s.value],name:"活跃用户数",itemStyle:{color:"#249EFF"}}]}]})));return(async()=>{n(!0);try{(e=>{i.value=e.totalUserCount,s.value=e.onlineUserCount,r.value=e.offlineUserCount})(await a())}finally{n(!1)}})(),(t,a)=>{const l=c,n=pt,i=e;return j(),H(i,{bordered:!1,title:"用户统计"},{default:W((()=>[z(se,{name:"fade",mode:"out-in"},{default:W((()=>[oe(o)?(j(),H(l,{key:0})):(j(),H(n,{key:1,height:"310px",option:oe(u)},null,8,["option"]))])),_:1})])),_:1})}}}),[["__scopeId","data-v-454a5709"]]),kt=me(E({__name:"visitorChart",setup(a){const{rangeDate:o,checked:l,getVisitorChart:n,emptyChart:i}=bt();function s(e){return{type:"text",bottom:"8",...e,style:{text:"",textAlign:"center",fill:"#4E5969",fontSize:12}}}const{loading:r,setLoading:u}=Oe(!0),p=_([]),d=_([]),f=_([s({left:"2.6%"}),s({right:0})]),{chartOption:v}=dt((()=>({grid:{left:"2.6%",right:"0",top:"10",bottom:"30"},xAxis:{type:"category",offset:2,data:p.value,boundaryGap:!1,axisLabel:{color:"#4E5969",formatter:(e,t)=>0===t||t===p.value.length-1?"":`${e}`},axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!0,interval:e=>0!==e&&e!==p.value.length-1,lineStyle:{color:"#E5E8EF"}},axisPointer:{show:!0,lineStyle:{color:"#23ADFF",width:2}}},yAxis:{type:"value",axisLine:{show:!1},axisLabel:{formatter:(e,t)=>0===t?e:`${e}`},splitLine:{show:!0,lineStyle:{type:"dashed",color:"#E5E8EF"}}},tooltip:{trigger:"axis",formatter(e){const[t]=e;return`<div>\n            <p class="tooltip-title">${t.axisValueLabel}</p>\n            <div class="content-panel"><span>访客</span><span class="tooltip-value">${Number(t.value).toLocaleString()}</span></div>\n          </div>`},className:"echarts-tooltip-diy"},graphic:{elements:f.value},series:[{data:d.value,type:"line",smooth:!0,symbolSize:12,emphasis:{focus:"series",itemStyle:{borderWidth:2}},lineStyle:{width:3,color:new Ye(0,0,1,0,[{offset:0,color:"rgba(30, 231, 255, 1)"},{offset:.5,color:"rgba(36, 154, 255, 1)"},{offset:1,color:"rgba(111, 66, 251, 1)"}])},showSymbol:!1,areaStyle:{opacity:.8,color:new Ye(0,0,0,1,[{offset:0,color:"rgba(17, 126, 255, 0.16)"},{offset:1,color:"rgba(17, 128, 255, 0)"}])}}]}))),m=e=>{p.value=[],d.value=[],e.forEach(((t,a)=>{p.value.push(t.x),d.value.push(t.y),0===a&&(f.value[0].style.text=t.x),a===e.length-1&&(f.value[1].style.text=t.x)}))},y=async e=>{u(!0);try{const t=await n(e);m(t)}finally{u(!1)}},h=async()=>{l.value=-1,u(!0);try{const e=await n();m(e)}finally{u(!1)}};return y(0),(a,n)=>{const s=Te,u=je,p=ce,d=Be,f=c,m=pt,x=e,g=t;return j(),H(g,{loading:oe(r)},{default:W((()=>[z(x,{bordered:!1,title:"访客变化图"},{extra:W((()=>[z(d,null,{default:W((()=>[z(u,{type:"button",modelValue:oe(l),"onUpdate:modelValue":n[0]||(n[0]=e=>ie(l)?l.value=e:null),onChange:y},{default:W((()=>[z(s,{value:0},{default:W((()=>n[2]||(n[2]=[F("上周")]))),_:1}),z(s,{value:1},{default:W((()=>n[3]||(n[3]=[F("本周")]))),_:1}),z(s,{value:2},{default:W((()=>n[4]||(n[4]=[F("上月")]))),_:1}),z(s,{value:3},{default:W((()=>n[5]||(n[5]=[F("本月")]))),_:1})])),_:1},8,["modelValue"]),z(p,{style:{width:"380px"},modelValue:oe(o),"onUpdate:modelValue":n[1]||(n[1]=e=>ie(o)?o.value=e:null),onChange:h,"allow-clear":!1},null,8,["modelValue"])])),_:1})])),default:W((()=>[z(se,{name:"fade",mode:"out-in"},{default:W((()=>[oe(i)?(j(),H(f,{key:0})):(j(),H(m,{key:1,height:"289px",option:oe(v)},null,8,["option"]))])),_:1})])),_:1})])),_:1},8,["loading"])}}}),[["__scopeId","data-v-8a956a37"]]),_t={class:"page-container"},$t={class:"flex gap-x-[18px]"},Yt={class:"h-full flex flex-1 flex-col gap-y-[18px]"},Tt={class:"w-[426px] h-full flex flex-col gap-y-[18px]"},jt={class:"mt-[18px]"},Mt={class:"mt-[18px]"},Ot=E({__name:"index",setup(e){const{statisticList:t,getStatistics:a}=bt();return a(),(e,a)=>(j(),M("div",_t,[N("div",$t,[N("div",Yt,[z(Dt,{list:oe(t)},null,8,["list"]),z(Ct)]),N("div",Tt,[z(ut),z(Et)])]),N("div",jt,[z(wt)]),N("div",Mt,[z(kt)])]))}});export{Ot as default};

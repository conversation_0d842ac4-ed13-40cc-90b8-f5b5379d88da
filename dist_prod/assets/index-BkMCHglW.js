const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/info-B-VlI-_c.js","assets/index-DOhy6BH_.js","assets/index-D-8JbLQk.js","assets/vue-D-10XvVk.js","assets/index-DxPaQOvH.css","assets/index-DB09tZwb.css","assets/index-BEo1tUsK.js","assets/pick-Ccd8Sfcm.js","assets/index-6rnfXikd.css","assets/index-DVDXfQhn.js","assets/index-DGtjsHgS.js","assets/index-BJBnsrKF.css","assets/index-DDFSMqsG.js","assets/ResizeObserver.es-CzGuHLZU.js","assets/index-RZyF5P1Y.css","assets/index-8er2yjEK.css","assets/index-Cuq5XRs0.js","assets/resize-observer-Dtogi-DJ.js","assets/index-DD6vSYIM.js","assets/index-C0ni2jp2.css","assets/index-CdWxsKz_.js","assets/index-CX9L_GU1.css","assets/index-DQjhgQFu.js","assets/index-Db7LPRu1.css","assets/index-Dbgee0nK.css","assets/useCommon-BuUbRw8e.js","assets/apiCommon-DcubqwY_.js","assets/index-DdMaxvYa.js","assets/index-DfEXMvnc.js","assets/use-children-components-v8i8lsOx.js","assets/index-B5FzkxT_.css","assets/index-DmW4RN1x.js","assets/index-Bl_vBcmJ.css","assets/index-CUtvFEc_.js","assets/use-index-D_ozg7PK.js","assets/index-CHOaln3D.js","assets/index-komh9C6_.css","assets/useLoading-D5mh7tTu.js","assets/usePagination-Dd_EW2BO.js","assets/apiStore-vHDpafS2.js","assets/dayjs.min-Daes5FZc.js","assets/index-O7pr3qsq.js","assets/index-BlQqQ5bI.css","assets/index-CJ6Fn8S6.css"])))=>i.map(i=>d[i]);
import{N as e,m as a}from"./index-D-8JbLQk.js";import"./index-DOhy6BH_.js";import{C as t}from"./index-CdWxsKz_.js";import"./index-DDFSMqsG.js";import{L as o}from"./index-Cuq5XRs0.js";import"./index-DD6vSYIM.js";import"./index-DQjhgQFu.js";import{T as s,a as i,P as l}from"./index-DdMaxvYa.js";import{B as n,S as r}from"./index-DGtjsHgS.js";import{D as d}from"./index-DmW4RN1x.js";/* empty css              */import{A as c}from"./index-CUtvFEc_.js";import{C as u,R as p}from"./index-BEo1tUsK.js";/* empty css              */import{B as m,r as f,c as g,d as h,K as v,o as y,k as w,A as x,z as _,u as S,e as j,q as C,N as k,f as b,j as z,y as I,p as P,I as O,J as R,af as T,M as q,h as A}from"./vue-D-10XvVk.js";import{u as M}from"./useCommon-BuUbRw8e.js";import{u as V}from"./useLoading-D5mh7tTu.js";import{u as B}from"./usePagination-Dd_EW2BO.js";import{r as D,a as L,b as Y,c as E}from"./apiStore-vHDpafS2.js";import{d as U}from"./dayjs.min-Daes5FZc.js";import{M as H}from"./index-O7pr3qsq.js";import{I as N}from"./index-DfEXMvnc.js";const J=()=>{const{loading:e,setLoading:a}=V(),{pagination:t}=B(),o=m({}),s=f(void 0),i=g((()=>s.value?r.value.find((e=>e.id===s.value)):null)),l=f([]),n=g((()=>l.value.length?r.value.filter((e=>l.value.includes(e.id))):[])),r=f([]),d=async()=>{a(!0),s.value=void 0,l.value=[];try{const{data:e}=await D();r.value=e.rows.map((e=>(e.createTime=U(e.createTime).format("YYYY-MM-DD HH:mm:ss"),e))),t.total=e.total,a(!1)}catch(e){r.value=[],t.total=0,a(!1)}},c=m({storeId:void 0});return{loading:e,queryParams:o,pagination:t,rows:r,selectedId:s,selectedRow:i,selectedIds:l,selectedRows:n,selectAll:e=>{l.value=e?r.value.map((e=>e.id)):[]},rowSelect:(e,a,t)=>{l.value.includes(t.id)?l.value.splice(l.value.indexOf(t.id),1):l.value.push(t.id)},rowClick:e=>{l.value.includes(e.id)?l.value.splice(l.value.indexOf(e.id),1):l.value.push(e.id)},query:d,reset:()=>{t.current=1,Object.assign(o,{}),d()},pageChange:async e=>{t.current=e,d()},pageSizeChange:async e=>{t.current=1,t.pageSize=e,d()},form:c,add:async e=>{try{await L({...e})}catch(a){throw a}},del:async e=>{try{await Y(e)}catch(a){throw a}},setSortIndex:async(e,a)=>{try{await E(e,a)}catch(t){throw t}}}},K={class:"page-container"},$={class:"h-full flex flex-col gap-[18px]"},F=["src"],G={key:1},Q=h({__name:"index",setup(m){const g=q((()=>a((()=>import("./info-B-VlI-_c.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43]))));M();const{loading:h,queryParams:V,pagination:B,rows:D,selectedId:L,selectedIds:Y,selectAll:E,rowSelect:U,rowClick:Q,query:W,reset:X,pageChange:Z,pageSizeChange:ee,add:ae,del:te,setSortIndex:oe}=J(),se=v("addRef"),ie=f(!1),le=async a=>{var t,o;try{if(await(null==(o=null==(t=se.value)?void 0:t.formRef)?void 0:o.validate()))throw new Error("校验失败");await ae(k(se.value.form)),e.success({title:"成功提示",content:"已添加",duration:1500}),a(!0),W()}catch(s){a(!1)}};return y((()=>{Z(1)})),(a,m)=>{const f=H,v=b("icon-plus"),y=n,k=r,q=u,M=p,V=s,Y=c,E=N,U=b("icon-check"),J=b("icon-close"),Q=d,X=o,ae=i,ne=l,re=t;return z(),w("div",K,[x(f,{visible:S(ie),"onUpdate:visible":m[0]||(m[0]=e=>j(ie)?ie.value=e:null),"title-align":"start",title:"添加","cancel-button-props":{type:"outline"},"unmount-on-close":"","mask-closable":!1,"esc-to-close":!1,"on-before-ok":le,onCancel:m[1]||(m[1]=e=>ie.value=!1)},{default:_((()=>[x(S(g),{ref_key:"addRef",ref:se},null,512)])),_:1},8,["visible"]),C("div",$,[x(re,{bordered:!1,class:"flex-1 overflow-y-hidden","body-style":{height:"100%"}},{actions:_((()=>[S(B).total?(z(),I(ne,{key:0,current:S(B).current,"page-size":S(B).pageSize,"show-total":S(B).showTotal,"show-page-size":S(B).showPageSize,"page-size-options":S(B).pageSizeOptions,total:S(B).total,onChange:S(Z),onPageSizeChange:S(ee)},null,8,["current","page-size","show-total","show-page-size","page-size-options","total","onChange","onPageSizeChange"])):P("",!0)])),default:_((()=>[x(M,{class:"mb-[12px]"},{default:_((()=>[x(q,{span:16},{default:_((()=>[x(k,null,{default:_((()=>[x(y,{type:"primary",onClick:m[2]||(m[2]=()=>{L.value=void 0,ie.value=!0})},{icon:_((()=>[x(v)])),default:_((()=>[m[3]||(m[3]=O(" 添加 "))])),_:1})])),_:1})])),_:1})])),_:1}),x(ae,{size:"large","row-key":"id",loading:S(h),pagination:!1,data:S(D),bordered:{cell:!0},scroll:{y:"calc(100% - 96px)"}},{columns:_((()=>[x(V,{align:"center",title:"序号",width:80},{cell:_((({rowIndex:e})=>[O(R(S(B).pageSize*(S(B).current-1)+e+1),1)])),_:1}),x(V,{align:"center",title:"店铺名称",width:200,ellipsis:"",tooltip:""},{cell:_((({record:e})=>[x(k,null,{default:_((()=>[x(Y,{size:32},{default:_((()=>[C("img",{src:e.logo},null,8,F)])),_:2},1024),C("span",null,R(e.name),1)])),_:2},1024)])),_:1}),x(V,{align:"center",title:"排序",width:160},{cell:_((({record:a})=>[C("div",null,[a.showSetSortIndex?(z(),I(k,{key:0},{default:_((()=>[x(E,{modelValue:a.recommendSortIndex,"onUpdate:modelValue":e=>a.recommendSortIndex=e,"hide-button":"",min:1,max:9999,disabled:!a.showSetSortIndex},null,8,["modelValue","onUpdate:modelValue","disabled"]),x(y,{type:"primary",onClick:T((t=>(async a=>{try{await oe(a.id,a.recommendSortIndex),e.success({title:"成功提示",content:"已设置顺序",duration:1500}),W()}catch(t){}})(a)),["stop"])},{icon:_((()=>[x(U)])),_:2},1032,["onClick"]),x(y,{type:"primary",status:"warning",onClick:T(S(W),["stop"])},{icon:_((()=>[x(J)])),_:1},8,["onClick"])])),_:2},1024)):(z(),w("span",G,R(a.recommendSortIndex),1))])])),_:1}),x(V,{align:"center",title:"操作",width:200,fixed:"right"},{cell:_((({record:a})=>[x(k,null,{split:_((()=>[x(Q,{direction:"vertical"})])),default:_((()=>[x(X,{disabled:S(D).some((e=>e.showSetSortIndex)),onClick:T((()=>{a.showSetSortIndex=!0}),["stop"])},{default:_((()=>m[4]||(m[4]=[O(" 排序 ")]))),_:2},1032,["disabled","onClick"]),x(X,{status:"danger",onClick:T((t=>(a=>{try{H.warning({title:"提示",content:()=>A("div",{class:"text-center"},`确定从金刚区中删除【${a.name}】？`),maskClosable:!1,escToClose:!1,hideCancel:!1,cancelButtonProps:{type:"outline"},onBeforeOk:async t=>{try{await te(a.id),e.success({title:"成功提示",content:"已删除",duration:1500}),t(!0),W()}catch(o){t(!1)}}})}catch(t){}})(a)),["stop"])},{default:_((()=>m[5]||(m[5]=[O("删除")]))),_:2},1032,["onClick"])])),_:2},1024)])),_:1})])),_:1},8,["loading","data"])])),_:1})])])}}}),W=Object.freeze(Object.defineProperty({__proto__:null,default:Q},Symbol.toStringTag,{value:"Module"}));export{W as i,J as u};

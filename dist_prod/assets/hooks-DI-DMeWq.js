import{u as e}from"./useLoading-D5mh7tTu.js";import{u as a}from"./usePagination-Dd_EW2BO.js";import{O as o,Q as i,R as t,S as r,T as d,N as s}from"./index-D-8JbLQk.js";import{d as n}from"./dayjs.min-Daes5FZc.js";import{B as l,r as m,c as v}from"./vue-D-10XvVk.js";const u=u=>{const{loading:c,setLoading:p}=e(),{pagination:y}=a(),g=l({orderNo:void 0,deliveryType:void 0,payType:void 0,commodityName:void 0,storeNo:void 0,storeName:void 0,userNo:void 0,nickname:void 0,mobile:void 0,minAmount:void 0,maxAmount:void 0,createTime:void 0}),f=m(void 0),h=v((()=>f.value?N.value.find((e=>e.id===f.value)):null)),T=m([]),x=v((()=>T.value.length?N.value.filter((e=>T.value.includes(e.id))):[])),N=m([]),w=async()=>{var e;p(!0),f.value=void 0,T.value=[];try{const{data:a}=await(e=>o({url:"/admin/platform/order/list",method:"post",data:e}))({...g,mobile:null==(e=g.mobile)?void 0:e.toString(),...u,pageNum:y.current,pageSize:y.pageSize});N.value=a.rows.map((e=>(e.createTime=n(e.createTime).format("YYYY-MM-DD HH:mm:ss"),e.payTime=e.payTime?n(e.payTime).format("YYYY-MM-DD HH:mm:ss"):"-",e))),y.total=a.total,p(!1)}catch(a){N.value=[],y.total=0,p(!1)}};return{loading:c,queryParams:g,pagination:y,rows:N,selectedId:f,selectedRow:h,selectedIds:T,selectedRows:x,selectAll:e=>{T.value=e?N.value.map((e=>e.id)):[]},rowSelect:(e,a,o)=>{T.value.includes(o.id)?T.value.splice(T.value.indexOf(o.id),1):T.value.push(o.id)},rowClick:e=>{T.value.includes(e.id)?T.value.splice(T.value.indexOf(e.id),1):T.value.push(e.id)},query:w,reset:()=>{y.current=1,Object.assign(g,{orderNo:void 0,deliveryType:void 0,payType:void 0,commodityName:void 0,storeNo:void 0,storeName:void 0,userNo:void 0,nickname:void 0,mobile:void 0,minAmount:void 0,maxAmount:void 0,createTime:void 0}),w()},pageChange:async e=>{y.current=e,w()},pageSizeChange:async e=>{y.current=1,y.pageSize=e,w()},exports:async()=>{try{await new Promise(((e,a)=>{i({url:t+"/admin/platform/order/exports",method:"post",responseType:"blob",headers:{Authorization:"Bearer "+r()}}).then((o=>{"application/vnd.openxmlformats"===o.data.type?(d(o,"application/vnd.openxmlformats;charset=utf-8","订单记录.xlsx"),e(!0)):(s.warning({title:"下载出错",content:"请稍后重试",duration:3e3}),a())})).catch((()=>{a()}))}))}catch(e){throw e}}}};export{u};

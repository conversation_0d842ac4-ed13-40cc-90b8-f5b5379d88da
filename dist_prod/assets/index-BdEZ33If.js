import"./index-DOhy6BH_.js";import{I as e}from"./index-DDFSMqsG.js";import{T as a,L as t}from"./index-Cuq5XRs0.js";import"./index-DD6vSYIM.js";import"./index-DQjhgQFu.js";import{T as l,a as o,P as i}from"./index-DdMaxvYa.js";import{B as n,S as r}from"./index-DGtjsHgS.js";import{C as s}from"./index-CdWxsKz_.js";import{D as d}from"./index-DmW4RN1x.js";import{A as u}from"./index-CUtvFEc_.js";import{C as c,R as p}from"./index-BEo1tUsK.js";import{F as m,a as v}from"./index-DVDXfQhn.js";import{R as f}from"./index-dpn1_5z1.js";/* empty css              */import{D as g,a as y}from"./index-DIKBiUsz.js";import{B as h,r as w,c as _,d as k,o as x,k as b,A as j,z as C,u as z,e as S,q as T,ae as P,f as I,j as Y,I as $,J as B,y as R,p as V,F as D,af as F,h as M}from"./vue-D-10XvVk.js";import{O as N,N as O,n as U}from"./index-D-8JbLQk.js";import{u as H}from"./useLoading-D5mh7tTu.js";import{u as q}from"./usePagination-Dd_EW2BO.js";import{d as A}from"./dayjs.min-Daes5FZc.js";import{M as L}from"./index-O7pr3qsq.js";import"./pick-Ccd8Sfcm.js";import"./ResizeObserver.es-CzGuHLZU.js";import"./resize-observer-Dtogi-DJ.js";import"./index-DfEXMvnc.js";import"./use-children-components-v8i8lsOx.js";import"./use-index-D_ozg7PK.js";import"./index-CHOaln3D.js";import"./render-function-CAXdZVZM.js";const E=e=>{const{loading:a,setLoading:t}=H(),{pagination:l}=q(),o=h({nickname:void 0,createTime:void 0}),i=w(void 0),n=_((()=>i.value?d.value.find((e=>e.id===i.value)):null)),r=w([]),s=_((()=>r.value.length?d.value.filter((e=>r.value.includes(e.id))):[])),d=w([]),u=async()=>{t(!0),i.value=void 0,r.value=[];try{const{data:a}=await(e=>N({url:"/admin/platform/userCommissionFlow/list",method:"post",data:e}))({...o,type:e,pageNum:l.current,pageSize:l.pageSize});d.value=a.rows.map((e=>(e.approveTime=e.approveTime?A(e.approveTime).format("YYYY-MM-DD HH:mm:ss"):"-",e.payoutTime=e.payoutTime?A(e.payoutTime).format("YYYY-MM-DD HH:mm:ss"):"-",e.createTime=A(e.createTime).format("YYYY-MM-DD HH:mm:ss"),e))),l.total=a.total,t(!1)}catch(a){d.value=[],l.total=0,t(!1)}};return{loading:a,queryParams:o,pagination:l,rows:d,selectedId:i,selectedRow:n,selectedIds:r,selectedRows:s,selectAll:e=>{r.value=e?d.value.map((e=>e.id)):[]},rowSelect:(e,a,t)=>{r.value.includes(t.id)?r.value.splice(r.value.indexOf(t.id),1):r.value.push(t.id)},rowClick:e=>{r.value.includes(e.id)?r.value.splice(r.value.indexOf(e.id),1):r.value.push(e.id)},query:u,reset:()=>{l.current=1,Object.assign(o,{nickname:void 0,createTime:void 0}),u()},pageChange:async e=>{l.current=e,u()},pageSizeChange:async e=>{l.current=1,l.pageSize=e,u()},approvePayout:async(e,a)=>{try{await((e,a)=>N({url:`/admin/platform/userCommissionFlow/approvePayout/${e}`,method:"put",data:a}))(e,a)}catch(t){throw t}},payout:async e=>{try{await(e=>N({url:`/admin/platform/userCommissionFlow/payout/${e}`,method:"put"}))(e)}catch(a){throw a}}}},J={class:"page-container"},G={class:"h-full flex flex-col gap-[18px]"},K=["src"],Q={class:"text-left"},W={class:"pt-1"},X={class:"pt-1"},Z=k({__name:"index",setup(h){const _=P(),{loading:k,queryParams:N,pagination:H,rows:q,selectedId:A,selectedRow:Z,selectedIds:ee,selectAll:ae,rowSelect:te,rowClick:le,query:oe,reset:ie,pageChange:ne,pageSizeChange:re,approvePayout:se,payout:de}=E("UserCommissionFlows"===_.name?1:-1),ue=w(!1),ce=w(!1),pe=async()=>{try{ce.value=!0,await se(A.value,{approveState:1,approveReason:"人工审核通过"}),ce.value=!1,ue.value=!1,O.success({title:"成功提示",content:"已审核通过提现申请",duration:1500}),oe()}catch(e){ce.value=!1}},me=w(!1),ve=w(void 0),fe=async e=>{try{if(!ve.value)throw O.warning({title:"提示",content:"请填写拒绝理由",duration:1500}),new Error("校验失败");await se(A.value,{approveState:-1,approveReason:ve.value}),O.success({title:"成功提示",content:"已审核拒绝提现申请",duration:1500}),ue.value=!1,e(!0),oe()}catch(a){e(!1)}};return x((()=>{ne(1)})),(h,w)=>{const x=n,P=r,E=g,ee=y,ae=s,te=L,le=e,se=m,ge=c,ye=f,he=I("icon-search"),we=I("icon-refresh"),_e=p,ke=v,xe=l,be=a,je=u,Ce=d,ze=t,Se=o,Te=i;return Y(),b("div",J,[j(te,{visible:z(ue),"title-align":"start",title:"审核详情","cancel-button-props":{type:"outline"},"cancel-text":"拒绝","ok-text":"通过","unmount-on-close":"","mask-closable":!1,"esc-to-close":!1,onCancel:w[1]||(w[1]=e=>ue.value=!1),"body-style":"background-color: var(--color-fill-2)"},{footer:C((()=>[j(P,null,{default:C((()=>[j(x,{type:"outline",onClick:w[0]||(w[0]=()=>{ve.value=void 0,me.value=!0})},{default:C((()=>w[7]||(w[7]=[$(" 审核拒绝 ")]))),_:1}),j(x,{type:"primary",loading:z(ce),onClick:pe},{default:C((()=>w[8]||(w[8]=[$("审核通过")]))),_:1},8,["loading"])])),_:1})])),default:C((()=>[j(ae,{bordered:!1},{default:C((()=>[j(ee,{bordered:"",size:"large",column:1},{default:C((()=>[j(E,{label:"提现用户"},{default:C((()=>{var e,a;return[$(B(null==(a=null==(e=z(Z))?void 0:e.user)?void 0:a.nickname),1)]})),_:1}),j(E,{label:"提现金额"},{default:C((()=>{var e;return[$(B(null==(e=z(Z))?void 0:e.amount.toFixed(2))+" 元",1)]})),_:1}),j(E,{label:"银行名称"},{default:C((()=>{var e,a;return[$(B((null==(a=null==(e=z(Z))?void 0:e.payoutBankInfo)?void 0:a.bankName)??"-"),1)]})),_:1}),j(E,{label:"银行账号"},{default:C((()=>{var e,a;return[$(B((null==(a=null==(e=z(Z))?void 0:e.payoutBankInfo)?void 0:a.bankAccount)??"-"),1)]})),_:1}),j(E,{label:"开户人姓名"},{default:C((()=>{var e,a;return[$(B((null==(a=null==(e=z(Z))?void 0:e.payoutBankInfo)?void 0:a.contactName)??"-"),1)]})),_:1}),j(E,{label:"开户人身份证号"},{default:C((()=>{var e,a;return[$(B((null==(a=null==(e=z(Z))?void 0:e.payoutBankInfo)?void 0:a.contactIdentityCard)??"-"),1)]})),_:1}),j(E,{label:"开户人手机号"},{default:C((()=>{var e,a;return[$(B((null==(a=null==(e=z(Z))?void 0:e.payoutBankInfo)?void 0:a.contactNumber)??"-"),1)]})),_:1})])),_:1})])),_:1})])),_:1},8,["visible"]),j(te,{visible:z(me),"onUpdate:visible":w[3]||(w[3]=e=>S(me)?me.value=e:null),"title-align":"start",title:"拒绝理由","cancel-button-props":{type:"outline"},"ok-text":"提交","unmount-on-close":"","mask-closable":!1,"esc-to-close":!1,"on-before-ok":fe},{default:C((()=>[j(le,{modelValue:z(ve),"onUpdate:modelValue":w[2]||(w[2]=e=>S(ve)?ve.value=e:null),placeholder:`${h.$inputPlaceholder}拒绝理由`,"allow-clear":""},null,8,["modelValue","placeholder"])])),_:1},8,["visible"]),T("div",G,[j(ae,{bordered:!1},{default:C((()=>[j(ke,{model:z(N),"auto-label-width":""},{default:C((()=>[j(_e,{gutter:16},{default:C((()=>[j(ge,{span:6},{default:C((()=>[j(se,{"show-colon":"",label:"用户昵称",field:"nickname"},{default:C((()=>[j(le,{modelValue:z(N).nickname,"onUpdate:modelValue":w[4]||(w[4]=e=>z(N).nickname=e),placeholder:`${h.$inputPlaceholder}用户昵称`,"allow-clear":""},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),j(ge,{span:6},{default:C((()=>[j(se,{"show-colon":"",label:"日期",field:"createTime"},{default:C((()=>[j(ye,{modelValue:z(N).createTime,"onUpdate:modelValue":w[5]||(w[5]=e=>z(N).createTime=e),placeholder:[`${h.$selectPlaceholder}开始日期`,`${h.$selectPlaceholder}结束日期`]},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),j(ge,{span:6},{default:C((()=>[j(se,{"hide-label":""},{default:C((()=>[j(P,{size:18},{default:C((()=>[j(x,{type:"primary",onClick:w[6]||(w[6]=e=>z(ne)(1))},{icon:C((()=>[j(he)])),default:C((()=>[w[9]||(w[9]=$(" 查询 "))])),_:1}),j(x,{type:"outline",onClick:z(ie)},{icon:C((()=>[j(we)])),default:C((()=>[w[10]||(w[10]=$(" 重置 "))])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),j(ae,{bordered:!1,class:"flex-1 overflow-y-hidden","body-style":{height:"100%"}},{actions:C((()=>[z(H).total?(Y(),R(Te,{key:0,current:z(H).current,"page-size":z(H).pageSize,"show-total":z(H).showTotal,"show-page-size":z(H).showPageSize,"page-size-options":z(H).pageSizeOptions,total:z(H).total,onChange:z(ne),onPageSizeChange:z(re)},null,8,["current","page-size","show-total","show-page-size","page-size-options","total","onChange","onPageSizeChange"])):V("",!0)])),default:C((()=>[j(Se,{size:"large","row-key":"id",loading:z(k),pagination:!1,data:z(q),bordered:{cell:!0},scroll:{y:"calc(100% - 52px)"}},{columns:C((()=>[j(xe,{align:"center",title:"序号",width:80},{cell:C((({rowIndex:e})=>[$(B(e+1),1)])),_:1}),j(xe,{align:"center",title:"类型",width:150},{cell:C((({record:e})=>[j(be,{color:1===e.type?"green":"orange"},{default:C((()=>[$(B(1===e.type?"推广订单收入":"提现"),1)])),_:2},1032,["color"])])),_:1}),j(xe,{align:"center",title:"用户信息",width:350,ellipsis:"",tooltip:""},{cell:C((({record:e})=>[j(P,null,{default:C((()=>{var a,t,l;return[j(je,{size:32},{default:C((()=>{var a;return[T("img",{src:null==(a=e.user)?void 0:a.avatar},null,8,K)]})),_:2},1024),T("div",Q,[T("p",null,"用户编号："+B(null==(a=e.user)?void 0:a.userNo),1),T("p",W,"用户昵称："+B(null==(t=e.user)?void 0:t.nickname),1),T("p",X,"用户手机："+B(z(U)(null==(l=e.user)?void 0:l.mobile)),1)])]})),_:2},1024)])),_:1}),j(xe,{align:"center",title:"关联订单",width:200},{cell:C((({record:e})=>{var a;return[$(B((null==(a=e.order)?void 0:a.orderNo)??"-"),1)]})),_:1}),j(xe,{align:"center",title:"金额",width:150},{cell:C((({record:e})=>[$(B(e.amount)+" 元",1)])),_:1}),j(xe,{align:"center",title:"审核状态",width:150},{cell:C((({record:e})=>[$(B(0===e.approveState?"待审核":1===e.approveState?"审核通过":"审核拒绝"),1)])),_:1}),j(xe,{align:"center",title:"审核原因",width:200,ellipsis:"",tooltip:"","data-index":"approveReason"}),j(xe,{align:"center",title:"审核时间",width:180,"data-index":"approveTime"}),"UserCommissionPayouts"===z(_).name?(Y(),b(D,{key:0},[j(xe,{align:"center",title:"打款状态",width:150},{cell:C((({record:e})=>[$(B(-1===e.payoutState?"未打款":1===e.payoutState?"已打款":"-"),1)])),_:1}),j(xe,{align:"center",title:"打款时间",width:200,ellipsis:"",tooltip:"","data-index":"payoutTime"})],64)):V("",!0),j(xe,{align:"center",title:"创建时间",width:180,"data-index":"createTime"}),"UserCommissionPayouts"===z(_).name?(Y(),R(xe,{key:1,align:"center",title:"操作",width:200,fixed:"right"},{cell:C((({record:e})=>[j(P,null,{split:C((()=>[j(Ce,{direction:"vertical"})])),default:C((()=>[0===e.approveState?(Y(),R(ze,{key:0,onClick:F((()=>{A.value=e.id,ue.value=!0}),["stop"])},{default:C((()=>w[11]||(w[11]=[$(" 审核 ")]))),_:2},1032,["onClick"])):V("",!0),1===e.approveState&&-1===e.payoutState?(Y(),R(ze,{key:1,onClick:F((a=>{return t=e.id,void L.confirm({title:"提示",content:()=>M("div",{class:"text-center"},"该操作将修改打款状态，确定已线下打款？"),maskClosable:!1,escToClose:!1,hideCancel:!1,cancelButtonProps:{type:"outline"},onBeforeOk:async e=>{try{await de(t),O.success({title:"成功提示",content:"已打款",duration:1500}),e(!0),oe()}catch(a){e(!1)}}});var t}),["stop"])},{default:C((()=>w[12]||(w[12]=[$("打款")]))),_:2},1032,["onClick"])):V("",!0)])),_:2},1024)])),_:1})):V("",!0)])),_:1},8,["loading","data"])])),_:1})])])}}});export{Z as default};

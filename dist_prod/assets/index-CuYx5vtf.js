import{_ as e,g as t,x as o,o as a,v as l,w as r,a5 as n,a6 as i,B as s,a as u,W as c,a7 as v,a8 as d,a9 as p,aa as f,ab as m,ac as g,i as y,k as b,ad as w,e as C,P as h,ae as k,s as x,f as P}from"./index-D-8JbLQk.js";import{d as I,j as $,k as L,l as O,J as S,p as B,m as A,f as j,q as z,A as E,v as R,H as N,t as T,c as V,F,L as M,y as W,z as D,r as _,a as G,w as U,n as Y,B as X,b as Z,h as H,Z as K,a7 as q,a6 as J,O as Q,C as ee,af as te,aj as oe,i as ae,D as le,a3 as re}from"./vue-D-10XvVk.js";import{e as ne,a as ie,b as se,t as ue,K as ce,o as ve}from"./index-DDFSMqsG.js";import{R as de}from"./render-function-CAXdZVZM.js";const pe=I({name:"ImageFooter",props:{title:{type:String},description:{type:String}},setup:()=>({prefixCls:t("image-footer")})}),fe=["title"],me=["title"];var ge=e(pe,[["render",function(e,t,o,a,l,r){return $(),L("div",{class:O(e.prefixCls)},[e.title||e.description?($(),L("div",{key:0,class:O(`${e.prefixCls}-caption`)},[e.title?($(),L("div",{key:0,class:O(`${e.prefixCls}-caption-title`),title:e.title},S(e.title),11,fe)):B("v-if",!0),e.description?($(),L("div",{key:1,class:O(`${e.prefixCls}-caption-description`),title:e.description},S(e.description),11,me)):B("v-if",!0)],2)):B("v-if",!0),e.$slots.extra?($(),L("div",{key:1,class:O(`${e.prefixCls}-extra`)},[A(e.$slots,"extra")],2)):B("v-if",!0)],2)}]]);var ye=e(I({name:"ImagePreviewArrow",components:{IconLeft:o,IconRight:a},props:{onPrev:{type:Function},onNext:{type:Function}},setup:()=>({prefixCls:t("image-preview-arrow")})}),[["render",function(e,t,o,a,l,r){const n=j("icon-left"),i=j("icon-right");return $(),L("div",{class:O(e.prefixCls)},[z("div",{class:O([`${e.prefixCls}-left`,{[`${e.prefixCls}-disabled`]:!e.onPrev}]),onClick:t[0]||(t[0]=t=>{t.preventDefault(),e.onPrev&&e.onPrev()})},[E(n)],2),z("div",{class:O([`${e.prefixCls}-right`,{[`${e.prefixCls}-disabled`]:!e.onNext}]),onClick:t[1]||(t[1]=t=>{t.preventDefault(),e.onNext&&e.onNext()})},[E(i)],2)],2)}]]);var be=I({name:"ImagePreviewAction",components:{Tooltip:ne},inheritAttrs:!1,props:{name:{type:String},disabled:{type:Boolean}},setup(e,{slots:o,attrs:a}){const l=t("image-preview-toolbar-action");return()=>{var t;const{name:r,disabled:n}=e,i=null==(t=o.default)?void 0:t.call(o);if(!i||!i.length)return null;const s=E("div",R({class:[`${l}`,{[`${l}-disabled`]:n}],onMousedown:e=>{e.preventDefault()}},a),[E("span",{class:`${l}-content`},[i])]);return r?E(ne,{class:`${l}-tooltip`,content:r},"function"==typeof(u=s)||"[object Object]"===Object.prototype.toString.call(u)&&!N(u)?s:{default:()=>[s]}):s;var u}}});var we=e(I({name:"ImagePreviewToolbar",components:{RenderFunction:de,PreviewAction:be},props:{actions:{type:Array,default:()=>[]},actionsLayout:{type:Array,default:()=>[]}},setup(e){const{actions:o,actionsLayout:a}=T(e);return{prefixCls:t("image-preview-toolbar"),resultActions:V((()=>{const e=new Set(a.value);return o.value.filter((t=>e.has(t.key))).sort(((e,t)=>a.value.indexOf(e.key)>a.value.indexOf(t.key)?1:-1))}))}}}),[["render",function(e,t,o,a,l,r){const n=j("RenderFunction"),i=j("PreviewAction");return $(),L("div",{class:O(e.prefixCls)},[($(!0),L(F,null,M(e.resultActions,(e=>($(),W(i,{key:e.key,name:e.name,disabled:e.disabled,onClick:e.onClick},{default:D((()=>[E(n,{"render-func":e.content},null,8,["render-func"])])),_:2},1032,["name","disabled","onClick"])))),128)),A(e.$slots,"default")],2)}]]);function Ce(e){const t=_("beforeLoad"),o=V((()=>"beforeLoad"===t.value)),a=V((()=>"loading"===t.value)),l=V((()=>"error"===t.value)),r=V((()=>"loaded"===t.value));return{status:t,isBeforeLoad:o,isLoading:a,isError:l,isLoaded:r,setLoadStatus:e=>{t.value=e}}}function he(e){const{wrapperEl:t,imageEl:o,scale:a}=T(e),n=_([0,0]),i=_(!1);let s=0,u=0,c=[0,0];const v=()=>{if(!t.value||!o.value)return;const e=t.value.getBoundingClientRect(),l=o.value.getBoundingClientRect(),[r,i]=function(e,t,o,a,l){let r=o,n=a;return o&&(e.width>t.width?r=0:(t.left>e.left&&(r-=Math.abs(e.left-t.left)/l),t.right<e.right&&(r+=Math.abs(e.right-t.right)/l))),a&&(e.height>t.height?n=0:(t.top>e.top&&(n-=Math.abs(e.top-t.top)/l),t.bottom<e.bottom&&(n+=Math.abs(e.bottom-t.bottom)/l))),[r,n]}(e,l,n.value[0],n.value[1],a.value);r===n.value[0]&&i===n.value[1]||(n.value=[r,i])},d=e=>{e.preventDefault&&e.preventDefault();const t=c[0]+(e.pageX-s)/a.value,o=c[1]+(e.pageY-u)/a.value;n.value=[t,o]},p=e=>{e.preventDefault&&e.preventDefault(),i.value=!1,v(),m()},f=e=>{e.target===e.currentTarget&&(e.preventDefault&&e.preventDefault(),i.value=!0,s=e.pageX,u=e.pageY,c=[...n.value],l(window,"mousemove",d,!1),l(window,"mouseup",p,!1))};function m(){r(window,"mousemove",d,!1),r(window,"mouseup",p,!1)}return G((e=>{o.value&&l(o.value,"mousedown",f),e((()=>{o.value&&r(o.value,"mousedown",f),m()}))})),U([a],(()=>{Y((()=>v()))})),{translate:n,moving:i,resetTranslate(){n.value=[0,0]}}}function ke(e){const{container:t,hidden:o}=T(e);let a=!1,l={};const r=()=>{if(t.value&&"hidden"!==t.value.style.overflow){const o=t.value.style;a=!0;const r="BODY"===(e=t.value).tagName?window.innerWidth-(document.body.clientWidth||document.documentElement.clientWidth):e.offsetWidth-e.clientWidth;r&&(l.width=o.width,t.value.style.width=`calc(${t.value.style.width||"100%"} - ${r}px)`),l.overflow=o.overflow,t.value.style.overflow="hidden"}var e},n=()=>{if(t.value&&a){const e=l;Object.keys(e).forEach((o=>{t.value.style[o]=e[o]}))}a=!1,l={}};return G((e=>{o.value?r():n(),e((()=>{n()}))})),[n,r]}const xe=[25,33,50,67,75,80,90,100,110,125,150,175,200,250,300,400,500].map((e=>+(e/100).toFixed(2))),Pe=xe[0],Ie=xe[xe.length-1];function $e(e=1,t="zoomIn"){let o=xe.indexOf(e);return-1===o&&(o=function(e){let t=xe.length-1;for(let o=0;o<xe.length;o++){const a=xe[o];if(e===a){t=o;break}if(e<a){const l=xe[o-1];t=void 0===l||Math.abs(l-e)<=Math.abs(a-e)?o-1:o;break}}return t}(e)),"zoomIn"===t?o===xe.length-1?e:xe[o+1]:0===o?e:xe[o-1]}var Le=Object.defineProperty,Oe=Object.getOwnPropertySymbols,Se=Object.prototype.hasOwnProperty,Be=Object.prototype.propertyIsEnumerable,Ae=(e,t,o)=>t in e?Le(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o;var je=I({name:"ImagePreview",components:{PreviewArrow:ye,PreviewToolbar:we,IconLoading:s,IconClose:u},props:{renderToBody:{type:Boolean,default:!0},src:{type:String},visible:{type:Boolean,default:void 0},defaultVisible:{type:Boolean,default:!1},maskClosable:{type:Boolean,default:!0},closable:{type:Boolean,default:!0},actionsLayout:{type:Array,default:()=>["fullScreen","rotateRight","rotateLeft","zoomIn","zoomOut","originalSize"]},popupContainer:{type:[Object,String]},inGroup:{type:Boolean,default:!1},groupArrowProps:{type:Object,default:()=>({})},escToClose:{type:Boolean,default:!0},wheelZoom:{type:Boolean,default:!0},keyboard:{type:Boolean,default:!0},defaultScale:{type:Number,default:1},zoomRate:{type:Number,default:1.1}},emits:["close","update:visible"],setup(e,{emit:o}){const{t:a}=ie(),{src:s,popupContainer:u,visible:y,defaultVisible:b,maskClosable:w,actionsLayout:C,defaultScale:h,zoomRate:k}=T(e),x=_(),P=_(),I=t("image-preview"),[$,L]=se(b.value,X({value:y})),O=V((()=>[I,{[`${I}-hide`]:!$.value}])),S=function(e,t){const{popupContainer:o}=T(t);return V((()=>(n(o.value)?i(o.value):o.value)||e))}(document.body,X({popupContainer:u})),B=V((()=>S.value===document.body)),{zIndex:A}=c("dialog",{visible:$}),j=V((()=>((e,t)=>{for(var o in t||(t={}))Se.call(t,o)&&Ae(e,o,t[o]);if(Oe)for(var o of Oe(t))Be.call(t,o)&&Ae(e,o,t[o]);return e})({},B.value?{zIndex:A.value,position:"fixed"}:{zIndex:"inherit",position:"absolute"}))),{isLoading:z,isLoaded:E,setLoadStatus:R}=Ce(),N=_(0),F=_(h.value),{translate:M,moving:W,resetTranslate:D}=he(X({wrapperEl:x,imageEl:P,visible:$,scale:F})),G=_(!1);let K=null;ke(X({container:S,hidden:$}));const q=e=>C.value.includes(e),J=t=>{switch(t.stopPropagation(),t.preventDefault(),t.key){case ce.ESC:e.escToClose&&oe();break;case ce.ARROW_LEFT:e.groupArrowProps.onPrev&&e.groupArrowProps.onPrev();break;case ce.ARROW_RIGHT:e.groupArrowProps.onNext&&e.groupArrowProps.onNext();break;case ce.ARROW_UP:q("zoomIn")&&re("zoomIn");break;case ce.ARROW_DOWN:q("zoomOut")&&re("zoomOut");break;case ce.SPACE:q("originalSize")&&ae(1)}},Q=ue((t=>{if(t.preventDefault(),t.stopPropagation(),!e.wheelZoom)return;const o=(t.deltaY||t.deltaX)>0?"zoomOut":"zoomIn",a=function(e,t=1.1,o="zoomIn"){const a="zoomIn"===o?t:1/t,l=Number.parseFloat((e*a).toFixed(3));return Math.min(Ie,Math.max(Pe,l))}(F.value,k.value,o);ae(a)}));let ee=!1;const te=()=>{ee&&(ee=!1,r(S.value,"keydown",J))};function oe(){$.value&&(o("close"),o("update:visible",!1),L(!1))}function ae(e){F.value!==e&&(F.value=e,!G.value&&(G.value=!0),K&&clearTimeout(K),K=setTimeout((()=>{G.value=!1}),1e3))}function le(e){const t="clockwise"===e?(N.value+90)%360:0===N.value?270:N.value-90;N.value=t}function re(e){ae($e(F.value,e))}return U([s,$],(()=>{$.value?(N.value=0,F.value=h.value,D(),R("loading"),Y((()=>{var e;null==(e=null==x?void 0:x.value)||e.focus()})),e.keyboard&&!ee&&(ee=!0,l(S.value,"keydown",J))):te()})),Z((()=>{te()})),{prefixCls:I,classNames:O,container:S,wrapperStyles:j,scale:F,translate:M,rotate:N,moving:W,mergedVisible:$,isLoading:z,isLoaded:E,scaleValueVisible:G,refWrapper:x,refImage:P,onWheel:Q,onMaskClick:function(e){var t;null==(t=null==x?void 0:x.value)||t.focus(),w.value&&e.target===e.currentTarget&&oe()},onCloseClick:oe,onImgLoad(){R("loaded")},onImgError(){R("error")},actions:V((()=>[{key:"fullScreen",name:a("imagePreview.fullScreen"),content:()=>H(v),onClick:()=>function(){const e=x.value.getBoundingClientRect(),t=P.value.getBoundingClientRect(),o=e.height/(t.height/F.value),a=e.width/(t.width/F.value);ae(Math.max(o,a))}()},{key:"rotateRight",name:a("imagePreview.rotateRight"),content:()=>H(d),onClick:()=>le("clockwise")},{key:"rotateLeft",name:a("imagePreview.rotateLeft"),content:()=>H(p),onClick:()=>le("counterclockwise")},{key:"zoomIn",name:a("imagePreview.zoomIn"),content:()=>H(f),onClick:()=>re("zoomIn"),disabled:F.value===Ie},{key:"zoomOut",name:a("imagePreview.zoomOut"),content:()=>H(m),onClick:()=>re("zoomOut"),disabled:F.value===Pe},{key:"originalSize",name:a("imagePreview.originalSize"),content:()=>H(g),onClick:()=>ae(1)}]))}}});const ze=["src"];var Ee=e(je,[["render",function(e,t,o,a,l,r){const n=j("IconLoading"),i=j("PreviewToolbar"),s=j("IconClose"),u=j("PreviewArrow");return $(),W(oe,{to:e.container,disabled:!e.renderToBody},[z("div",{class:O(e.classNames),style:Q(e.wrapperStyles)},[E(J,{name:"image-fade",onBeforeEnter:t[0]||(t[0]=e=>e.parentElement&&(e.parentElement.style.display="block")),onAfterLeave:t[1]||(t[1]=e=>e.parentElement&&(e.parentElement.style.display=""))},{default:D((()=>[K(z("div",{class:O(`${e.prefixCls}-mask`)},null,2),[[q,e.mergedVisible]])])),_:1}),e.mergedVisible?($(),L("div",{key:0,ref:"refWrapper",tabindex:"0",class:O(`${e.prefixCls}-wrapper`),onClick:t[6]||(t[6]=(...t)=>e.onMaskClick&&e.onMaskClick(...t)),onWheel:t[7]||(t[7]=te(((...t)=>e.onWheel&&e.onWheel(...t)),["prevent","stop"]))},[B(" img "),z("div",{class:O(`${e.prefixCls}-img-container`),style:Q({transform:`scale(${e.scale}, ${e.scale})`}),onClick:t[4]||(t[4]=(...t)=>e.onMaskClick&&e.onMaskClick(...t))},[($(),L("img",{ref:"refImage",key:e.src,src:e.src,class:O([`${e.prefixCls}-img`,{[`${e.prefixCls}-img-moving`]:e.moving}]),style:Q({transform:`translate(${e.translate[0]}px, ${e.translate[1]}px) rotate(${e.rotate}deg)`}),onLoad:t[2]||(t[2]=(...t)=>e.onImgLoad&&e.onImgLoad(...t)),onError:t[3]||(t[3]=(...t)=>e.onImgError&&e.onImgError(...t))},null,46,ze))],6),B(" loading "),e.isLoading?($(),L("div",{key:0,class:O(`${e.prefixCls}-loading`)},[E(n)],2)):B("v-if",!0),B(" scale value "),E(J,{name:"image-fade"},{default:D((()=>[e.scaleValueVisible?($(),L("div",{key:0,class:O(`${e.prefixCls}-scale-value`)},S((100*e.scale).toFixed(0))+"% ",3)):B("v-if",!0)])),_:1}),B(" toolbar "),e.isLoaded&&e.actionsLayout.length?($(),W(i,{key:1,actions:e.actions,"actions-layout":e.actionsLayout},{default:D((()=>[A(e.$slots,"actions")])),_:3},8,["actions","actions-layout"])):B("v-if",!0),B(" close btn "),e.closable?($(),L("div",{key:2,class:O(`${e.prefixCls}-close-btn`),onClick:t[5]||(t[5]=(...t)=>e.onCloseClick&&e.onCloseClick(...t))},[E(s)],2)):B("v-if",!0),B(" group arrow "),e.inGroup?($(),W(u,ee(R({key:3},e.groupArrowProps)),null,16)):B("v-if",!0)],34)):B("v-if",!0)],6)],8,["to","disabled"])}]]);function Re(e){if(y(e))return;if(!b(e)&&/^\d+(%)$/.test(e))return e;const t=parseInt(e,10);return b(t)?`${t}px`:void 0}const Ne=Symbol("PreviewGroupInjectionKey");var Te=Object.defineProperty,Ve=Object.getOwnPropertySymbols,Fe=Object.prototype.hasOwnProperty,Me=Object.prototype.propertyIsEnumerable,We=(e,t,o)=>t in e?Te(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,De=(e,t)=>{for(var o in t||(t={}))Fe.call(t,o)&&We(e,o,t[o]);if(Ve)for(var o of Ve(t))Me.call(t,o)&&We(e,o,t[o]);return e};let _e=0;const Ge=I({name:"Image",components:{IconImageClose:w,IconLoading:s,ImageFooter:ge,ImagePreview:Ee},inheritAttrs:!1,props:{renderToBody:{type:Boolean,default:!0},src:{type:String},width:{type:[String,Number]},height:{type:[String,Number]},title:{type:String},description:{type:String},fit:{type:String},alt:{type:String},hideFooter:{type:[Boolean,String],default:!1},footerPosition:{type:String,default:"inner"},showLoader:{type:Boolean,default:!1},preview:{type:Boolean,default:!0},previewVisible:{type:Boolean,default:void 0},defaultPreviewVisible:{type:Boolean,default:!1},previewProps:{type:Object},footerClass:{type:[String,Array,Object]}},emits:["preview-visible-change","update:previewVisible"],setup(e,{attrs:o,slots:a,emit:l}){const{t:r}=ie(),{height:n,width:i,hideFooter:s,title:u,description:c,src:v,footerPosition:d,defaultPreviewVisible:p,previewVisible:f,preview:m,previewProps:g}=T(e),y=ae(Ne,void 0),b=t("image"),w=_(),{isLoaded:k,isError:x,isLoading:P,setLoadStatus:I}=Ce(),$=V((()=>({width:Re(null==i?void 0:i.value),height:Re(null==n?void 0:n.value)}))),L=V((()=>e.fit?{objectFit:e.fit}:{})),O=V((()=>[`${b}`,{[`${b}-loading`]:P.value,[`${b}-loading-error`]:x.value,[`${b}-with-footer-inner`]:k&&B&&"inner"===d.value,[`${b}-with-footer-outer`]:k&&B&&"outer"===d.value},o.class])),S=V((()=>[$.value,o.style])),B=V((()=>!!((null==u?void 0:u.value)||(null==c?void 0:c.value)||a.extra)&&(C(s.value)?!s.value&&k.value:"never"===s.value))),A=V((()=>ve(o,["class","style"]))),[j,z]=se(p.value,X({value:f})),E=V((()=>!(null==y?void 0:y.preview)&&m.value));G((()=>{!h&&w.value&&(w.value.src=null==v?void 0:v.value,I("loading"))}));const R=_e++;return G((e=>{var t,o,a;const l=null==(a=null==y?void 0:y.registerImageUrl)?void 0:a.call(y,R,(null!=(o=null==(t=null==g?void 0:g.value)?void 0:t.src)?o:null==v?void 0:v.value)||"",m.value);e((()=>{null==l||l()}))})),{t:r,refImg:w,prefixCls:b,wrapperClassNames:O,wrapperStyles:S,showFooter:B,imgProps:A,imgStyle:$,isLoaded:k,isError:x,isLoading:P,mergedPreviewVisible:j,mergePreview:E,onImgLoaded:function(){I("loaded")},onImgLoadError:function(){I("error")},onImgClick:function(){m.value&&((null==y?void 0:y.preview)?y.preview(R):(l("preview-visible-change",!0),z(!0)))},onPreviewClose:function(){l("preview-visible-change",!1),z(!1)},fitStyle:L}}}),Ue=["title","alt"];var Ye=e(Ge,[["render",function(e,t,o,a,l,r){const n=j("IconImageClose"),i=j("IconLoading"),s=j("ImageFooter"),u=j("ImagePreview");return $(),L("div",{class:O(e.wrapperClassNames),style:Q(e.wrapperStyles)},[z("img",R({ref:"refImg",class:`${e.prefixCls}-img`},e.imgProps,{style:De(De({},e.imgStyle),e.fitStyle),title:e.title,alt:e.alt,onLoad:t[0]||(t[0]=(...t)=>e.onImgLoaded&&e.onImgLoaded(...t)),onError:t[1]||(t[1]=(...t)=>e.onImgLoadError&&e.onImgLoadError(...t)),onClick:t[2]||(t[2]=(...t)=>e.onImgClick&&e.onImgClick(...t))}),null,16,Ue),e.isLoaded?B("v-if",!0):($(),L("div",{key:0,class:O(`${e.prefixCls}-overlay`)},[e.isError?A(e.$slots,"error",{key:0},(()=>[z("div",{class:O(`${e.prefixCls}-error`)},[z("div",{class:O(`${e.prefixCls}-error-icon`)},[A(e.$slots,"error-icon",{},(()=>[E(n)]))],2),e.alt||e.description?($(),L("div",{key:0,class:O(`${e.prefixCls}-error-alt`)},S(e.alt||e.description),3)):B("v-if",!0)],2)])):B("v-if",!0),e.isLoading&&(e.showLoader||e.$slots.loader)?A(e.$slots,"loader",{key:1},(()=>[z("div",{class:O([`${e.prefixCls}-loader`])},[z("div",{class:O(`${e.prefixCls}-loader-spin`)},[E(i),z("div",{class:O(`${e.prefixCls}-loader-spin-text`)},S(e.t("image.loading")),3)],2)],2)])):B("v-if",!0)],2)),e.showFooter?($(),W(s,{key:1,class:O(e.footerClass),"prefix-cls":e.prefixCls,title:e.title,description:e.description},le({_:2},[e.$slots.extra?{name:"extra",fn:D((()=>[A(e.$slots,"extra")]))}:void 0]),1032,["class","prefix-cls","title","description"])):B("v-if",!0),e.isLoaded&&e.mergePreview?($(),W(u,R({key:2,src:e.src},e.previewProps,{visible:e.mergedPreviewVisible,"render-to-body":e.renderToBody,onClose:e.onPreviewClose}),{actions:D((()=>[A(e.$slots,"preview-actions")])),_:3},16,["src","visible","render-to-body","onClose"])):B("v-if",!0)],6)}]]),Xe=I({name:"ImagePreviewGroup",components:{ImagePreview:Ee},inheritAttrs:!1,props:{renderToBody:{type:Boolean,default:!0},srcList:{type:Array},current:{type:Number},defaultCurrent:{type:Number,default:0},infinite:{type:Boolean,default:!1},visible:{type:Boolean,default:void 0},defaultVisible:{type:Boolean,default:!1},maskClosable:{type:Boolean,default:!0},closable:{type:Boolean,default:!0},actionsLayout:{type:Array,default:()=>["fullScreen","rotateRight","rotateLeft","zoomIn","zoomOut","originalSize"]},popupContainer:{type:[String,Object]}},emits:["change","update:current","visible-change","update:visible"],setup(e,{emit:t}){const{srcList:o,visible:a,defaultVisible:l,current:r,defaultCurrent:n,infinite:i}=T(e),[s,u]=se(l.value,X({value:a})),c=e=>{e!==s.value&&(t("visible-change",e),t("update:visible",e),u(e))},v=V((()=>new Map(k(null==o?void 0:o.value)?null==o?void 0:o.value.map(((e,t)=>[t,{url:e,canPreview:!0}])):[]))),d=_(new Map(v.value||[])),p=V((()=>Array.from(d.value.keys()))),f=V((()=>p.value.length));U(v,(()=>{d.value=new Map(v.value||[])}));const[m,g]=se(n.value,X({value:r})),b=e=>{e!==m.value&&(t("change",e),t("update:current",e),g(e))},w=V((()=>p.value[m.value])),C=V((()=>{var e;return null==(e=d.value.get(w.value))?void 0:e.url}));re(Ne,X({registerImageUrl:function(e,t,o){return v.value.has(e)||d.value.set(e,{url:t,canPreview:o}),function(){v.value.has(e)||d.value.delete(e)}},preview:e=>{c(!0),(e=>{const t=p.value.indexOf(e);t!==m.value&&b(t)})(e)}}));const h=V((()=>{const e=(e,t)=>{var o;for(let a=e;a<=t;a++){const e=p.value[a];if(null==(o=d.value.get(e))?void 0:o.canPreview)return a}},t=e(m.value+1,f.value-1);return y(t)&&i.value?e(0,m.value-1):t})),x=V((()=>{const e=(e,t)=>{var o;for(let a=e;a>=t;a--){const e=p.value[a];if(null==(o=d.value.get(e))?void 0:o.canPreview)return a}},t=e(m.value-1,0);return y(t)&&i.value?e(f.value-1,m.value+1):t})),P=V((()=>y(x.value)?void 0:()=>{!y(x.value)&&b(x.value)})),I=V((()=>y(h.value)?void 0:()=>{!y(h.value)&&b(h.value)}));return{mergedVisible:s,currentUrl:C,prevIndex:x,nextIndex:h,onClose(){c(!1)},groupArrowProps:X({onPrev:P,onNext:I})}}}),Ze=Object.defineProperty,He=Object.defineProperties,Ke=Object.getOwnPropertyDescriptors,qe=Object.getOwnPropertySymbols,Je=Object.prototype.hasOwnProperty,Qe=Object.prototype.propertyIsEnumerable,et=(e,t,o)=>t in e?Ze(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,tt=(e,t)=>{for(var o in t||(t={}))Je.call(t,o)&&et(e,o,t[o]);if(qe)for(var o of qe(t))Qe.call(t,o)&&et(e,o,t[o]);return e};var ot=e(Xe,[["render",function(e,t,o,a,l,r){const n=j("ImagePreview");return $(),L(F,null,[A(e.$slots,"default"),E(n,R((i=tt({},e.$attrs),s={groupArrowProps:e.groupArrowProps},He(i,Ke(s))),{"in-group":"",src:e.currentUrl,visible:e.mergedVisible,"mask-closable":e.maskClosable,closable:e.closable,"actions-layout":e.actionsLayout,"popup-container":e.popupContainer,"render-to-body":e.renderToBody,onClose:e.onClose}),le({_:2},[e.$slots.actions?{name:"actions",fn:D((()=>[A(e.$slots,"actions",{url:e.currentUrl})]))}:void 0]),1040,["src","visible","mask-closable","closable","actions-layout","popup-container","render-to-body","onClose"])],64);var i,s}]]);const at=Object.assign(Ye,{Preview:Ee,PreviewGroup:ot,install:(e,t)=>{x(e,t);const o=P(t);e.component(o+Ye.name,Ye),e.component(o+Ee.name,Ee),e.component(o+ot.name,ot),e.component(o+be.name,be)}});export{at as I,ot as a};

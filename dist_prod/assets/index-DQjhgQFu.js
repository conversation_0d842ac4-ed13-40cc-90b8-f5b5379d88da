import{I as e,g as l,ae as a,i as u,C as n,a5 as o,k as d,c as t,s,f as v}from"./index-D-8JbLQk.js";import{d as r,A as c,t as i,r as p,i as m,c as h,w as b,n as f,a3 as k,B as g}from"./vue-D-10XvVk.js";import{u as x}from"./index-DGtjsHgS.js";var y=r({name:"IconCheck",render:()=>c("svg",{"aria-hidden":"true",focusable:"false",viewBox:"0 0 1024 1024",width:"200",height:"200",fill:"currentColor"},[c("path",{d:"M877.44815445 206.10060629a64.72691371 64.72691371 0 0 0-95.14856334 4.01306852L380.73381888 685.46812814 235.22771741 533.48933518a64.72691371 64.72691371 0 0 0-92.43003222-1.03563036l-45.82665557 45.82665443a64.72691371 64.72691371 0 0 0-0.90617629 90.61767965l239.61903446 250.10479331a64.72691371 64.72691371 0 0 0 71.19960405 15.14609778 64.33855261 64.33855261 0 0 0 35.08198741-21.23042702l36.24707186-42.71976334 40.5190474-40.77795556-3.36579926-3.49525333 411.40426297-486.74638962a64.72691371 64.72691371 0 0 0-3.88361443-87.64024149l-45.3088404-45.43829334z","p-id":"840"},null)])});const C=Symbol("ArcoCheckboxGroup");var V=r({name:"Checkbox",components:{IconCheck:y,IconHover:e},props:{modelValue:{type:[Boolean,Array],default:void 0},defaultChecked:{type:Boolean,default:!1},value:{type:[String,Number,Boolean]},disabled:{type:Boolean,default:!1},indeterminate:{type:Boolean,default:!1},uninjectGroupContext:{type:Boolean,default:!1}},emits:{"update:modelValue":e=>!0,change:(e,l)=>!0},setup(o,{emit:d,slots:t}){const{disabled:s,modelValue:v}=i(o),r=l("checkbox"),k=p(),g=o.uninjectGroupContext?void 0:m(C,void 0),V="ArcoCheckboxGroup"===(null==g?void 0:g.name),{mergedDisabled:B,eventHandlers:$}=x({disabled:s}),A=p(o.defaultChecked),G=h((()=>{var e;return V?null==g?void 0:g.computedValue:null!=(e=o.modelValue)?e:A.value})),j=h((()=>{var e;return a(G.value)?G.value.includes(null==(e=o.value)||e):G.value})),w=h((()=>(null==g?void 0:g.disabled)||(null==B?void 0:B.value)||!j.value&&(null==g?void 0:g.isMaxed))),I=e=>{e.stopPropagation()},S=e=>{var l,u,n,t;const{checked:s}=e.target;let v=s;if(a(G.value)){const e=new Set(G.value);s?e.add(null==(l=o.value)||l):e.delete(null==(u=o.value)||u),v=Array.from(e)}A.value=s,V&&a(v)?null==g||g.handleChange(v,e):(d("update:modelValue",v),d("change",v,e),null==(t=null==(n=$.value)?void 0:n.onChange)||t.call(n,e)),f((()=>{k.value&&k.value.checked!==j.value&&(k.value.checked=j.value)}))},H=h((()=>[r,{[`${r}-checked`]:j.value,[`${r}-indeterminate`]:o.indeterminate,[`${r}-disabled`]:w.value}])),M=e=>{var l,a;null==(a=null==(l=$.value)?void 0:l.onFocus)||a.call(l,e)},z=e=>{var l,a;null==(a=null==(l=$.value)?void 0:l.onBlur)||a.call(l,e)};return b(v,(e=>{(u(e)||n(e))&&(A.value=!1)})),b(G,(e=>{var l;let u;u=a(e)?e.includes(null==(l=o.value)||l):e,A.value!==u&&(A.value=u),k.value&&k.value.checked!==u&&(k.value.checked=u)})),()=>{var l,a,u,n;return c("label",{"aria-disabled":w.value,class:H.value},[c("input",{ref:k,type:"checkbox",checked:j.value,value:o.value,class:`${r}-target`,disabled:w.value,onClick:I,onChange:S,onFocus:M,onBlur:z},null),null!=(n=null==(u=null!=(a=t.checkbox)?a:null==(l=null==g?void 0:g.slots)?void 0:l.checkbox)?void 0:u({checked:j.value,disabled:w.value}))?n:c(e,{class:`${r}-icon-hover`,disabled:w.value||j.value},{default:()=>[c("div",{class:`${r}-icon`},[j.value&&c(y,{class:`${r}-icon-check`},null)])]}),t.default&&c("span",{class:`${r}-label`},[t.default()])])}}}),B=r({name:"CheckboxGroup",props:{modelValue:{type:Array,default:void 0},defaultValue:{type:Array,default:()=>[]},max:{type:Number},options:{type:Array},direction:{type:String,default:"horizontal"},disabled:{type:Boolean,default:!1}},emits:{"update:modelValue":e=>!0,change:(e,l)=>!0},setup(e,{emit:u,slots:n}){const{disabled:s}=i(e),v=l("checkbox-group"),{mergedDisabled:r,eventHandlers:m}=x({disabled:s}),f=p(e.defaultValue),y=h((()=>a(e.modelValue)?e.modelValue:f.value)),B=h((()=>void 0!==e.max&&y.value.length>=e.max)),$=h((()=>{var l;return(null!=(l=e.options)?l:[]).map((e=>o(e)||d(e)?{label:e,value:e}:e))}));k(C,g({name:"ArcoCheckboxGroup",computedValue:y,disabled:r,isMaxed:B,slots:n,handleChange:(e,l)=>{var a,n;f.value=e,u("update:modelValue",e),u("change",e,l),null==(n=null==(a=m.value)?void 0:a.onChange)||n.call(a,l)}}));const A=h((()=>[v,`${v}-direction-${e.direction}`]));b((()=>e.modelValue),(e=>{a(e)?f.value=[...e]:f.value=[]}));return()=>{var e;return c("span",{class:A.value},[$.value.length>0?$.value.map((e=>{const l=y.value.includes(e.value);return c(V,{key:e.value,value:e.value,disabled:e.disabled||!l&&B.value,indeterminate:e.indeterminate,modelValue:l},{default:()=>[n.label?n.label({data:e}):t(e.label)?e.label():e.label]})})):null==(e=n.default)?void 0:e.call(n)])}}});const $=Object.assign(V,{Group:B,install:(e,l)=>{s(e,l);const a=v(l);e.component(a+V.name,V),e.component(a+B.name,B)}});export{$ as C};

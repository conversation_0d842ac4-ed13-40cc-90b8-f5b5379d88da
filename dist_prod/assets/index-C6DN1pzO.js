const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/info-frCv0bUX.js","assets/index-DOhy6BH_.js","assets/index-D-8JbLQk.js","assets/vue-D-10XvVk.js","assets/index-DxPaQOvH.css","assets/index-DB09tZwb.css","assets/index-BEo1tUsK.js","assets/pick-Ccd8Sfcm.js","assets/index-6rnfXikd.css","assets/index-DVDXfQhn.js","assets/index-DGtjsHgS.js","assets/index-BJBnsrKF.css","assets/index-DDFSMqsG.js","assets/ResizeObserver.es-CzGuHLZU.js","assets/index-RZyF5P1Y.css","assets/index-8er2yjEK.css","assets/index-DiBSSeoD.js","assets/resize-observer-Dtogi-DJ.js","assets/index-B4zgCFsq.css","assets/index-Cuq5XRs0.js","assets/index-DD6vSYIM.js","assets/index-C0ni2jp2.css","assets/index-CdWxsKz_.js","assets/index-CX9L_GU1.css","assets/index-DQjhgQFu.js","assets/index-Db7LPRu1.css","assets/index-Dbgee0nK.css","assets/index-DdMaxvYa.js","assets/index-DfEXMvnc.js","assets/use-children-components-v8i8lsOx.js","assets/index-B5FzkxT_.css","assets/index-DmW4RN1x.js","assets/index-Bl_vBcmJ.css","assets/index-DSEUEyxe.js","assets/index-O7pr3qsq.js","assets/index-Mb2FwH0E.css","assets/useLoading-D5mh7tTu.js","assets/usePagination-Dd_EW2BO.js","assets/dayjs.min-Daes5FZc.js","assets/index-CJ6Fn8S6.css","assets/permission-1DIJ-Ivi.js","assets/render-function-CAXdZVZM.js","assets/permission-DUdm3PVX.css"])))=>i.map(i=>d[i]);
import{O as e,N as a,m as t}from"./index-D-8JbLQk.js";import"./index-DOhy6BH_.js";import{I as l}from"./index-DDFSMqsG.js";import{L as o}from"./index-Cuq5XRs0.js";import"./index-DD6vSYIM.js";import"./index-DQjhgQFu.js";import{T as i,a as s,P as n}from"./index-DdMaxvYa.js";import{B as r,S as d}from"./index-DGtjsHgS.js";import{C as c}from"./index-CdWxsKz_.js";import{D as u}from"./index-DmW4RN1x.js";import{C as m,R as p}from"./index-BEo1tUsK.js";import{F as f,a as v}from"./index-DVDXfQhn.js";import{D as h}from"./index-DSEUEyxe.js";/* empty css              */import{B as g,r as y,c as w,d as _,K as k,o as b,k as x,A as C,z as j,u as z,e as R,q as N,N as S,f as P,j as O,I,y as T,p as K,J as $,af as D,M as E,h as q}from"./vue-D-10XvVk.js";import{u as A}from"./useLoading-D5mh7tTu.js";import{u as L}from"./usePagination-Dd_EW2BO.js";import{d as M}from"./dayjs.min-Daes5FZc.js";import{M as V}from"./index-O7pr3qsq.js";const B=()=>{const{loading:a,setLoading:t}=A(),{pagination:l}=L(),o=g({roleName:void 0}),i=y(void 0),s=w((()=>i.value?d.value.find((e=>e.id===i.value)):null)),n=y([]),r=w((()=>n.value.length?d.value.filter((e=>n.value.includes(e.id))):[])),d=y([]),c=async()=>{t(!0),i.value=void 0,n.value=[];try{const{data:a}=await(a=>e({url:"/admin/platform/accountRole/list",method:"post",data:a}))({...o,pageNum:l.current,pageSize:l.pageSize});d.value=a.rows.map((e=>(e.createTime=M(e.createTime).format("YYYY-MM-DD HH:mm:ss"),e))),l.total=a.total,t(!1)}catch(a){d.value=[],l.total=0,t(!1)}},u=g({roleNo:void 0,roleName:void 0,remark:void 0});return{loading:a,queryParams:o,pagination:l,rows:d,selectedId:i,selectedRow:s,selectedIds:n,selectedRows:r,selectAll:e=>{n.value=e?d.value.map((e=>e.id)):[]},rowSelect:(e,a,t)=>{n.value.includes(t.id)?n.value.splice(n.value.indexOf(t.id),1):n.value.push(t.id)},rowClick:e=>{n.value.includes(e.id)?n.value.splice(n.value.indexOf(e.id),1):n.value.push(e.id)},query:c,reset:()=>{l.current=1,Object.assign(o,{roleName:void 0}),c()},pageChange:async e=>{l.current=e,c()},pageSizeChange:async e=>{l.current=1,l.pageSize=e,c()},form:u,add:async a=>{try{await(a=>e({url:"/admin/platform/accountRole/add",method:"put",data:a}))(a)}catch(t){throw t}},detail:async a=>{try{const{data:t}=await(a=>e({url:`/admin/platform/accountRole/detail/${a}`,method:"get"}))(a),{roleNo:l,roleName:o,remark:i}=t;Object.assign(u,{roleNo:l,roleName:o,remark:i})}catch(t){throw t}},edit:async(a,t)=>{try{await((a,t)=>e({url:`/admin/platform/accountRole/edit/${a}`,method:"put",data:t}))(a,t)}catch(l){throw l}},del:async a=>{try{await(a=>e({url:`/admin/platform/accountRole/del/${a}`,method:"delete"}))(a)}catch(t){throw t}},setPermission:async(a,t)=>{try{await((a,t)=>e({url:`/admin/platform/accountRole/permission/${a}`,method:"put",data:t}))(a,t)}catch(l){throw l}}}},U={class:"page-container"},Y={class:"h-full flex flex-col gap-[18px]"},H=_({__name:"index",setup(e){const g=E((()=>t((()=>import("./info-frCv0bUX.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39])))),w=E((()=>t((()=>import("./permission-1DIJ-Ivi.js")),__vite__mapDeps([40,1,2,3,4,5,24,10,11,25,12,7,13,14,41,19,17,20,21,22,23,26,42])))),{loading:_,queryParams:A,pagination:L,rows:M,selectedId:H,selectedRow:F,selectedIds:J,selectAll:X,rowSelect:G,rowClick:Q,query:W,reset:Z,pageChange:ee,pageSizeChange:ae,add:te,edit:le,del:oe,setPermission:ie}=B(),se=k("addRef"),ne=y(!1),re=async e=>{var t,l;try{if(await(null==(l=null==(t=se.value)?void 0:t.formRef)?void 0:l.validate()))throw new Error("校验失败");await te(S(se.value.form)),a.success({title:"成功提示",content:"已添加角色",duration:1500}),e(!0),W()}catch(o){e(!1)}},de=k("editRef"),ce=y(!1),ue=async e=>{var t,l;try{if(await(null==(l=null==(t=de.value)?void 0:t.formRef)?void 0:l.validate()))throw new Error("校验失败");await le(H.value,S(de.value.form)),a.success({title:"成功提示",content:"已修改角色",duration:1500}),e(!0),W()}catch(o){e(!1)}},me=y(),pe=y(!1),fe=async e=>{try{await ie(H.value,{checkedKeys:S(me.value.checkedKeys),halfCheckedKeys:S(me.value.halfCheckedKeys)}),a.success({title:"成功提示",content:"已修改角色权限",duration:1500}),e(!0),W()}catch(t){}};return b((()=>{ee(1)})),(e,t)=>{const y=V,k=h,b=l,S=f,E=m,B=P("icon-search"),J=r,X=P("icon-refresh"),G=d,Q=p,te=v,le=c,ie=P("icon-plus"),ve=i,he=u,ge=o,ye=s,we=n;return O(),x("div",U,[C(y,{visible:z(ne),"onUpdate:visible":t[0]||(t[0]=e=>R(ne)?ne.value=e:null),width:600,"title-align":"start",title:"新增角色","cancel-button-props":{type:"outline"},"unmount-on-close":"","mask-closable":!1,"esc-to-close":!1,"on-before-ok":re,onCancel:t[1]||(t[1]=e=>ne.value=!1)},{default:j((()=>[C(z(g),{ref_key:"addRef",ref:se},null,512)])),_:1},8,["visible"]),C(y,{visible:z(ce),"onUpdate:visible":t[2]||(t[2]=e=>R(ce)?ce.value=e:null),width:600,"title-align":"start",title:"修改角色","cancel-button-props":{type:"outline"},"unmount-on-close":"","mask-closable":!1,"esc-to-close":!1,"on-before-ok":ue,onCancel:t[3]||(t[3]=e=>ce.value=!1)},{default:j((()=>[C(z(g),{ref_key:"editRef",ref:de,id:z(H)},null,8,["id"])])),_:1},8,["visible"]),C(k,{visible:z(pe),"onUpdate:visible":t[4]||(t[4]=e=>R(pe)?pe.value=e:null),width:340,title:"授权管理",unmountOnClose:"","mask-closable":!1,"esc-to-close":!1,"cancel-button-props":{type:"outline"},"on-before-ok":fe,onCancel:t[5]||(t[5]=e=>pe.value=!1)},{default:j((()=>{var e;return[C(z(w),{ref_key:"permissionRef",ref:me,permission:(null==(e=z(F))?void 0:e.permission)??{checkedKeys:[],halfCheckedKeys:[]}},null,8,["permission"])]})),_:1},8,["visible"]),N("div",Y,[C(le,{bordered:!1},{default:j((()=>[C(te,{model:z(A),"auto-label-width":""},{default:j((()=>[C(Q,{gutter:16},{default:j((()=>[C(E,{span:6},{default:j((()=>[C(S,{"show-colon":"",label:"角色名称",field:"roleName"},{default:j((()=>[C(b,{modelValue:z(A).roleName,"onUpdate:modelValue":t[6]||(t[6]=e=>z(A).roleName=e),placeholder:`${e.$inputPlaceholder}角色名称`,"allow-clear":""},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),C(E,{span:18},{default:j((()=>[C(S,{"hide-label":""},{default:j((()=>[C(G,{size:18},{default:j((()=>[C(J,{type:"primary",onClick:t[7]||(t[7]=e=>z(ee)(1))},{icon:j((()=>[C(B)])),default:j((()=>[t[9]||(t[9]=I(" 查询 "))])),_:1}),C(J,{type:"outline",onClick:z(Z)},{icon:j((()=>[C(X)])),default:j((()=>[t[10]||(t[10]=I(" 重置 "))])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),C(le,{bordered:!1,class:"flex-1 overflow-y-hidden","body-style":{height:"100%"}},{actions:j((()=>[z(L).total?(O(),T(we,{key:0,current:z(L).current,"page-size":z(L).pageSize,"show-total":z(L).showTotal,"show-page-size":z(L).showPageSize,"page-size-options":z(L).pageSizeOptions,total:z(L).total,onChange:z(ee),onPageSizeChange:z(ae)},null,8,["current","page-size","show-total","show-page-size","page-size-options","total","onChange","onPageSizeChange"])):K("",!0)])),default:j((()=>[C(Q,{class:"mb-[12px]"},{default:j((()=>[C(E,{span:16},{default:j((()=>[C(G,null,{default:j((()=>[C(J,{type:"primary",onClick:t[8]||(t[8]=()=>{H.value=void 0,ne.value=!0})},{icon:j((()=>[C(ie)])),default:j((()=>[t[11]||(t[11]=I(" 添加角色 "))])),_:1})])),_:1})])),_:1})])),_:1}),C(ye,{size:"large","row-key":"id",loading:z(_),pagination:!1,data:z(M),bordered:{cell:!0},scroll:{y:"calc(100% - 96px)"}},{columns:j((()=>[C(ve,{align:"center",title:"序号",width:80},{cell:j((({rowIndex:e})=>[I($(z(L).pageSize*(z(L).current-1)+e+1),1)])),_:1}),C(ve,{align:"center",title:"角色编号",width:150,"data-index":"roleNo"}),C(ve,{align:"center",title:"角色名称",width:200,ellipsis:"",tooltip:"","data-index":"roleName"}),C(ve,{align:"center",title:"备注",width:200,ellipsis:"",tooltip:"","data-index":"remark"}),C(ve,{align:"center",title:"创建时间",width:180,"data-index":"createTime"}),C(ve,{align:"center",title:"操作",width:300,fixed:"right"},{cell:j((({record:e})=>[C(G,null,{split:j((()=>[C(he,{direction:"vertical"})])),default:j((()=>[C(ge,{onClick:D((()=>{H.value=e.id,pe.value=!0}),["stop"])},{default:j((()=>t[12]||(t[12]=[I(" 授权 ")]))),_:2},1032,["onClick"]),C(ge,{onClick:D((()=>{H.value=e.id,ce.value=!0}),["stop"])},{default:j((()=>t[13]||(t[13]=[I(" 编辑 ")]))),_:2},1032,["onClick"]),C(ge,{status:"danger",onClick:D((t=>(e=>{V.warning({title:"提示",content:()=>q("div",{class:"text-center"},`确定删除【${e.roleName}】？`),maskClosable:!1,escToClose:!1,hideCancel:!1,cancelButtonProps:{type:"outline"},onBeforeOk:async t=>{try{await oe(e.id),a.success({title:"成功提示",content:"已删除角色",duration:1500}),t(!0),W()}catch(l){t(!1)}}})})(e)),["stop"])},{default:j((()=>t[14]||(t[14]=[I("删除")]))),_:2},1032,["onClick"])])),_:2},1024)])),_:1})])),_:1},8,["loading","data"])])),_:1})])])}}}),F=Object.freeze(Object.defineProperty({__proto__:null,default:H},Symbol.toStringTag,{value:"Module"}));export{F as i,B as u};

import{_ as e,I as t,a,g as l,o as i,x as o,r as n,U as s,i as u,k as r,V as d,s as v,f as c}from"./index-D-8JbLQk.js";import{d as p,i as f,c as y,f as b,j as m,k as g,q as h,m as $,l as k,y as x,z as B,A as S,af as z,p as C,v as T,H as j,t as O,r as w,o as H,n as P,S as I,O as K,w as A,B as q,a3 as L,b as R,g as W}from"./vue-D-10XvVk.js";import{R as N}from"./resize-observer-Dtogi-DJ.js";import{u as D}from"./index-DOhy6BH_.js";import{u as M}from"./use-children-components-v8i8lsOx.js";const E=Symbol("ArcoTabs");var F=e(p({name:"TabsTab",components:{IconHover:t,IconClose:a},props:{tab:{type:Object,required:!0},active:Boolean,editable:Boolean},emits:["click","delete"],setup(e,{emit:t}){const a=l("tabs-tab"),i=f(E,{}),o=a=>{e.tab.disabled||t("click",e.tab.key,a)},n=e=>{"Enter"===e.key&&o(e)},s=y((()=>Object.assign("click"===i.trigger?{onClick:o}:{onMouseover:o},{onKeydown:n}))),u=y((()=>[a,{[`${a}-active`]:e.active,[`${a}-closable`]:e.editable&&e.tab.closable,[`${a}-disabled`]:e.tab.disabled}]));return{prefixCls:a,cls:u,eventHandlers:s,handleDelete:a=>{e.tab.disabled||t("delete",e.tab.key,a)}}}}),[["render",function(e,t,a,l,i,o){const n=b("icon-close"),s=b("icon-hover");return m(),g("div",T({tabindex:"0",class:e.cls},e.eventHandlers),[h("span",{class:k(`${e.prefixCls}-title`)},[$(e.$slots,"default")],2),e.editable&&e.tab.closable?(m(),x(s,{key:0,class:k(`${e.prefixCls}-close-btn`),onClick:z(e.handleDelete,["stop"])},{default:B((()=>[S(n)])),_:1},8,["class","onClick"])):C("v-if",!0)],16)}]]);var U=p({name:"TabsButton",props:{type:{type:String,default:"next"},direction:{type:String,default:"horizontal"},disabled:{type:Boolean,default:!1},onClick:{type:Function}},emits:["click"],setup(e,{emit:a}){const u=l("tabs-nav-button"),r=t=>{e.disabled||a("click",e.type,t)},d=y((()=>[u,{[`${u}-disabled`]:e.disabled,[`${u}-left`]:"horizontal"===e.direction&&"previous"===e.type,[`${u}-right`]:"horizontal"===e.direction&&"next"===e.type,[`${u}-up`]:"vertical"===e.direction&&"previous"===e.type,[`${u}-down`]:"vertical"===e.direction&&"next"===e.type}]));return()=>{let a;return S("div",{class:d.value,onClick:r},[S(t,{disabled:e.disabled},(l=a="horizontal"===e.direction?"next"===e.type?S(i,null,null):S(o,null,null):"next"===e.type?S(n,null,null):S(s,null,null),"function"==typeof l||"[object Object]"===Object.prototype.toString.call(l)&&!j(l)?a:{default:()=>[a]}))]);var l}}});var X=e(p({name:"TabsNavInk",props:{activeTabRef:{type:Object},direction:{type:String},disabled:Boolean,animation:Boolean},setup(e){const{activeTabRef:t}=O(e),a=l("tabs-nav-ink"),i=w(0),o=w(0),n=y((()=>"vertical"===e.direction?{top:`${i.value}px`,height:`${o.value}px`}:{left:`${i.value}px`,width:`${o.value}px`})),s=()=>{if(t.value){const a="vertical"===e.direction?t.value.offsetTop:t.value.offsetLeft,l="vertical"===e.direction?t.value.offsetHeight:t.value.offsetWidth;a===i.value&&l===o.value||(i.value=a,o.value=l)}};H((()=>{P((()=>s()))})),I((()=>{s()}));const u=y((()=>[a,{[`${a}-animation`]:e.animation,[`${a}-disabled`]:e.disabled}]));return{prefixCls:a,cls:u,style:n}}}),[["render",function(e,t,a,l,i,o){return m(),g("div",{class:k(e.cls),style:K(e.style)},null,6)}]]),Y=p({name:"TabsNav",props:{tabs:{type:Array,required:!0},direction:{type:String,required:!0},type:{type:String,required:!0},activeKey:{type:[String,Number]},activeIndex:{type:Number,required:!0},position:{type:String,required:!0},size:{type:String,required:!0},showAddButton:{type:Boolean,default:!1},editable:{type:Boolean,default:!1},animation:{type:Boolean,required:!0},headerPadding:{type:Boolean,default:!0},scrollPosition:{type:String,default:"auto"}},emits:["click","add","delete"],setup(e,{emit:a,slots:i}){const{tabs:o,activeKey:n,activeIndex:s,direction:v,scrollPosition:c}=O(e),p=l("tabs-nav"),f=w(),b=w(),m=w({}),g=y((()=>{if(!u(n.value))return m.value[n.value]})),h=w(),$=y((()=>e.editable&&["line","card","card-gutter"].includes(e.type))),k=w(!1),x=w(0),B=w(0),z=w(0),C=()=>{var e,t,a;k.value=T(),k.value?(x.value=null!=(a="vertical"===v.value?null==(e=f.value)?void 0:e.offsetHeight:null==(t=f.value)?void 0:t.offsetWidth)?a:0,B.value=b.value&&f.value?"vertical"===v.value?b.value.offsetHeight-f.value.offsetHeight:b.value.offsetWidth-f.value.offsetWidth:0,z.value>B.value&&(z.value=B.value)):z.value=0},T=()=>!(!f.value||!b.value)&&("vertical"===e.direction?b.value.offsetHeight>f.value.offsetHeight:b.value.offsetWidth>f.value.offsetWidth),j=e=>{(!f.value||!b.value||e<0)&&(e=0),z.value=Math.min(e,B.value)},I=e=>{if(!k.value)return;e.preventDefault();const{deltaX:t,deltaY:a}=e;Math.abs(t)>Math.abs(a)?j(z.value+t):j(z.value+a)},K=(e,t)=>{a("click",e,t)},q=(e,t)=>{a("delete",e,t)},L=e=>{const t="previous"===e?z.value-x.value:z.value+x.value;j(t)},R=()=>{C(),h.value&&h.value.$forceUpdate()};A(o,(()=>{P((()=>{C()}))})),A([s,c],(()=>{setTimeout((()=>{(()=>{if(!g.value||!f.value||!k.value)return;((e,t)=>{const{scrollTop:a,scrollLeft:l}=e;"horizontal"===t&&l&&e.scrollTo({left:-1*l}),"vertical"===t&&a&&e.scrollTo({top:-1*a})})(f.value,v.value);const e="horizontal"===v.value,t=e?"offsetLeft":"offsetTop",a=e?"offsetWidth":"offsetHeight",l=g.value[t],i=g.value[a],o=f.value[a],n=window.getComputedStyle(g.value),s=e?"end"===c.value?"marginRight":"marginLeft":"end"===c.value?"marginBottom":"marginTop",u=parseFloat(n[s])||0;"auto"===c.value?l<z.value?j(l-u):l+i>z.value+o&&j(l+i-o+u):"center"===c.value?j(l+(i-o+u)/2):"start"===c.value?j(l-u):"end"===c.value?j(l+i-o+u):r(c.value)&&j(l-c.value)})()}),0)})),H((()=>{C()}));const W=()=>$.value&&e.showAddButton?S("div",{class:`${p}-add-btn`,onClick:e=>a("add",e)},[S(t,null,{default:()=>[S(d,null,null)]})]):null,D=y((()=>[p,`${p}-${e.direction}`,`${p}-${e.position}`,`${p}-size-${e.size}`,`${p}-type-${e.type}`])),M=y((()=>[`${p}-tab-list`,{[`${p}-tab-list-no-padding`]:!e.headerPadding&&["line","text"].includes(e.type)&&"horizontal"===e.direction}])),E=y((()=>(({direction:e,type:t,offset:a})=>"vertical"===e?{transform:`translateY(${-a}px)`}:{transform:`translateX(${-a}px)`})({direction:e.direction,type:e.type,offset:z.value}))),Y=y((()=>[`${p}-tab`,{[`${p}-tab-scroll`]:k.value}]));return()=>{var t;return S("div",{class:D.value},[k.value&&S(U,{type:"previous",direction:e.direction,disabled:z.value<=0,onClick:L},null),S(N,{onResize:()=>C()},{default:()=>[S("div",{class:Y.value,ref:f,onWheel:I},[S(N,{onResize:R},{default:()=>[S("div",{ref:b,class:M.value,style:E.value},[e.tabs.map(((t,a)=>S(F,{key:t.key,ref:e=>{(null==e?void 0:e.$el)&&(m.value[t.key]=e.$el)},active:t.key===n.value,tab:t,editable:e.editable,onClick:K,onDelete:q},{default:()=>{var e,a,l;return[null!=(l=null==(a=(e=t.slots).title)?void 0:a.call(e))?l:t.title]}}))),"line"===e.type&&g.value&&S(X,{ref:h,activeTabRef:g.value,direction:e.direction,disabled:!1,animation:e.animation},null)])]}),!k.value&&W()])]}),k.value&&S(U,{type:"next",direction:e.direction,disabled:z.value>=B.value,onClick:L},null),S("div",{class:`${p}-extra`},[k.value&&W(),null==(t=i.extra)?void 0:t.call(i)])])}}}),_=p({name:"Tabs",props:{activeKey:{type:[String,Number],default:void 0},defaultActiveKey:{type:[String,Number],default:void 0},position:{type:String,default:"top"},size:{type:String},type:{type:String,default:"line"},direction:{type:String,default:"horizontal"},editable:{type:Boolean,default:!1},showAddButton:{type:Boolean,default:!1},destroyOnHide:{type:Boolean,default:!1},lazyLoad:{type:Boolean,default:!1},justify:{type:Boolean,default:!1},animation:{type:Boolean,default:!1},headerPadding:{type:Boolean,default:!0},autoSwitch:{type:Boolean,default:!1},hideContent:{type:Boolean,default:!1},trigger:{type:String,default:"click"},scrollPosition:{type:[String,Number],default:"auto"}},emits:{"update:activeKey":e=>!0,change:e=>!0,tabClick:(e,t)=>!0,add:e=>!0,delete:(e,t)=>!0},setup(e,{emit:t,slots:a}){const{size:i,lazyLoad:o,destroyOnHide:n,trigger:s}=O(e),r=l("tabs"),{mergedSize:d}=D(i),v=y((()=>"vertical"===e.direction?"left":e.position)),c=y((()=>["left","right"].includes(v.value)?"vertical":"horizontal")),{children:p,components:f}=M("TabPane"),b=q(new Map),m=y((()=>{const e=[];return f.value.forEach((t=>{const a=b.get(t);a&&e.push(a)})),e})),g=y((()=>m.value.map((e=>e.key)))),h=w(e.defaultActiveKey),$=y((()=>{var t;const a=null!=(t=e.activeKey)?t:h.value;return u(a)?g.value[0]:a})),k=y((()=>{const e=g.value.indexOf($.value);return-1===e?0:e}));L(E,q({lazyLoad:o,destroyOnHide:n,activeKey:$,addItem:(e,t)=>{b.set(e,t)},removeItem:e=>{b.delete(e)},trigger:s}));const x=e=>{e!==$.value&&(h.value=e,t("update:activeKey",e),t("change",e))},B=(e,a)=>{x(e),t("tabClick",e,a)},z=a=>{t("add",a),e.autoSwitch&&P((()=>{const e=g.value[g.value.length-1];x(e)}))},C=(e,a)=>{t("delete",e,a)},T=()=>S("div",{class:[`${r}-content`,{[`${r}-content-hide`]:e.hideContent}]},[S("div",{class:[`${r}-content-list`,{[`${r}-content-animation`]:e.animation}],style:{marginLeft:`-${100*k.value}%`}},[p.value])]),j=y((()=>[r,`${r}-${c.value}`,`${r}-${v.value}`,`${r}-type-${e.type}`,`${r}-size-${d.value}`,{[`${r}-justify`]:e.justify}]));return()=>{var t;return p.value=null==(t=a.default)?void 0:t.call(a),S("div",{class:j.value},["bottom"===v.value&&T(),S(Y,{tabs:m.value,activeKey:$.value,activeIndex:k.value,direction:c.value,position:v.value,editable:e.editable,animation:e.animation,showAddButton:e.showAddButton,headerPadding:e.headerPadding,scrollPosition:e.scrollPosition,size:d.value,type:e.type,onClick:B,onAdd:z,onDelete:C},{extra:a.extra}),"bottom"!==v.value&&T()])}}}),V=Object.defineProperty,G=Object.getOwnPropertySymbols,J=Object.prototype.hasOwnProperty,Q=Object.prototype.propertyIsEnumerable,Z=(e,t,a)=>t in e?V(e,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[t]=a;var ee=e(p({name:"TabPane",props:{title:String,disabled:{type:Boolean,default:!1},closable:{type:Boolean,default:!0},destroyOnHide:{type:Boolean,default:!1}},setup(e,{slots:t}){var a;const{title:i,disabled:o,closable:n}=O(e),s=W(),u=l("tabs"),r=f(E,{}),d=w(),v=y((()=>null==s?void 0:s.vnode.key)),c=y((()=>v.value===r.activeKey)),p=w(!r.lazyLoad||c.value),b=q({key:v,title:i,disabled:o,closable:n,slots:t});return(null==s?void 0:s.uid)&&(null==(a=r.addItem)||a.call(r,s.uid,b)),R((()=>{var e;(null==s?void 0:s.uid)&&(null==(e=r.removeItem)||e.call(r,s.uid))})),A(c,(t=>{t?p.value||(p.value=!0):(e.destroyOnHide||r.destroyOnHide)&&(p.value=!1)})),I((()=>{b.slots=((e,t)=>{for(var a in t||(t={}))J.call(t,a)&&Z(e,a,t[a]);if(G)for(var a of G(t))Q.call(t,a)&&Z(e,a,t[a]);return e})({},t)})),{prefixCls:u,active:c,itemRef:d,mounted:p}}}),[["render",function(e,t,a,l,i,o){return m(),g("div",{ref:"itemRef",class:k([`${e.prefixCls}-content-item`,{[`${e.prefixCls}-content-item-active`]:e.active}])},[e.mounted?(m(),g("div",{key:0,class:k(`${e.prefixCls}-pane`)},[$(e.$slots,"default")],2)):C("v-if",!0)],2)}]]);const te=Object.assign(_,{TabPane:ee,install:(e,t)=>{v(e,t);const a=c(t);e.component(a+_.name,_),e.component(a+ee.name,ee)}});export{te as T,ee as a};

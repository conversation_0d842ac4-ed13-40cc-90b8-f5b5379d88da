import"./index-DOhy6BH_.js";import{T as e}from"./index-Cuq5XRs0.js";import{C as a}from"./index-CdWxsKz_.js";import{D as l,a as t}from"./index-DIKBiUsz.js";import{B as r}from"./index-DGtjsHgS.js";import"./index-DD6vSYIM.js";import{U as o,_ as s}from"./index-HLwNT5T9.js";import{I as n}from"./index-CuYx5vtf.js";import{r as d}from"./apiUpload-DpATemHF.js";import{d as i,al as u,am as c,r as m,f as p,j as f,y as b,z as _,u as v,A as y,I as g,J as k,p as x,k as C,q as j,h,ae as N,o as P,K as V,F as I}from"./vue-D-10XvVk.js";import{M as S}from"./index-O7pr3qsq.js";import{_ as z}from"./_plugin-vue_export-helper-BCo6x5W8.js";import{n as U}from"./index-D-8JbLQk.js";import{u as w}from"./useCommon-BuUbRw8e.js";import{u as B}from"./useAddress-CutR4aE-.js";import{u as A}from"./hooks-BLzyyGa3.js";for(var M,R=[],T=0;T<256;++T)R.push((T+256).toString(16).slice(1));var D=new Uint8Array(16);function O(){if(!M&&!(M="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return M(D)}const L={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function E(e,a,l){if(L.randomUUID&&!a&&!e)return L.randomUUID();var t=(e=e||{}).random||(e.rng||O)();return t[6]=15&t[6]|64,t[8]=63&t[8]|128,function(e,a=0){return(R[e[a+0]]+R[e[a+1]]+R[e[a+2]]+R[e[a+3]]+"-"+R[e[a+4]]+R[e[a+5]]+"-"+R[e[a+6]]+R[e[a+7]]+"-"+R[e[a+8]]+R[e[a+9]]+"-"+R[e[a+10]]+R[e[a+11]]+R[e[a+12]]+R[e[a+13]]+R[e[a+14]]+R[e[a+15]]).toLowerCase()}(t)}const q={class:"flex flex-1 justify-between items-center px-[12px] py-[8px] bg-[var(--color-fill-1)] rounded-md"},F={class:"flex items-center gap-x-[16px]"},H={class:"max-w-[300px] truncate"},G={class:"text-xs text-gray-500"},J=z(i({__name:"index",props:u({accept:{},title:{},disabled:{type:Boolean},limit:{}},{modelValue:{default:()=>[]},modelModifiers:{}}),emits:u(["uploadSuccess","uploadError"],["update:modelValue"]),setup(e,{emit:a}){const l=a,t=c(e,"modelValue"),s=m(!1),i=e=>{u(e)},u=async e=>{const{onSuccess:a,onError:r,fileItem:o}=e;try{s.value=!0;const e=await d(o.file);t.value=[...t.value,{uid:E(),name:o.name,size:o.file.size,url:e}],s.value=!1,a(e),l("uploadSuccess",t.value)}catch(n){l("uploadError"),s.value=!1,r({error:n})}};return(e,a)=>{const l=p("icon-upload"),d=r,u=n,c=p("icon-loading"),m=p("icon-check"),N=p("icon-delete"),P=o;return f(),b(P,{accept:e.accept,"file-list":t.value,multiple:"",limit:e.limit||100,"custom-request":i},{"upload-button":_((()=>[e.disabled?x("",!0):(f(),b(d,{key:0,loading:v(s),type:"primary"},{icon:_((()=>[y(l)])),default:_((()=>[g(" "+k(e.title),1)])),_:1},8,["loading"]))])),"upload-item":_((({fileItem:a,index:l})=>[(f(),C("div",{key:l,class:"upload-list-item"},[j("div",q,[j("div",F,[y(u,{class:"cursor-pointer",width:"40",height:"40",fit:"contain",src:a.url},null,8,["src"]),j("div",null,[j("div",H,k(a.name),1),j("span",G,k(a.size?Math.round(a.size/1024/1024*100)/100:0)+"M",1)])]),a.percent<1?(f(),b(c,{key:0,size:18})):(f(),b(m,{key:1,size:18,class:"!text-green-500"}))]),e.disabled?x("",!0):(f(),b(N,{key:0,size:18,class:"cursor-pointer",onClick:()=>{a.percent<1||(e=>{new Promise(((a,l)=>{S.warning({title:"提示",content:()=>h("div",{class:"text-center"},`确定删除【${e.name}】？`),maskClosable:!1,escToClose:!1,hideCancel:!1,cancelButtonProps:{type:"outline"},onOk:()=>{t.value=t.value.filter((a=>a.uid!==e.uid)),a(!0)},onCancel:()=>l("cancel")})}))})(a)}},null,8,["onClick"]))]))])),_:1},8,["accept","file-list","limit"])}}}),[["__scopeId","data-v-c482604d"]]),K={class:"h-[700px] overflow-y-scroll no-scrollbar"},Z={class:"h-full flex flex-col gap-[18px]"},$={key:1},Q={key:1},W=["innerHTML"],X={key:1},Y={key:1},ee={key:1},ae={key:1},le=i({__name:"detail",props:{id:{}},setup(r,{expose:o}){const n=N(),d=Number("0"),i=r,{storeBusinessStateOptions:u}=w(),{provinces:c,cities:m,setCities:p,areas:h,setAreas:S}=B(),{form:z,detail:M}=A(1);P((async()=>{await M(i.id),p(z.provinceCode),S(z.provinceCode,z.cityCode)}));return o({formRef:V("formRef"),form:z}),(r,o)=>{const i=s,c=l,m=J,p=t,h=a,N=e;return f(),C("div",K,[j("div",Z,[y(h,{bordered:!1,title:"装修信息"},{default:_((()=>[y(p,{bordered:"",size:"large",column:1},{default:_((()=>[y(c,{label:"店铺LOGO"},{default:_((()=>[y(i,{modelValue:v(z).logo,disabled:""},null,8,["modelValue"])])),_:1}),y(c,{label:"店铺背景图片"},{default:_((()=>{var e;return[(null==(e=v(z).storeBackgroundPictures)?void 0:e.length)?(f(),b(m,{key:0,modelValue:v(z).storeBackgroundPictures,disabled:""},null,8,["modelValue"])):(f(),C("span",$,"-"))]})),_:1}),y(c,{label:"店铺宣传图片"},{default:_((()=>{var e;return[(null==(e=v(z).storePropagandaPictures)?void 0:e.length)?(f(),b(m,{key:0,modelValue:v(z).storePropagandaPictures,disabled:""},null,8,["modelValue"])):(f(),C("span",Q,"-"))]})),_:1}),y(c,{label:"店铺介绍"},{default:_((()=>[v(z).storeIntroduce?(f(),C("div",{key:0,innerHTML:v(z).storeIntroduce},null,8,W)):(f(),C("span",X,"-"))])),_:1})])),_:1})])),_:1}),y(h,{bordered:!1,title:"基本信息"},{default:_((()=>[y(p,{bordered:"",size:"large",column:2},{default:_((()=>[y(c,{label:"店铺编号"},{default:_((()=>[g(k(v(z).storeNo),1)])),_:1}),y(c,{label:"店铺名称"},{default:_((()=>[g(k(v(z).name),1)])),_:1}),y(c,{label:"联系人姓名"},{default:_((()=>[g(k(v(z).contactName),1)])),_:1}),y(c,{label:"联系电话"},{default:_((()=>{var e;return[g(k(v(U)(null==(e=v(z).contactNumber)?void 0:e.toString())),1)]})),_:1}),y(c,{label:"联系电话2"},{default:_((()=>{var e;return[g(k(v(U)((null==(e=v(z).contactNumber2)?void 0:e.toString())??"-")),1)]})),_:1}),y(c,{label:"店铺状态"},{default:_((()=>{var e;return["StoreApprove"===v(n).name?(f(),C(I,{key:0},[g("-")],64)):x("",!0),"StoreList"===v(n).name?(f(),b(N,{key:1,color:(null==(e=v(u).find((e=>e.value===v(z).businessState)))?void 0:e.color)??""},{default:_((()=>{var e;return[g(k((null==(e=v(u).find((e=>e.value===v(z).businessState)))?void 0:e.label)??""),1)]})),_:1},8,["color"])):x("",!0)]})),_:1}),v(d)?(f(),b(c,{key:0,label:"平台佣金比例"},{default:_((()=>[g(k(v(z).platformCommissionRate)+" %",1)])),_:1})):x("",!0),y(c,{label:"店铺分类",span:2},{default:_((()=>[g(k(v(z).storeCategoryName)+" - "+k(v(z).storeSubCategoryName),1)])),_:1}),y(c,{label:"店铺地址",span:2},{default:_((()=>[g(k(v(z).provinceName)+" - "+k(v(z).cityName)+" - "+k(v(z).areaName)+" - "+k(v(z).detailAddress),1)])),_:1})])),_:1})])),_:1}),y(h,{bordered:!1,title:"企业信息"},{default:_((()=>[y(p,{bordered:"",size:"large",column:2},{default:_((()=>[y(c,{label:"营业执照"},{default:_((()=>[v(z).license?(f(),b(i,{key:0,modelValue:v(z).license,disabled:""},null,8,["modelValue"])):(f(),C("span",Y,"-"))])),_:1}),y(c,{label:"法人身份证人像面"},{default:_((()=>[v(z).legalPersonIdCardFront?(f(),b(i,{key:0,modelValue:v(z).legalPersonIdCardFront,disabled:""},null,8,["modelValue"])):(f(),C("span",ee,"-"))])),_:1}),y(c,{label:"法人身份证国徽面"},{default:_((()=>[v(z).legalPersonIdCardBack?(f(),b(i,{key:0,modelValue:v(z).legalPersonIdCardBack,disabled:""},null,8,["modelValue"])):(f(),C("span",ae,"-"))])),_:1}),y(c,{label:"企业性质"},{default:_((()=>[g(k(1===v(z).enterpriseNature?"个体工商户":2===v(z).enterpriseNature?"企业":3===v(z).enterpriseNature?"政府机关":4===v(z).enterpriseNature?"事业单位":5===v(z).enterpriseNature?"其他组织":"-"),1)])),_:1}),y(c,{label:"促进会职务"},{default:_((()=>[g(k(v(z).enterpriseName||"-"),1)])),_:1}),y(c,{label:"企业法人"},{default:_((()=>[g(k(v(z).legalPerson||"-"),1)])),_:1}),y(c,{label:"企业法人身份证号"},{default:_((()=>[g(k(v(z).legalPersonIdentityCard||"-"),1)])),_:1}),y(c,{label:"企业法人手机号"},{default:_((()=>[g(k(v(z).legalPersonPhone||"-"),1)])),_:1}),y(c,{label:"统一社会信用代码"},{default:_((()=>[g(k(v(z).unifiedSocialCreditCode||"-"),1)])),_:1}),y(c,{label:"注册地址"},{default:_((()=>[g(k(v(z).registeredAddress||"-"),1)])),_:1}),y(c,{label:"经营范围",span:2},{default:_((()=>[g(k(v(z).businessScope||"-"),1)])),_:1})])),_:1})])),_:1}),y(h,{bordered:!1,title:"开票信息"},{default:_((()=>[y(p,{bordered:"",size:"large",column:2},{default:_((()=>[y(c,{label:"发票抬头"},{default:_((()=>[g(k(v(z).invoiceTitle||"-"),1)])),_:1}),y(c,{label:"税号"},{default:_((()=>[g(k(v(z).invoiceTaxNumber||"-"),1)])),_:1}),y(c,{label:"开户行"},{default:_((()=>[g(k(v(z).bankName||"-"),1)])),_:1}),y(c,{label:"开户行支行名称"},{default:_((()=>[g(k(v(z).bankBranchName||"-"),1)])),_:1}),y(c,{label:"开户账号"},{default:_((()=>[g(k(v(z).bankAccount||"-"),1)])),_:1}),y(c,{label:"银行预留手机号"},{default:_((()=>[g(k(v(z).bankReservePhone||"-"),1)])),_:1})])),_:1})])),_:1})])])}}}),te=Object.freeze(Object.defineProperty({__proto__:null,default:le},Symbol.toStringTag,{value:"Module"}));export{J as _,te as d};

import{u as e}from"./useLoading-D5mh7tTu.js";import{u as a}from"./usePagination-Dd_EW2BO.js";import{O as t,Q as i,R as o,S as r,T as d,N as s}from"./index-D-8JbLQk.js";import{d as n}from"./dayjs.min-Daes5FZc.js";import{B as c,r as m,c as l}from"./vue-D-10XvVk.js";const u=u=>{const{loading:v,setLoading:p}=e(),{pagination:h}=a(),y=c({userNo:void 0,nickname:void 0,mobile:void 0,state:void 0,createTime:void 0}),g=m(void 0),f=l((()=>g.value?b.value.find((e=>e.id===g.value)):null)),w=m([]),N=l((()=>w.value.length?b.value.filter((e=>w.value.includes(e.id))):[])),b=m([]),C=async()=>{var e;p(!0),g.value=void 0,w.value=[];try{const{data:a}=await(e=>t({url:"/admin/platform/user/list",method:"post",data:e}))({...y,...u,mobile:null==(e=y.mobile)?void 0:e.toString(),pageNum:h.current,pageSize:h.pageSize});b.value=a.rows.map((e=>(e.createTime=n(e.createTime).format("YYYY-MM-DD HH:mm:ss"),e.delTime=e.delTime?n(e.createTime).format("YYYY-MM-DD HH:mm:ss"):"",e))),h.total=a.total,p(!1)}catch(a){b.value=[],h.total=0,p(!1)}},T=c({avatar:void 0,userNo:void 0,nickname:void 0,mobile:void 0,birthday:void 0,gender:void 0,state:void 0,provinceCode:void 0,provinceName:void 0,cityCode:void 0,cityName:void 0,areaCode:void 0,areaName:void 0,createTime:void 0,inviteUsers:[]});return{loading:v,queryParams:y,pagination:h,rows:b,selectedId:g,selectedRow:f,selectedIds:w,selectedRows:N,selectAll:e=>{w.value=e?b.value.map((e=>e.id)):[]},rowSelect:(e,a,t)=>{w.value.includes(t.id)?w.value.splice(w.value.indexOf(t.id),1):w.value.push(t.id)},rowClick:e=>{w.value.includes(e.id)?w.value.splice(w.value.indexOf(e.id),1):w.value.push(e.id)},query:C,reset:()=>{h.current=1,Object.assign(y,{userNo:void 0,nickname:void 0,mobile:void 0,state:void 0,createTime:void 0}),C()},pageChange:async e=>{h.current=e,C()},pageSizeChange:async e=>{h.current=1,h.pageSize=e,C()},form:T,add:async e=>{try{await(e=>t({url:"/admin/platform/user/add",method:"put",data:e}))(e)}catch(a){throw a}},detail:async e=>{try{const{data:a}=await(e=>t({url:`/admin/platform/user/detail/${e}`,method:"get"}))(e),{avatar:i,userNo:o,nickname:r,mobile:d,birthday:s,gender:c,state:m,provinceCode:l,provinceName:u,cityCode:v,cityName:p,areaCode:h,areaName:y,createTime:g,inviteUsers:f}=a;Object.assign(T,{avatar:i,userNo:o,nickname:r,mobile:d,birthday:s,gender:c,state:m,provinceCode:l,provinceName:u,cityCode:v,cityName:p,areaCode:h,areaName:y,createTime:n(g).format("YYYY-MM-DD HH:mm:ss"),inviteUsers:f})}catch(a){throw a}},edit:async(e,a)=>{try{await((e,a)=>t({url:`/admin/platform/user/edit/${e}`,method:"put",data:a}))(e,a)}catch(i){throw i}},del:async e=>{try{await(e=>t({url:`/admin/platform/user/del/${e}`,method:"delete"}))(e)}catch(a){throw a}},recover:async e=>{try{await(e=>t({url:`/admin/platform/user/recover/${e}`,method:"delete"}))(e)}catch(a){throw a}},changeState:async(e,a)=>{try{await((e,a)=>t({url:`/admin/platform/user/changeState/${e}`,method:"put",data:{state:a}}))(e,a)}catch(i){throw i}},exports:async()=>{try{await new Promise(((e,a)=>{i({url:o+"/admin/platform/user/exports",method:"post",responseType:"blob",headers:{Authorization:"Bearer "+r()}}).then((t=>{"application/vnd.openxmlformats"===t.data.type?(d(t,"application/vnd.openxmlformats;charset=utf-8","用户信息.xlsx"),e(!0)):(s.warning({title:"下载出错",content:"请稍后重试",duration:3e3}),a())})).catch((()=>{a()}))}))}catch(e){throw e}}}};export{u};

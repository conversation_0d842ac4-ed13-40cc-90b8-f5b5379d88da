const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/detail-BEEYEW2J.js","assets/index-DOhy6BH_.js","assets/index-D-8JbLQk.js","assets/vue-D-10XvVk.js","assets/index-DxPaQOvH.css","assets/index-DB09tZwb.css","assets/index-BEo1tUsK.js","assets/pick-Ccd8Sfcm.js","assets/index-6rnfXikd.css","assets/index-DVDXfQhn.js","assets/index-DGtjsHgS.js","assets/index-BJBnsrKF.css","assets/index-DDFSMqsG.js","assets/ResizeObserver.es-CzGuHLZU.js","assets/index-RZyF5P1Y.css","assets/index-8er2yjEK.css","assets/index-Cuq5XRs0.js","assets/resize-observer-Dtogi-DJ.js","assets/index-DD6vSYIM.js","assets/index-C0ni2jp2.css","assets/index-CdWxsKz_.js","assets/index-CX9L_GU1.css","assets/index-DQjhgQFu.js","assets/index-Db7LPRu1.css","assets/index-Dbgee0nK.css","assets/index-dpn1_5z1.js","assets/dayjs.min-Daes5FZc.js","assets/render-function-CAXdZVZM.js","assets/index-Cf9H8fsj.css","assets/index-HLwNT5T9.js","assets/index-CuYx5vtf.js","assets/index-BZi9bKOJ.css","assets/apiUpload-DpATemHF.js","assets/_plugin-vue_export-helper-BCo6x5W8.js","assets/index-IkV6U84s.css","assets/useCommon-BuUbRw8e.js","assets/apiCommon-DcubqwY_.js","assets/list-CZDs9CTf.js","assets/index-DdMaxvYa.js","assets/index-DfEXMvnc.js","assets/use-children-components-v8i8lsOx.js","assets/index-B5FzkxT_.css","assets/index-DmW4RN1x.js","assets/index-Bl_vBcmJ.css","assets/useLoading-D5mh7tTu.js","assets/usePagination-Dd_EW2BO.js","assets/index-O7pr3qsq.js","assets/index-BlQqQ5bI.css","assets/index-CJ6Fn8S6.css","assets/index-DiBSSeoD.js","assets/index-B4zgCFsq.css","assets/index-DkZuZOQi.js","assets/index-BcWAySBs.css","assets/detail-DhpFZqm3.js","assets/index-DIKBiUsz.js","assets/use-index-D_ozg7PK.js","assets/index-Cf1pvoHl.css","assets/useAddress-CutR4aE-.js","assets/hooks-BLzyyGa3.js","assets/apiStore-vHDpafS2.js","assets/detail-mVd9Ns1A.css","assets/index-C7OqXj1S.js","assets/index-CUtvFEc_.js","assets/index-CHOaln3D.js","assets/index-komh9C6_.css","assets/hooks-Xhdb4ByR.js","assets/detail-B36dfyfG.css"])))=>i.map(i=>d[i]);
import{n as e,$ as l,m as a,N as t}from"./index-D-8JbLQk.js";import"./index-DOhy6BH_.js";import{I as o}from"./index-DDFSMqsG.js";import{S as s,T as i,L as n}from"./index-Cuq5XRs0.js";import"./index-DD6vSYIM.js";import"./index-DQjhgQFu.js";import{T as d,a as r,P as c}from"./index-DdMaxvYa.js";import{B as u,S as p}from"./index-DGtjsHgS.js";import{C as m}from"./index-CdWxsKz_.js";import{D as f}from"./index-DmW4RN1x.js";import{A as h}from"./index-CUtvFEc_.js";import{C as _,R as g}from"./index-BEo1tUsK.js";import{F as y,a as v}from"./index-DVDXfQhn.js";import{R as C}from"./index-dpn1_5z1.js";/* empty css              *//* empty css              */import{d as b,K as w,r as x,o as k,f as j,j as S,k as V,A as $,z as P,u as z,e as N,q as I,I as U,y as R,p as T,J as O,F as A,af as B,M as E,N as q,h as L}from"./vue-D-10XvVk.js";import{u as D}from"./useCommon-BuUbRw8e.js";import{u as F}from"./useAddress-CutR4aE-.js";import{u as M}from"./hooks-BLzyyGa3.js";import{M as J}from"./index-O7pr3qsq.js";import{I as K}from"./index-DfEXMvnc.js";import"./pick-Ccd8Sfcm.js";import"./ResizeObserver.es-CzGuHLZU.js";import"./resize-observer-Dtogi-DJ.js";import"./use-children-components-v8i8lsOx.js";import"./use-index-D_ozg7PK.js";import"./index-CHOaln3D.js";import"./dayjs.min-Daes5FZc.js";import"./render-function-CAXdZVZM.js";import"./apiCommon-DcubqwY_.js";import"./useLoading-D5mh7tTu.js";import"./usePagination-Dd_EW2BO.js";import"./apiStore-vHDpafS2.js";const G={class:"pt-[18px] h-full"},H={class:"h-full flex flex-col gap-[18px]"},Q=["src"],W={class:"text-left"},X={class:"pt-1 w-[210px] truncate"},Y={class:"pt-1"},Z={class:"pt-1"},ee=["src"],le={class:"text-left"},ae={class:"pt-1 w-[280px] truncate"},te={class:"pt-1"},oe={key:1},se=b({__name:"list",props:{delState:{}},setup(b){const se=b,ie=E((()=>a((()=>import("./detail-BEEYEW2J.js").then((e=>e.a))),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66])))),ne=E((()=>a((()=>import("./detail-DhpFZqm3.js").then((e=>e.d))),__vite__mapDeps([53,1,2,3,4,5,16,12,7,10,11,13,14,17,18,19,20,21,22,23,24,54,6,8,55,56,29,30,27,31,32,33,34,46,35,36,57,58,44,45,59,26,60])))),{storeBusinessStateOptions:de,storeCategoryOptions:re,initStoreCategoryOptions:ce,storeSubCategoryOptions:ue,initStoreSubCategoryOptions:pe}=D(),{provinces:me,cities:fe,setCities:he,areas:_e,setAreas:ge}=F(),{loading:ye,queryParams:ve,pagination:Ce,rows:be,selectedId:we,selectedIds:xe,selectAll:ke,rowSelect:je,rowClick:Se,query:Ve,reset:$e,pageChange:Pe,pageSizeChange:ze,add:Ne,edit:Ie,del:Ue,exports:Re}=M({approveState:-1===se.delState?1:void 0,delState:se.delState}),Te=w("addRef"),Oe=x(!1),Ae=async e=>{var l,a;try{if(await(null==(a=null==(l=Te.value)?void 0:l.formRef)?void 0:a.validate()))throw new Error("校验失败");await Ne(q(Te.value.form)),t.success({title:"成功提示",content:"已添加店铺",duration:1500}),e(!0),Ve()}catch(o){e(!1)}},Be=w("editRef"),Ee=x(!1),qe=async e=>{var l,a;try{if(await(null==(a=null==(l=Be.value)?void 0:l.formRef)?void 0:a.validate()))throw new Error("校验失败");await Ie(we.value,q(Be.value.form)),t.success({title:"成功提示",content:"已修改店铺",duration:1500}),e(!0),Ve()}catch(o){e(!1)}},Le=x(!1),De=()=>{try{J.warning({title:"提示",content:()=>L("div",{class:"text-center"},"确定导出所有店铺信息？"),maskClosable:!1,escToClose:!1,hideCancel:!1,cancelButtonProps:{type:"outline"},onBeforeOk:async e=>{try{await Re(),t.success({title:"操作提示",content:"已导出所有店铺信息",duration:1500}),e(!0)}catch(l){e(!1)}}})}catch(e){}};return k((()=>{ce(),Pe(1)})),(a,b)=>{const w=J,x=o,k=y,E=_,q=s,D=K,F=g,M=C,se=j("icon-search"),ce=u,xe=j("icon-refresh"),ke=p,je=v,Se=m,Ne=j("icon-plus"),Ie=j("icon-export"),Re=d,Fe=h,Me=i,Je=f,Ke=n,Ge=r,He=c;return S(),V("div",G,[$(w,{visible:z(Oe),"onUpdate:visible":b[0]||(b[0]=e=>N(Oe)?Oe.value=e:null),width:1e3,"title-align":"start",title:"店铺入驻","cancel-button-props":{type:"outline"},"unmount-on-close":"","mask-closable":!1,"esc-to-close":!1,"on-before-ok":Ae,onCancel:b[1]||(b[1]=e=>Oe.value=!1),"body-style":"background-color: var(--color-fill-2)"},{default:P((()=>[$(z(ie),{ref_key:"addRef",ref:Te},null,512)])),_:1},8,["visible"]),$(w,{visible:z(Ee),"onUpdate:visible":b[2]||(b[2]=e=>N(Ee)?Ee.value=e:null),width:1e3,"title-align":"start",title:"修改店铺","cancel-button-props":{type:"outline"},"unmount-on-close":"","mask-closable":!1,"esc-to-close":!1,"on-before-ok":qe,onCancel:b[3]||(b[3]=e=>Ee.value=!1),"body-style":"background-color: var(--color-fill-2)"},{default:P((()=>[$(z(ie),{ref_key:"editRef",ref:Be,id:z(we)},null,8,["id"])])),_:1},8,["visible"]),$(w,{visible:z(Le),"onUpdate:visible":b[4]||(b[4]=e=>N(Le)?Le.value=e:null),width:1e3,"title-align":"start",title:"店铺详情","cancel-button-props":{type:"outline"},"unmount-on-close":"","mask-closable":!1,"esc-to-close":!1,onCancel:b[5]||(b[5]=e=>Le.value=!1),footer:!1,"body-style":"background-color: var(--color-fill-2)"},{default:P((()=>[$(z(ne),{id:z(we)},null,8,["id"])])),_:1},8,["visible"]),I("div",H,[$(Se,{bordered:!1},{default:P((()=>[$(je,{model:z(ve),"auto-label-width":""},{default:P((()=>[$(F,{gutter:16},{default:P((()=>[$(E,{span:6},{default:P((()=>[$(k,{"show-colon":"",label:"店铺名称",field:"name"},{default:P((()=>[$(x,{modelValue:z(ve).name,"onUpdate:modelValue":b[6]||(b[6]=e=>z(ve).name=e),placeholder:`${a.$inputPlaceholder}店铺名称`,"allow-clear":""},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),$(E,{span:6},{default:P((()=>[$(k,{"show-colon":"",label:"店铺状态",field:"businessState"},{default:P((()=>[$(q,{modelValue:z(ve).businessState,"onUpdate:modelValue":b[7]||(b[7]=e=>z(ve).businessState=e),options:z(de),placeholder:`${a.$selectPlaceholder}店铺状态`,"allow-clear":""},null,8,["modelValue","options","placeholder"])])),_:1})])),_:1}),$(E,{span:6},{default:P((()=>[$(k,{"show-colon":"",label:"联系人姓名",field:"contactName"},{default:P((()=>[$(x,{modelValue:z(ve).contactName,"onUpdate:modelValue":b[8]||(b[8]=e=>z(ve).contactName=e),placeholder:`${a.$inputPlaceholder}联系人姓名`,"allow-clear":""},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),$(E,{span:6},{default:P((()=>[$(k,{"show-colon":"",label:"联系电话",field:"contactNumber"},{default:P((()=>[$(D,{"hide-button":"",modelValue:z(ve).contactNumber,"onUpdate:modelValue":b[9]||(b[9]=e=>z(ve).contactNumber=e),placeholder:`${a.$inputPlaceholder}联系电话`,"allow-clear":""},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),$(E,{span:6},{default:P((()=>[$(k,{"show-colon":"",label:"店铺分类"},{default:P((()=>[$(F,{align:"center",class:"w-full"},{default:P((()=>[$(E,{span:11},{default:P((()=>[$(k,{"no-style":"",field:"storeCategoryId"},{default:P((()=>[$(q,{modelValue:z(ve).storeCategoryId,"onUpdate:modelValue":b[10]||(b[10]=e=>z(ve).storeCategoryId=e),options:z(re),placeholder:`${a.$selectPlaceholder}一级分类`,onChange:b[11]||(b[11]=e=>{z(ve).storeSubCategoryId=void 0,ue.value=[],z(pe)(e)}),"allow-clear":""},null,8,["modelValue","options","placeholder"])])),_:1})])),_:1}),$(E,{span:2,class:"text-center"},{default:P((()=>b[21]||(b[21]=[U("-")]))),_:1}),$(E,{span:11},{default:P((()=>[$(k,{"no-style":"",field:"storeSubCategoryId"},{default:P((()=>[$(q,{modelValue:z(ve).storeSubCategoryId,"onUpdate:modelValue":b[12]||(b[12]=e=>z(ve).storeSubCategoryId=e),options:z(ue),placeholder:`${a.$selectPlaceholder}二级分类`,"allow-clear":""},null,8,["modelValue","options","placeholder"])])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),$(E,{span:6},{default:P((()=>[$(k,{"show-colon":"",label:"店铺地址"},{default:P((()=>[$(F,{align:"center",class:"w-full"},{default:P((()=>[$(E,{span:7},{default:P((()=>[$(k,{"no-style":"",field:"provinceCode"},{default:P((()=>[$(q,{modelValue:z(ve).provinceCode,"onUpdate:modelValue":b[13]||(b[13]=e=>z(ve).provinceCode=e),options:z(me),placeholder:`${a.$selectPlaceholder}省`,onChange:b[14]||(b[14]=e=>{z(ve).cityCode=void 0,z(ve).areaCode=void 0,fe.value=[],_e.value=[],z(he)(e)}),"allow-clear":""},null,8,["modelValue","options","placeholder"])])),_:1})])),_:1}),$(E,{span:1,class:"text-center"},{default:P((()=>b[22]||(b[22]=[U("-")]))),_:1}),$(E,{span:7},{default:P((()=>[$(k,{"no-style":"",field:"cityCode"},{default:P((()=>[$(q,{modelValue:z(ve).cityCode,"onUpdate:modelValue":b[15]||(b[15]=e=>z(ve).cityCode=e),options:z(fe),placeholder:`${a.$selectPlaceholder}市`,onChange:b[16]||(b[16]=e=>{z(ve).areaCode=void 0,_e.value=[],z(ge)(z(ve).provinceCode,e)}),"allow-clear":""},null,8,["modelValue","options","placeholder"])])),_:1})])),_:1}),$(E,{span:1,class:"text-center"},{default:P((()=>b[23]||(b[23]=[U("-")]))),_:1}),$(E,{span:8},{default:P((()=>[$(k,{"no-style":"",field:"areaCode"},{default:P((()=>[$(q,{modelValue:z(ve).areaCode,"onUpdate:modelValue":b[17]||(b[17]=e=>z(ve).areaCode=e),options:z(_e),placeholder:`${a.$selectPlaceholder}区`,"allow-clear":""},null,8,["modelValue","options","placeholder"])])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),$(E,{span:6},{default:P((()=>[$(k,{"show-colon":"",label:"入驻时间",field:"createTime"},{default:P((()=>[$(M,{modelValue:z(ve).createTime,"onUpdate:modelValue":b[18]||(b[18]=e=>z(ve).createTime=e),placeholder:[`${a.$selectPlaceholder}开始日期`,`${a.$selectPlaceholder}结束日期`]},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),$(E,{span:6},{default:P((()=>[$(k,{"hide-label":""},{default:P((()=>[$(ke,{size:18},{default:P((()=>[$(ce,{type:"primary",onClick:b[19]||(b[19]=e=>z(Pe)(1))},{icon:P((()=>[$(se)])),default:P((()=>[b[24]||(b[24]=U(" 查询 "))])),_:1}),$(ce,{type:"outline",onClick:z($e)},{icon:P((()=>[$(xe)])),default:P((()=>[b[25]||(b[25]=U(" 重置 "))])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),$(Se,{bordered:!1,class:"flex-1 overflow-y-hidden","body-style":{height:"100%"}},{actions:P((()=>[z(Ce).total?(S(),R(He,{key:0,current:z(Ce).current,"page-size":z(Ce).pageSize,"show-total":z(Ce).showTotal,"show-page-size":z(Ce).showPageSize,"page-size-options":z(Ce).pageSizeOptions,total:z(Ce).total,onChange:z(Pe),onPageSizeChange:z(ze)},null,8,["current","page-size","show-total","show-page-size","page-size-options","total","onChange","onPageSizeChange"])):T("",!0)])),default:P((()=>[$(F,{class:"mb-[12px]"},{default:P((()=>[$(E,{span:16},{default:P((()=>[$(ke,null,{default:P((()=>[$(ce,{type:"primary",onClick:b[20]||(b[20]=()=>{we.value=void 0,Oe.value=!0})},{icon:P((()=>[$(Ne)])),default:P((()=>[b[26]||(b[26]=U(" 店铺入驻 "))])),_:1}),$(ce,{disabled:!z(be).length||1===a.delState,type:"primary",onClick:De},{icon:P((()=>[$(Ie)])),default:P((()=>[b[27]||(b[27]=U(" 导出 "))])),_:1},8,["disabled"])])),_:1})])),_:1})])),_:1}),$(Ge,{size:"large","row-key":"id",loading:z(ye),pagination:!1,data:z(be),bordered:{cell:!0},scroll:{y:"calc(100% - 96px)"}},{columns:P((()=>[$(Re,{align:"center",title:"序号",width:80},{cell:P((({rowIndex:e})=>[U(O(z(Ce).pageSize*(z(Ce).current-1)+e+1),1)])),_:1}),$(Re,{align:"center",title:"店铺信息",width:300,ellipsis:"",tooltip:""},{cell:P((({record:l})=>[$(ke,null,{default:P((()=>[$(Fe,{size:32},{default:P((()=>[I("img",{src:l.logo},null,8,Q)])),_:2},1024),I("div",W,[I("p",null,"店铺编号："+O(l.storeNo),1),I("p",X,"店铺名称："+O(l.name),1),I("p",Y,"联系人："+O(l.contactName),1),I("p",Z,"联系电话："+O(z(e)(l.contactNumber)),1)])])),_:2},1024)])),_:1}),$(Re,{align:"center",title:"店铺状态",width:150,ellipsis:"",tooltip:""},{cell:P((({record:e})=>{var l;return[1===e.delState?(S(),R(Me,{key:0,color:"red"},{default:P((()=>b[28]||(b[28]=[U("已注销")]))),_:1})):e.businessState?(S(),R(Me,{key:1,color:(null==(l=z(de).find((l=>l.value===e.businessState)))?void 0:l.color)??""},{default:P((()=>{var l;return[U(O((null==(l=z(de).find((l=>l.value===e.businessState)))?void 0:l.label)??""),1)]})),_:2},1032,["color"])):T("",!0)]})),_:1}),-1===a.delState?(S(),V(A,{key:0},[$(Re,{align:"center",title:"企业信息",width:400,ellipsis:"",tooltip:""},{cell:P((({record:e})=>[e.unifiedSocialCreditCode?(S(),R(ke,{key:0},{default:P((()=>[$(Fe,{shape:"square",size:32},{default:P((()=>[I("img",{src:e.license},null,8,ee)])),_:2},1024),I("div",le,[I("p",null,"统一社会信用代码："+O(e.unifiedSocialCreditCode),1),I("p",ae,"促进会职务："+O(e.enterpriseName),1),I("p",te,"企业法人："+O(e.legalPerson),1)])])),_:2},1024)):(S(),V("span",oe,"-"))])),_:1}),$(Re,{align:"center",title:"店铺分类",width:300,ellipsis:"",tooltip:""},{cell:P((({record:e})=>[U(O(e.storeCategoryName)+" - "+O(e.storeSubCategoryName),1)])),_:1}),$(Re,{align:"center",title:"店铺地址",width:300,ellipsis:"",tooltip:""},{cell:P((({record:e})=>[U(O(e.provinceName)+" - "+O(e.cityName)+" - "+O(e.areaName)+" - "+O(e.detailAddress),1)])),_:1}),$(Re,{align:"center",title:"平台佣金比例",width:180},{cell:P((({record:e})=>[U(O(z(l)(e.platformCommissionRate,100))+" %",1)])),_:1}),$(Re,{align:"center",title:"在售商品数量",width:150,ellipsis:"",tooltip:""},{cell:P((({record:e})=>[U(O(e.saleCommodityCount)+" 个",1)])),_:1}),$(Re,{align:"center",title:"入驻时间",width:180,"data-index":"createTime"}),-1===a.delState?(S(),R(Re,{key:0,align:"center",title:"操作",width:300,fixed:"right"},{cell:P((({record:e})=>[$(ke,null,{split:P((()=>[$(Je,{direction:"vertical"})])),default:P((()=>[$(Ke,{onClick:B((()=>{we.value=e.id,Le.value=!0}),["stop"])},{default:P((()=>b[29]||(b[29]=[U(" 详情 ")]))),_:2},1032,["onClick"]),$(Ke,{onClick:B((()=>{we.value=e.id,Ee.value=!0}),["stop"])},{default:P((()=>b[30]||(b[30]=[U(" 编辑 ")]))),_:2},1032,["onClick"]),$(Ke,{status:"danger",onClick:B((l=>(e=>{try{J.warning({title:"提示",content:()=>L("div",{class:"text-center"},`确定注销【${e.name}】？`),maskClosable:!1,escToClose:!1,hideCancel:!1,cancelButtonProps:{type:"outline"},onBeforeOk:async l=>{try{await Ue(e.id),t.success({title:"成功提示",content:"已注销店铺",duration:1500}),l(!0),Ve()}catch(a){l(!1)}}})}catch(l){}})(e)),["stop"])},{default:P((()=>b[31]||(b[31]=[U("注销")]))),_:2},1032,["onClick"])])),_:2},1024)])),_:1})):T("",!0)],64)):(S(),R(Re,{key:1,align:"center",title:"注销时间",width:180,"data-index":"delTime"}))])),_:1},8,["loading","data"])])),_:1})])])}}});export{se as default};

import{_ as e}from"./index-HLwNT5T9.js";import"./index-DOhy6BH_.js";import{S as r}from"./index-DGtjsHgS.js";import{A as l}from"./index-CUtvFEc_.js";import{C as o}from"./index-CdWxsKz_.js";import{D as t,a}from"./index-DIKBiUsz.js";import{T as d}from"./index-Cuq5XRs0.js";import{n as s}from"./index-D-8JbLQk.js";import{u as i}from"./useCommon-BuUbRw8e.js";import{u as n}from"./useAddress-CutR4aE-.js";import{u}from"./index-0le0xlGU.js";import{d as p,ae as v,o as m,K as f,j as c,k as b,q as j,A as x,z as _,u as y,I as g,J as T,F as Y,L as N}from"./vue-D-10XvVk.js";import{d as S}from"./dayjs.min-Daes5FZc.js";import"./index-DD6vSYIM.js";import"./index-DDFSMqsG.js";import"./pick-Ccd8Sfcm.js";import"./ResizeObserver.es-CzGuHLZU.js";import"./index-CuYx5vtf.js";import"./render-function-CAXdZVZM.js";import"./apiUpload-DpATemHF.js";import"./_plugin-vue_export-helper-BCo6x5W8.js";import"./use-index-D_ozg7PK.js";import"./index-CHOaln3D.js";import"./index-BEo1tUsK.js";import"./resize-observer-Dtogi-DJ.js";import"./index-DQjhgQFu.js";import"./apiCommon-DcubqwY_.js";import"./index-DdMaxvYa.js";import"./index-DfEXMvnc.js";import"./use-children-components-v8i8lsOx.js";import"./index-DmW4RN1x.js";import"./index-DVDXfQhn.js";import"./index-dpn1_5z1.js";/* empty css              */import"./useLoading-D5mh7tTu.js";import"./usePagination-Dd_EW2BO.js";import"./index-O7pr3qsq.js";const z={class:"h-[700px] overflow-y-scroll no-scrollbar"},A={class:"h-full flex flex-col gap-[18px]"},D=["src"],C={class:"text-left"},O={class:"pt-1"},h=["src"],H={class:"text-left"},M={class:"pt-1"},w={class:"pt-1"},k={class:"text-left"},F={class:"pt-1"},L={class:"pt-1"},R={class:"pt-1"},q={class:"pt-1"},P={class:"pt-1 w-[210px]"},V={class:"text-left"},B={class:"pt-1"},G={class:"pt-1"},I={class:"pt-1 w-[200px]"},J=p({__name:"detail",props:{id:{}},setup(p,{expose:J}){const{orderStateOptions:K,orderDeliveryTypeOptions:U,orderPayTypeOptions:Z}=i();v();const $=p,{storeBusinessStateOptions:E}=i(),{provinces:Q,cities:W,setCities:X,areas:ee,setAreas:re}=n(),{form:le,detail:oe}=u();m((async()=>{await oe($.id),X(le.provinceCode),re(le.provinceCode,le.cityCode)}));return J({formRef:f("formRef"),form:le}),(i,n)=>{const u=d,p=t,v=a,m=o,f=l,J=r,K=e;return c(),b("div",z,[j("div",A,[x(m,{bordered:!1,title:"基本信息"},{default:_((()=>[x(v,{bordered:"",size:"large",column:2},{default:_((()=>[x(p,{label:"类型"},{default:_((()=>[x(u,{color:1===y(le).type?"green":"orange"},{default:_((()=>[g(T(1===y(le).type?"订单收入":"向平台分账"),1)])),_:1},8,["color"])])),_:1}),x(p,{label:"金额"},{default:_((()=>[g(T(y(le).amount)+" 元",1)])),_:1}),x(p,{label:"状态"},{default:_((()=>[g(T(1===y(le).state?"处理完成":"处理中"),1)])),_:1}),x(p,{label:"创建时间"},{default:_((()=>[g(T(y(S)(y(le).createTime).format("YYYY-MM-DD HH:mm:ss")),1)])),_:1})])),_:1})])),_:1}),x(m,{bordered:!1,title:"订单信息"},{default:_((()=>[x(v,{bordered:"",size:"large",column:2},{default:_((()=>[x(p,{label:"订单编号"},{default:_((()=>{var e;return[g(T((null==(e=y(le).order)?void 0:e.orderNo)||"-"),1)]})),_:1}),x(p,{label:"下单时间"},{default:_((()=>{var e;return[g(T(y(S)(null==(e=y(le).order)?void 0:e.createTime).format("YYYY-MM-DD HH:mm:ss")||"-"),1)]})),_:1}),x(p,{label:"商品名称"},{default:_((()=>{var e;return[(c(!0),b(Y,null,N(null==(e=y(le).order)?void 0:e.commodities,((e,r)=>(c(),b("div",{key:r},[x(J,null,{default:_((()=>[x(f,{shape:"square",size:32},{default:_((()=>[j("img",{src:e.cover},null,8,D)])),_:2},1024),j("div",C,[j("p",null,"商品名称："+T(e.name),1),j("p",O,"商品数量："+T(e.count)+" "+T(e.unit),1)])])),_:2},1024)])))),128))]})),_:1}),x(p,{label:"用户信息"},{default:_((()=>[x(J,null,{default:_((()=>{var e,r,l;return[x(f,{size:32},{default:_((()=>{var e;return[j("img",{src:null==(e=y(le).order)?void 0:e.user.avatar},null,8,h)]})),_:1}),j("div",H,[j("p",null,"用户编号："+T(null==(e=y(le).order)?void 0:e.user.no),1),j("p",M,"用户昵称："+T(null==(r=y(le).order)?void 0:r.user.nickname),1),j("p",w,"用户手机："+T(y(s)(null==(l=y(le).order)?void 0:l.user.mobile)),1)])]})),_:1})])),_:1}),x(p,{label:"支付信息"},{default:_((()=>[x(J,null,{default:_((()=>{var e,r,l,o,t,a,d,s;return[j("div",k,[j("p",null,"商品金额："+T(null==(e=y(le).order)?void 0:e.commodityAmount.toFixed(2))+" 元",1),j("p",F,"优惠金额："+T(null==(r=y(le).order)?void 0:r.reduceAmount.toFixed(2))+" 元",1),j("p",L,"付款金额："+T(null==(l=y(le).order)?void 0:l.payAmount.toFixed(2))+" 元",1),j("p",R,"付款方式："+T((null==(o=y(Z).find((e=>{var r;return e.value===(null==(r=y(le).order)?void 0:r.payType)})))?void 0:o.label)??""),1),j("p",q,"支付状态："+T(0===(null==(t=y(le).order)?void 0:t.payState)?"待付款":-1===(null==(a=y(le).order)?void 0:a.payState)?"支付失败":1===(null==(d=y(le).order)?void 0:d.payState)?"支付成功":""),1),j("p",P,"付款时间："+T(y(S)(null==(s=y(le).order)?void 0:s.payTime).format("YYYY-MM-DD HH:mm:ss")),1)])]})),_:1})])),_:1}),x(p,{label:"收货信息"},{default:_((()=>[x(J,null,{default:_((()=>{var e,r,l,o,t,a,d,s,i,p;return[j("div",V,[j("p",null,[n[0]||(n[0]=g(" 配送方式： ")),x(u,{color:(null==(e=y(U).find((e=>{var r;return e.value===(null==(r=y(le).order)?void 0:r.deliveryType)})))?void 0:e.color)??""},{default:_((()=>{var e;return[g(T((null==(e=y(U).find((e=>{var r;return e.value===(null==(r=y(le).order)?void 0:r.deliveryType)})))?void 0:e.label)??""),1)]})),_:1},8,["color"])]),j("p",B,"收货人："+T(1===(null==(r=y(le).order)?void 0:r.deliveryType)?null==(o=null==(l=y(le).order)?void 0:l.delivery)?void 0:o.userName:"-"),1),j("p",G,"手机号码："+T(1===(null==(t=y(le).order)?void 0:t.deliveryType)?null==(d=null==(a=y(le).order)?void 0:a.delivery)?void 0:d.contactNumber:"-"),1),j("p",I,"收货地址："+T(1===(null==(s=y(le).order)?void 0:s.deliveryType)?null==(p=null==(i=y(le).order)?void 0:i.delivery)?void 0:p.address:"-"),1)])]})),_:1})])),_:1})])),_:1})])),_:1}),x(m,{bordered:!1,title:"店铺信息"},{default:_((()=>[x(v,{bordered:"",size:"large",column:2},{default:_((()=>[x(p,{label:"店铺LOGO",span:2},{default:_((()=>{var e;return[x(K,{modelValue:null==(e=y(le).store)?void 0:e.logo,disabled:""},null,8,["modelValue"])]})),_:1}),x(p,{label:"店铺编号"},{default:_((()=>{var e;return[g(T(null==(e=y(le).store)?void 0:e.storeNo),1)]})),_:1}),x(p,{label:"店铺名称"},{default:_((()=>{var e;return[g(T(null==(e=y(le).store)?void 0:e.name),1)]})),_:1}),x(p,{label:"联系人姓名"},{default:_((()=>{var e;return[g(T(null==(e=y(le).store)?void 0:e.contactName),1)]})),_:1}),x(p,{label:"联系电话"},{default:_((()=>{var e,r;return[g(T(y(s)(null==(r=null==(e=y(le).store)?void 0:e.contactNumber)?void 0:r.toString())),1)]})),_:1}),x(p,{label:"联系电话2"},{default:_((()=>{var e,r;return[g(T(y(s)((null==(r=null==(e=y(le).store)?void 0:e.contactNumber2)?void 0:r.toString())??"-")),1)]})),_:1}),x(p,{label:"店铺状态"},{default:_((()=>{var e;return[x(u,{color:(null==(e=y(E).find((e=>{var r;return e.value===(null==(r=y(le).store)?void 0:r.businessState)})))?void 0:e.color)??""},{default:_((()=>{var e;return[g(T((null==(e=y(E).find((e=>{var r;return e.value===(null==(r=y(le).store)?void 0:r.businessState)})))?void 0:e.label)??""),1)]})),_:1},8,["color"])]})),_:1}),x(p,{label:"店铺地址",span:2},{default:_((()=>{var e,r,l,o;return[g(T(null==(e=y(le).store)?void 0:e.provinceName)+" - "+T(null==(r=y(le).store)?void 0:r.cityName)+" - "+T(null==(l=y(le).store)?void 0:l.areaName)+" - "+T(null==(o=y(le).store)?void 0:o.detailAddress),1)]})),_:1})])),_:1})])),_:1})])])}}});export{J as default};

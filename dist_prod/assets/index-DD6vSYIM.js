import{g as e,j as l,af as a,s as t,f as o,_ as i,v as n,w as r,h as s}from"./index-D-8JbLQk.js";import{a as u,R as c}from"./index-DDFSMqsG.js";import{d,i as v,A as h,v as f,r as p,c as m,j as b,y as g,z as S,q as y,l as z,af as R,O as $,a6 as T,o as w,f as C,k as x,m as k,p as B,w as M}from"./vue-D-10XvVk.js";var O=d({name:"Empty",inheritAttrs:!1,props:{description:String,imgSrc:String,inConfigProvider:{type:Boolean,default:!1}},setup(t,{slots:o,attrs:i}){const n=e("empty"),{t:r}=u(),s=v(l,void 0);return()=>{var e,l,u,c;return t.inConfigProvider||!(null==s?void 0:s.slots.empty)||o.image||t.imgSrc||t.description?h("div",f({class:n},i),[h("div",{class:`${n}-image`},[null!=(l=null==(e=o.image)?void 0:e.call(o))?l:t.imgSrc?h("img",{src:t.imgSrc,alt:t.description||"empty"},null):h(a,null,null)]),h("div",{class:`${n}-description`},[null!=(c=null==(u=o.default)?void 0:u.call(o))?c:t.description||r("empty.description")])]):s.slots.empty({component:"empty"})}}});const V=Object.assign(O,{install:(e,l)=>{t(e,l);const a=o(l);e.component(a+O.name,O)}}),H=d({name:"Thumb",props:{data:{type:Object},direction:{type:String,default:"horizontal"},alwaysShow:{type:Boolean,default:!1},both:{type:Boolean,default:!1}},emits:["scroll"],setup(l,{emit:a}){const t=e("scrollbar"),o=p(!1),i=p(),s=p(),u=m((()=>"horizontal"===l.direction?{size:"width",direction:"left",offset:"offsetWidth",client:"clientX"}:{size:"height",direction:"top",offset:"offsetHeight",client:"clientY"})),c=p(0),d=p(!1),v=p(0),h=m((()=>{var e,a;return{[u.value.size]:`${null!=(a=null==(e=l.data)?void 0:e.thumbSize)?a:0}px`,[u.value.direction]:`${c.value}px`}})),f=e=>e<0?0:l.data&&e>l.data.max?l.data.max:e,b=e=>{if(i.value&&s.value){const l=f(e[u.value.client]-i.value.getBoundingClientRect()[u.value.direction]-v.value);l!==c.value&&(c.value=l,a("scroll",l))}},g=()=>{d.value=!1,r(window,"mousemove",b),r(window,"mouseup",g)},S=m((()=>[`${t}-thumb`,`${t}-thumb-direction-${l.direction}`,{[`${t}-thumb-dragging`]:d.value}]));return{visible:o,trackRef:i,thumbRef:s,prefixCls:t,thumbCls:S,thumbStyle:h,handleThumbMouseDown:e=>{e.preventDefault(),s.value&&(v.value=e[u.value.client]-s.value.getBoundingClientRect()[u.value.direction],d.value=!0,n(window,"mousemove",b),n(window,"mouseup",g),n(window,"contextmenu",g))},handleTrackClick:e=>{var t,o,i,n;if(e.preventDefault(),s.value){const r=f(e[u.value.client]>s.value.getBoundingClientRect()[u.value.direction]?c.value+(null!=(o=null==(t=l.data)?void 0:t.thumbSize)?o:0):c.value-(null!=(n=null==(i=l.data)?void 0:i.thumbSize)?n:0));r!==c.value&&(c.value=r,a("scroll",r))}},setOffset:e=>{d.value||(e=f(e))!==c.value&&(c.value=e)}}}});var j=i(d({name:"Scrollbar",components:{ResizeObserver:c,Thumb:i(H,[["render",function(e,l,a,t,o,i){return b(),g(T,null,{default:S((()=>[y("div",{ref:"trackRef",class:z([`${e.prefixCls}-track`,`${e.prefixCls}-track-direction-${e.direction}`]),onMousedown:l[1]||(l[1]=R(((...l)=>e.handleTrackClick&&e.handleTrackClick(...l)),["self"]))},[y("div",{ref:"thumbRef",class:z(e.thumbCls),style:$(e.thumbStyle),onMousedown:l[0]||(l[0]=(...l)=>e.handleThumbMouseDown&&e.handleThumbMouseDown(...l))},[y("div",{class:z(`${e.prefixCls}-thumb-bar`)},null,2)],38)],34)])),_:1})}]])},inheritAttrs:!1,props:{type:{type:String,default:"embed"},outerClass:[String,Object,Array],outerStyle:{type:[String,Object,Array]},hide:{type:Boolean,default:!1},disableHorizontal:{type:Boolean,default:!1},disableVertical:{type:Boolean,default:!1}},emits:{scroll:e=>!0},setup(l,{emit:a}){const t=e("scrollbar"),o=p(),i=p(),n=p(),r=p(),s=p(),u=p(!1),c=p(!1),d=m((()=>u.value&&!l.disableHorizontal)),v=m((()=>c.value&&!l.disableVertical)),h=p(!1),f=()=>{var e,a,t,f,p,m;if(o.value){const{clientWidth:b,clientHeight:g,offsetWidth:S,offsetHeight:y,scrollWidth:z,scrollHeight:R,scrollTop:$,scrollLeft:T}=o.value;u.value=z>b,c.value=R>g,h.value=d.value&&v.value;const w="embed"===l.type&&h.value?S-15:S,C="embed"===l.type&&h.value?y-15:y,x=Math.round(w/Math.min(z/b,w/20)),k=w-x,B=(z-b)/k,M=Math.round(C/Math.min(R/g,C/20)),O=C-M,V=(R-g)/O;if(i.value={ratio:B,thumbSize:x,max:k},n.value={ratio:V,thumbSize:M,max:O},$>0){const l=Math.round($/(null!=(a=null==(e=n.value)?void 0:e.ratio)?a:1));null==(t=s.value)||t.setOffset(l)}if(T>0){const e=Math.round(T/(null!=(p=null==(f=n.value)?void 0:f.ratio)?p:1));null==(m=r.value)||m.setOffset(e)}}};w((()=>{f()}));const b=m((()=>{const e={};return"track"===l.type&&(d.value&&(e.paddingBottom="15px"),v.value&&(e.paddingRight="15px")),[e,l.outerStyle]})),g=m((()=>[`${t}`,`${t}-type-${l.type}`,{[`${t}-both`]:h.value},l.outerClass]));return{prefixCls:t,cls:g,style:b,containerRef:o,horizontalThumbRef:r,verticalThumbRef:s,horizontalData:i,verticalData:n,isBoth:h,hasHorizontalScrollbar:d,hasVerticalScrollbar:v,handleResize:()=>{f()},handleScroll:e=>{var t,u,c,h,f,p;if(o.value){if(d.value&&!l.disableHorizontal){const e=Math.round(o.value.scrollLeft/(null!=(u=null==(t=i.value)?void 0:t.ratio)?u:1));null==(c=r.value)||c.setOffset(e)}if(v.value&&!l.disableVertical){const e=Math.round(o.value.scrollTop/(null!=(f=null==(h=n.value)?void 0:h.ratio)?f:1));null==(p=s.value)||p.setOffset(e)}}a("scroll",e)},handleHorizontalScroll:e=>{var l,a;o.value&&o.value.scrollTo({left:e*(null!=(a=null==(l=i.value)?void 0:l.ratio)?a:1)})},handleVerticalScroll:e=>{var l,a;o.value&&o.value.scrollTo({top:e*(null!=(a=null==(l=n.value)?void 0:l.ratio)?a:1)})}}},methods:{scrollTo(e,l){var a,t;s(e)?null==(a=this.$refs.containerRef)||a.scrollTo(e):(e||l)&&(null==(t=this.$refs.containerRef)||t.scrollTo(e,l))},scrollTop(e){var l;null==(l=this.$refs.containerRef)||l.scrollTo({top:e})},scrollLeft(e){var l;null==(l=this.$refs.containerRef)||l.scrollTo({left:e})}}}),[["render",function(e,l,a,t,o,i){const n=C("ResizeObserver"),r=C("thumb");return b(),x("div",{class:z(e.cls),style:$(e.style)},[h(n,{onResize:e.handleResize},{default:S((()=>[y("div",f({ref:"containerRef",class:`${e.prefixCls}-container`},e.$attrs,{onScroll:l[0]||(l[0]=(...l)=>e.handleScroll&&e.handleScroll(...l))}),[h(n,{onResize:e.handleResize},{default:S((()=>[k(e.$slots,"default")])),_:3},8,["onResize"])],16)])),_:3},8,["onResize"]),!e.hide&&e.hasHorizontalScrollbar?(b(),g(r,{key:0,ref:"horizontalThumbRef",data:e.horizontalData,direction:"horizontal",both:e.isBoth,onScroll:e.handleHorizontalScroll},null,8,["data","both","onScroll"])):B("v-if",!0),!e.hide&&e.hasVerticalScrollbar?(b(),g(r,{key:1,ref:"verticalThumbRef",data:e.verticalData,direction:"vertical",both:e.isBoth,onScroll:e.handleVerticalScroll},null,8,["data","both","onScroll"])):B("v-if",!0)],6)}]]);const D=Object.assign(j,{install:(e,l)=>{t(e,l);const a=o(l);e.component(a+j.name,j)}}),A=({popupVisible:e,defaultPopupVisible:l,emit:a})=>{var t;const o=p(null!=(t=null==l?void 0:l.value)&&t),i=m((()=>{var l;return null!=(l=null==e?void 0:e.value)?l:o.value}));return M(i,(e=>{o.value!==e&&(o.value=e)})),{computedPopupVisible:i,handlePopupVisibleChange:e=>{e!==i.value&&(o.value=e,a("update:popupVisible",e),a("popupVisibleChange",e))}}};export{V as E,D as S,A as u};

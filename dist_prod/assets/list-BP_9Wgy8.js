import"./index-DOhy6BH_.js";import{I as e}from"./index-DDFSMqsG.js";import{S as l,T as a}from"./index-Cuq5XRs0.js";import"./index-DD6vSYIM.js";import"./index-DQjhgQFu.js";import{T as t,a as o,P as r}from"./index-DdMaxvYa.js";import{B as d,S as s}from"./index-DGtjsHgS.js";import{C as n}from"./index-CdWxsKz_.js";import{A as i}from"./index-CUtvFEc_.js";import{C as p,R as u}from"./index-BEo1tUsK.js";import{F as c,a as m}from"./index-DVDXfQhn.js";import{R as f}from"./index-dpn1_5z1.js";/* empty css              */import{n as h,N as y}from"./index-D-8JbLQk.js";import{u as _}from"./useCommon-BuUbRw8e.js";import{u as v}from"./hooks-DI-DMeWq.js";import{d as w}from"./dayjs.min-Daes5FZc.js";import{M as g}from"./index-O7pr3qsq.js";import{I as x}from"./index-DfEXMvnc.js";import{d as S,o as b,f as V,j,k,q as T,A as $,z,u as N,I as P,y as C,p as A,J as U,F as Y,L as F,h as I}from"./vue-D-10XvVk.js";import"./pick-Ccd8Sfcm.js";import"./ResizeObserver.es-CzGuHLZU.js";import"./resize-observer-Dtogi-DJ.js";import"./use-children-components-v8i8lsOx.js";import"./use-index-D_ozg7PK.js";import"./index-CHOaln3D.js";import"./render-function-CAXdZVZM.js";import"./apiCommon-DcubqwY_.js";import"./useLoading-D5mh7tTu.js";import"./usePagination-Dd_EW2BO.js";const O={class:"pt-[18px] h-full"},D={class:"h-full flex flex-col gap-[18px]"},M={class:"text-left"},R={class:"pt-1"},q={class:"pt-1"},H=["src"],B={class:"text-left"},L={class:"pt-1"},J=["src"],E={class:"text-left"},G={class:"pt-1"},K={class:"pt-1"},Q=["src"],W={class:"text-left"},X={class:"pt-1 w-[210px] truncate"},Z={class:"pt-1"},ee={class:"pt-1"},le={class:"text-left"},ae={class:"pt-1"},te={class:"pt-1"},oe={class:"pt-1"},re={class:"pt-1"},de={class:"pt-1 w-[210px]"},se={class:"text-left"},ne={class:"pt-1"},ie={class:"pt-1"},pe={class:"pt-1 w-[210px] truncate"},ue={class:"text-left"},ce={class:"w-[300px] truncate"},me={class:"pt-1"},fe={key:0,class:"pt-1"},he={key:1,class:"pt-1"},ye={class:"pt-1"},_e={class:"pt-1"},ve={key:0,class:"pt-1"},we={key:1},ge=S({__name:"list",props:{type:{}},setup(S){const ge=S,{orderStateOptions:xe,orderDeliveryTypeOptions:Se,orderPayTypeOptions:be}=_(),{loading:Ve,queryParams:je,pagination:ke,rows:Te,selectedId:$e,selectedRow:ze,selectedIds:Ne,selectAll:Pe,rowSelect:Ce,rowClick:Ae,query:Ue,reset:Ye,pageChange:Fe,pageSizeChange:Ie,exports:Oe}=v({type:ge.type}),De=()=>{try{g.warning({title:"提示",content:()=>I("div",{class:"text-center"},"确定导出所有订单记录？"),maskClosable:!1,escToClose:!1,hideCancel:!1,cancelButtonProps:{type:"outline"},onBeforeOk:async e=>{try{await Oe(),y.success({title:"操作提示",content:"已导出所有订单记录",duration:1500}),e(!0)}catch(l){e(!1)}}})}catch(e){}};return b((()=>{Fe(1)})),(y,_)=>{const v=e,g=c,S=p,b=l,I=x,ge=u,xe=f,$e=V("icon-search"),ze=d,Ne=V("icon-refresh"),Pe=s,Ce=m,Ae=n,Ue=V("icon-export"),Oe=t,Me=i,Re=a,qe=o,He=r;return j(),k("div",O,[T("div",D,[$(Ae,{bordered:!1},{default:z((()=>[$(Ce,{model:N(je),"auto-label-width":""},{default:z((()=>[$(ge,{gutter:16},{default:z((()=>[$(S,{span:6},{default:z((()=>[$(g,{"show-colon":"",label:"订单编号",field:"orderNo"},{default:z((()=>[$(v,{modelValue:N(je).orderNo,"onUpdate:modelValue":_[0]||(_[0]=e=>N(je).orderNo=e),placeholder:`${y.$inputPlaceholder}订单编号`,"allow-clear":""},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),$(S,{span:6},{default:z((()=>[$(g,{"show-colon":"",label:"配送方式",field:"deliveryType"},{default:z((()=>[$(b,{modelValue:N(je).deliveryType,"onUpdate:modelValue":_[1]||(_[1]=e=>N(je).deliveryType=e),options:N(Se),placeholder:`${y.$selectPlaceholder}配送方式`,"allow-clear":""},null,8,["modelValue","options","placeholder"])])),_:1})])),_:1}),$(S,{span:6},{default:z((()=>[$(g,{"show-colon":"",label:"付款方式",field:"payType"},{default:z((()=>[$(b,{modelValue:N(je).payType,"onUpdate:modelValue":_[2]||(_[2]=e=>N(je).payType=e),options:N(be),placeholder:`${y.$selectPlaceholder}付款方式`,"allow-clear":""},null,8,["modelValue","options","placeholder"])])),_:1})])),_:1}),$(S,{span:6},{default:z((()=>[$(g,{"show-colon":"",label:"商品名称",field:"commodityName"},{default:z((()=>[$(v,{modelValue:N(je).commodityName,"onUpdate:modelValue":_[3]||(_[3]=e=>N(je).commodityName=e),placeholder:`${y.$inputPlaceholder}商品名称`,"allow-clear":""},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),$(S,{span:6},{default:z((()=>[$(g,{"show-colon":"",label:"店铺编号",field:"storeNo"},{default:z((()=>[$(v,{modelValue:N(je).storeNo,"onUpdate:modelValue":_[4]||(_[4]=e=>N(je).storeNo=e),placeholder:`${y.$inputPlaceholder}店铺编号`,"allow-clear":""},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),$(S,{span:6},{default:z((()=>[$(g,{"show-colon":"",label:"店铺名称",field:"storeName"},{default:z((()=>[$(v,{modelValue:N(je).storeName,"onUpdate:modelValue":_[5]||(_[5]=e=>N(je).storeName=e),placeholder:`${y.$inputPlaceholder}店铺名称`,"allow-clear":""},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),$(S,{span:6},{default:z((()=>[$(g,{"show-colon":"",label:"用户编号",field:"userNo"},{default:z((()=>[$(v,{modelValue:N(je).userNo,"onUpdate:modelValue":_[6]||(_[6]=e=>N(je).userNo=e),placeholder:`${y.$inputPlaceholder}用户编号`,"allow-clear":""},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),$(S,{span:6},{default:z((()=>[$(g,{"show-colon":"",label:"用户昵称",field:"nickname"},{default:z((()=>[$(v,{modelValue:N(je).nickname,"onUpdate:modelValue":_[7]||(_[7]=e=>N(je).nickname=e),placeholder:`${y.$inputPlaceholder}用户昵称`,"allow-clear":""},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),$(S,{span:6},{default:z((()=>[$(g,{"show-colon":"",label:"手机号",field:"mobile"},{default:z((()=>[$(I,{"hide-button":"",modelValue:N(je).mobile,"onUpdate:modelValue":_[8]||(_[8]=e=>N(je).mobile=e),placeholder:`${y.$inputPlaceholder}手机号`,"allow-clear":""},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),$(S,{span:6},{default:z((()=>[$(g,{"show-colon":"",label:"付款金额"},{default:z((()=>[$(ge,{align:"center",class:"w-full"},{default:z((()=>[$(S,{span:11},{default:z((()=>[$(g,{"no-style":"",field:"minAmount"},{default:z((()=>[$(I,{"hide-button":"",modelValue:N(je).minAmount,"onUpdate:modelValue":_[9]||(_[9]=e=>N(je).minAmount=e),placeholder:`${y.$inputPlaceholder}最小金额`,"allow-clear":""},{suffix:z((()=>_[13]||(_[13]=[P("元")]))),_:1},8,["modelValue","placeholder"])])),_:1})])),_:1}),$(S,{span:2,class:"text-center"},{default:z((()=>_[14]||(_[14]=[P("-")]))),_:1}),$(S,{span:11},{default:z((()=>[$(g,{"no-style":"",field:"maxAmount"},{default:z((()=>[$(I,{"hide-button":"",modelValue:N(je).maxAmount,"onUpdate:modelValue":_[10]||(_[10]=e=>N(je).maxAmount=e),placeholder:`${y.$inputPlaceholder}最大金额`,"allow-clear":""},{suffix:z((()=>_[15]||(_[15]=[P("元")]))),_:1},8,["modelValue","placeholder"])])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),$(S,{span:6},{default:z((()=>[$(g,{"show-colon":"",label:"下单时间",field:"createTime"},{default:z((()=>[$(xe,{modelValue:N(je).createTime,"onUpdate:modelValue":_[11]||(_[11]=e=>N(je).createTime=e),placeholder:[`${y.$selectPlaceholder}开始日期`,`${y.$selectPlaceholder}结束日期`]},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),$(S,{span:6},{default:z((()=>[$(g,{"hide-label":""},{default:z((()=>[$(Pe,{size:18},{default:z((()=>[$(ze,{type:"primary",onClick:_[12]||(_[12]=e=>N(Fe)(1))},{icon:z((()=>[$($e)])),default:z((()=>[_[16]||(_[16]=P(" 查询 "))])),_:1}),$(ze,{type:"outline",onClick:N(Ye)},{icon:z((()=>[$(Ne)])),default:z((()=>[_[17]||(_[17]=P(" 重置 "))])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),$(Ae,{bordered:!1,class:"flex-1 overflow-y-hidden","body-style":{height:"100%"}},{actions:z((()=>[N(ke).total?(j(),C(He,{key:0,current:N(ke).current,"page-size":N(ke).pageSize,"show-total":N(ke).showTotal,"show-page-size":N(ke).showPageSize,"page-size-options":N(ke).pageSizeOptions,total:N(ke).total,onChange:N(Fe),onPageSizeChange:N(Ie)},null,8,["current","page-size","show-total","show-page-size","page-size-options","total","onChange","onPageSizeChange"])):A("",!0)])),default:z((()=>[$(ge,{class:"mb-[12px]"},{default:z((()=>[$(S,{span:16},{default:z((()=>[$(Pe,null,{default:z((()=>[$(ze,{disabled:!N(Te).length,type:"primary",onClick:De},{icon:z((()=>[$(Ue)])),default:z((()=>[_[18]||(_[18]=P(" 导出 "))])),_:1},8,["disabled"])])),_:1})])),_:1})])),_:1}),$(qe,{size:"large","row-key":"id",loading:N(Ve),pagination:!1,data:N(Te),bordered:{cell:!0},scroll:{y:"calc(100% - 96px)"}},{columns:z((()=>[$(Oe,{align:"center",title:"序号",width:80},{cell:z((({rowIndex:e})=>[P(U(N(ke).pageSize*(N(ke).current-1)+e+1),1)])),_:1}),$(Oe,{align:"center",title:"订单信息",width:300,ellipsis:"",tooltip:""},{cell:z((({record:e})=>[$(Pe,null,{default:z((()=>[T("div",M,[T("p",null,"订单编号："+U(e.orderNo),1),T("p",R,"下单时间："+U(e.createTime),1),T("p",q,"备注信息："+U(e.remark||"-"),1)])])),_:2},1024)])),_:1}),$(Oe,{align:"center",title:"商品信息",width:300},{cell:z((({record:e})=>[T("div",null,[(j(!0),k(Y,null,F(e.commodities,((e,l)=>(j(),k("div",{key:l},[$(Pe,null,{default:z((()=>[$(Me,{shape:"square",size:32},{default:z((()=>[T("img",{src:e.cover},null,8,H)])),_:2},1024),T("div",B,[T("p",null,"商品名称："+U(e.name),1),T("p",L,"商品数量："+U(e.count)+" "+U(e.unit),1)])])),_:2},1024)])))),128))])])),_:1}),$(Oe,{align:"center",title:"用户信息",width:300,ellipsis:"",tooltip:""},{cell:z((({record:e})=>[$(Pe,null,{default:z((()=>[$(Me,{size:32},{default:z((()=>[T("img",{src:e.user.avatar},null,8,J)])),_:2},1024),T("div",E,[T("p",null,"用户编号："+U(e.user.no),1),T("p",G,"用户昵称："+U(e.user.nickname),1),T("p",K,"用户手机："+U(N(h)(e.user.mobile)),1)])])),_:2},1024)])),_:1}),$(Oe,{align:"center",title:"店铺信息",width:300,ellipsis:"",tooltip:""},{cell:z((({record:e})=>[$(Pe,null,{default:z((()=>[$(Me,{size:32},{default:z((()=>[T("img",{src:e.store.logo},null,8,Q)])),_:2},1024),T("div",W,[T("p",null,"店铺编号："+U(e.store.no),1),T("p",X,"店铺名称："+U(e.store.name),1),T("p",Z,"联系人："+U(e.store.contactName),1),T("p",ee,"联系电话："+U(N(h)(e.store.contactNumber)),1)])])),_:2},1024)])),_:1}),$(Oe,{align:"center",title:"支付信息",width:300},{cell:z((({record:e})=>[$(Pe,null,{default:z((()=>{var l;return[T("div",le,[T("p",null,"商品金额："+U(e.commodityAmount.toFixed(2))+" 元",1),T("p",ae,"优惠金额："+U(e.reduceAmount.toFixed(2))+" 元",1),T("p",te,"付款金额："+U(e.payAmount.toFixed(2))+" 元",1),T("p",oe,"付款方式："+U((null==(l=N(be).find((l=>l.value===e.payType)))?void 0:l.label)??""),1),T("p",re,"支付状态："+U(0===e.payState?"待付款":-1===e.payState?"支付失败":1===e.payState?"支付成功":""),1),T("p",de,"付款时间："+U(e.payTime),1)])]})),_:2},1024)])),_:1}),$(Oe,{align:"center",title:"收货信息",width:300},{cell:z((({record:e})=>[$(Pe,null,{default:z((()=>{var l,a,t,o;return[T("div",se,[T("p",null,[_[19]||(_[19]=P(" 配送方式： ")),$(Re,{color:(null==(l=N(Se).find((l=>l.value===e.deliveryType)))?void 0:l.color)??""},{default:z((()=>{var l;return[P(U((null==(l=N(Se).find((l=>l.value===e.deliveryType)))?void 0:l.label)??""),1)]})),_:2},1032,["color"])]),T("p",ne,"收货人："+U(1===e.deliveryType?null==(a=e.delivery)?void 0:a.userName:"-"),1),T("p",ie,"手机号码："+U(1===e.deliveryType?null==(t=e.delivery)?void 0:t.contactNumber:"-"),1),T("p",pe,"收货地址："+U(1===e.deliveryType?null==(o=e.delivery)?void 0:o.address:"-"),1)])]})),_:2},1024)])),_:1}),$(Oe,{align:"center",title:"订单状态",width:250},{cell:z((({record:e})=>[-1===e.state?(j(),C(Re,{key:0,color:"gray"},{default:z((()=>_[20]||(_[20]=[P("已取消")]))),_:1})):A("",!0),-2===e.state?(j(),C(Re,{key:1,color:"gray"},{default:z((()=>_[21]||(_[21]=[P("已关闭（已退款）")]))),_:1})):A("",!0),-3===e.state?(j(),C(Re,{key:2,color:"gray"},{default:z((()=>_[22]||(_[22]=[P("售后中")]))),_:1})):A("",!0),0===e.state?(j(),C(Re,{key:3,color:"red"},{default:z((()=>_[23]||(_[23]=[P("待付款")]))),_:1})):A("",!0),1===e.state?(j(),C(Re,{key:4,color:"orange"},{default:z((()=>[P(U(-1===e.afterSaleState?"待发货":"待发货（售后申请被商家驳回）"),1)])),_:2},1024)):A("",!0),2===e.state?(j(),C(Re,{key:5,color:"orange"},{default:z((()=>[P(U(-1===e.afterSaleState?1===e.deliveryType?"待收货":"待核销":1===e.deliveryType?"待收货（售后申请被商家驳回）":"待核销（售后申请被商家驳回）"),1)])),_:2},1024)):A("",!0),3===e.state?(j(),C(Re,{key:6,color:"blue"},{default:z((()=>[P(U(-1===e.commentState?"待评价":"已完成"),1)])),_:2},1024)):A("",!0)])),_:1}),$(Oe,{align:"center",title:"售后",width:400},{cell:z((({record:e})=>[1===e.afterSaleState?(j(),C(Pe,{key:0},{default:z((()=>{var l,a,t,o,r,d,s,n,i,p,u,c,m;return[T("div",ue,[T("p",ce,"售后原因："+U(null==(l=e.afterSale)?void 0:l.reason),1),T("p",me,"审核状态："+U(1===(null==(a=e.afterSale)?void 0:a.approveState)?"审核通过":-1===(null==(t=e.afterSale)?void 0:t.approveState)?"审核拒绝":"待审核"),1),-1===(null==(o=e.afterSale)?void 0:o.approveState)?(j(),k("p",fe,"拒绝原因："+U(null==(r=e.afterSale)?void 0:r.approveReason),1)):A("",!0),0!==(null==(d=e.afterSale)?void 0:d.approveState)?(j(),k("p",he,"审核时间："+U(N(w)(null==(s=e.afterSale)?void 0:s.approveTime).format("YYYY-MM-DD HH:mm:ss")),1)):A("",!0),1===(null==(n=e.afterSale)?void 0:n.approveState)?(j(),k(Y,{key:2},[T("p",ye,"退款金额："+U(null==(p=null==(i=e.afterSale)?void 0:i.refundAmount)?void 0:p.toFixed(2))+" 元",1),T("p",_e,"退款状态："+U(-1===(null==(u=e.afterSale)?void 0:u.refundState)?"未退款":0===(null==(c=e.afterSale)?void 0:c.refundState)?"退款中":"已退款"),1),1===e.refundState?(j(),k("p",ve,"退款时间："+U(N(w)(null==(m=e.afterSale)?void 0:m.refundTime).format("YYYY-MM-DD HH:mm:ss")),1)):A("",!0)],64)):A("",!0)])]})),_:2},1024)):(j(),k("span",we,"-"))])),_:1})])),_:1},8,["loading","data"])])),_:1})])])}}});export{ge as default};

import{_ as e,g as a,s,f as t}from"./index-D-8JbLQk.js";import{u as l,G as o,a as n,R as r,C as u}from"./index-BEo1tUsK.js";import{d as p,t as c,c as f,B as i,a as m,a3 as d,j as v,k as y,m as x,l as b,O as w,r as j,i as O,G as I}from"./vue-D-10XvVk.js";import{u as $}from"./use-index-D_ozg7PK.js";var g=e(p({name:"Grid",props:{cols:{type:[Number,Object],default:24},rowGap:{type:[Number,Object],default:0},colGap:{type:[Number,Object],default:0},collapsed:{type:Boolean,default:!1},collapsedRows:{type:Number,default:1}},setup(e){const{cols:s,rowGap:t,colGap:r,collapsedRows:u,collapsed:p}=c(e),v=l(s,24),y=l(r,0),x=l(t,0),b=a("grid"),w=f((()=>[b])),j=f((()=>[{gap:`${x.value}px ${y.value}px`,"grid-template-columns":`repeat(${v.value}, minmax(0px, 1fr))`}])),O=i(new Map),I=f((()=>{const e=[];for(const[a,s]of O.entries())e[a]=s;return e})),$=i({overflow:!1,displayIndexList:[],cols:v.value,colGap:y.value});return m((()=>{$.cols=v.value,$.colGap=y.value})),m((()=>{const e=function({cols:e,collapsed:a,collapsedRows:s,itemDataList:t}){let l=!1,o=[];function n(a){return Math.ceil(a/e)>s}if(a){let e=0;for(let a=0;a<t.length;a++)t[a].suffix&&(e+=t[a].span,o.push(a));if(!n(e)){let a=0;for(;a<t.length;){const s=t[a];if(!s.suffix){if(e+=s.span,n(e))break;o.push(a)}a++}}l=t.some(((e,a)=>!e.suffix&&!o.includes(a)))}else o=t.map(((e,a)=>a));return{overflow:l,displayIndexList:o}}({cols:v.value,collapsed:p.value,collapsedRows:u.value,itemDataList:I.value});$.overflow=e.overflow,$.displayIndexList=e.displayIndexList})),d(o,$),d(n,{collectItemData(e,a){O.set(e,a)},removeItemData(e){O.delete(e)}}),{classNames:w,style:j}}}),[["render",function(e,a,s,t,l,o){return v(),y("div",{class:b(e.classNames),style:w(e.style)},[x(e.$slots,"default")],6)}]]),G=Object.defineProperty,N=Object.defineProperties,R=Object.getOwnPropertyDescriptors,h=Object.getOwnPropertySymbols,L=Object.prototype.hasOwnProperty,D=Object.prototype.propertyIsEnumerable,P=(e,a,s)=>a in e?G(e,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[a]=s;var M=e(p({name:"GridItem",props:{span:{type:[Number,Object],default:1},offset:{type:[Number,Object],default:0},suffix:{type:Boolean,default:!1}},setup(e){const s=a("grid-item"),t=j(),{computedIndex:r}=$({itemRef:t,selector:`.${s}`}),u=O(o,{overflow:!1,displayIndexList:[],cols:24,colGap:0}),p=O(n),i=f((()=>{var e;return null==(e=null==u?void 0:u.displayIndexList)?void 0:e.includes(r.value)})),{span:d,offset:v}=c(e),y=l(d,1),x=l(v,0),b=f((()=>{return function(e,a){var s,t;const l=null!=(s=a.span)?s:1,o=null!=(t=a.offset)?t:0,n=Math.min(o,e);return{span:Math.min(n>0?l+o:l,e),offset:n,suffix:"suffix"in a&&!1!==a.suffix}}(u.cols,(a=((e,a)=>{for(var s in a||(a={}))L.call(a,s)&&P(e,s,a[s]);if(h)for(var s of h(a))D.call(a,s)&&P(e,s,a[s]);return e})({},e),s={span:y.value,offset:x.value},N(a,R(s))));var a,s})),w=f((()=>[s])),g=f((()=>{const{offset:e,span:a}=b.value,{colGap:s}=u;if(e>0){return{"margin-left":`calc((${`(100% - ${s*(a-1)}px) / ${a}`} * ${e}) + ${s*e}px)`}}return{}})),G=f((()=>{const{suffix:e,span:a}=b.value,{cols:s}=u;return e?""+(s-a+1):`span ${a}`})),M=f((()=>{const{span:e}=b.value;return t.value?[{"grid-column":`${G.value} / span ${e}`},g.value,i.value&&0!==e?{}:{display:"none"}]:[]}));return m((()=>{-1!==r.value&&(null==p||p.collectItemData(r.value,b.value))})),I((()=>{-1!==r.value&&(null==p||p.removeItemData(r.value))})),{classNames:w,style:M,domRef:t,overflow:f((()=>u.overflow))}}}),[["render",function(e,a,s,t,l,o){return v(),y("div",{ref:"domRef",class:b(e.classNames),style:w(e.style)},[x(e.$slots,"default",{overflow:e.overflow})],6)}]]);const B=Object.assign(g,{Row:r,Col:u,Item:M,install:(e,a)=>{s(e,a);const l=t(a);e.component(l+r.name,r),e.component(l+u.name,u),e.component(l+g.name,g),e.component(l+M.name,M)}});export{B as G,M as a};

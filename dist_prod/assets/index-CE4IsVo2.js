import"./index-DOhy6BH_.js";import{_ as s}from"./_plugin-vue_export-helper-BCo6x5W8.js";import{_ as a,g as e}from"./index-D-8JbLQk.js";import{d as o,j as r,k as t,m as n,l as u,y as f,z as l,I as m}from"./vue-D-10XvVk.js";var p=a(o({name:"LayoutFooter",setup:()=>({classNames:[e("layout-footer")]})}),[["render",function(s,a,e,o,f,l){return r(),t("footer",{class:u(s.classNames)},[n(s.$slots,"default")],2)}]]);const c=s({},[["render",function(s,a){const e=p;return r(),f(e,{class:"footer"},{default:l((()=>a[0]||(a[0]=[m("唐山星昇科技有限公司")]))),_:1})}],["__scopeId","data-v-b6feb510"]]);export{p as L,c as P};

import{d as e,t as o,r as t,c as n,o as a,b as l,w as s,f as r,j as i,y as d,z as c,aj as p,Z as u,k as f,v as b,A as m,a6 as v,l as y,a7 as C,p as h,q as k,O,m as x,I as g,J as w,U as B,n as j}from"./vue-D-10XvVk.js";import{_ as $,I as P,a as S,g as T,W as I,k as _,v as E,c as A,X as L,e as V,w as z,s as F,f as D,Y as M,Z as H}from"./index-D-8JbLQk.js";import{C as N,a as Z,h as q,K as J,o as K}from"./index-DDFSMqsG.js";import{B as R}from"./index-DGtjsHgS.js";import{u as U}from"./index-O7pr3qsq.js";var W=Object.defineProperty,X=Object.getOwnPropertySymbols,Y=Object.prototype.hasOwnProperty,G=Object.prototype.propertyIsEnumerable,Q=(e,o,t)=>o in e?W(e,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[o]=t;const ee=["top","right","bottom","left"];var oe=$(e({name:"Drawer",components:{ClientOnly:N,ArcoButton:R,IconHover:P,IconClose:S},inheritAttrs:!1,props:{visible:{type:Boolean,default:!1},defaultVisible:{type:Boolean,default:!1},placement:{type:String,default:"right",validator:e=>ee.includes(e)},title:String,mask:{type:Boolean,default:!0},maskClosable:{type:Boolean,default:!0},closable:{type:Boolean,default:!0},okText:String,cancelText:String,okLoading:{type:Boolean,default:!1},okButtonProps:{type:Object},cancelButtonProps:{type:Object},unmountOnClose:Boolean,width:{type:[Number,String],default:250},height:{type:[Number,String],default:250},popupContainer:{type:[String,Object],default:"body"},drawerStyle:{type:Object},onBeforeOk:{type:Function},onBeforeCancel:{type:Function},escToClose:{type:Boolean,default:!0},renderToBody:{type:Boolean,default:!0},header:{type:Boolean,default:!0},footer:{type:Boolean,default:!0},hideCancel:{type:Boolean,default:!1}},emits:{"update:visible":e=>!0,ok:e=>!0,cancel:e=>!0,open:()=>!0,close:()=>!0,beforeOpen:()=>!0,beforeClose:()=>!0},setup(e,{emit:r}){const{popupContainer:i}=o(e),d=T("drawer"),{t:c}=Z(),p=t(e.defaultVisible),u=n((()=>{var o;return null!=(o=e.visible)?o:p.value})),f=t(!1),b=n((()=>e.okLoading||f.value)),{teleportContainer:m,containerRef:v}=q({popupContainer:i,visible:u}),y=t(u.value);let C=!1;const h=o=>{e.escToClose&&o.key===J.ESC&&g()&&$(o)},k=()=>{e.escToClose&&!C&&(C=!0,E(document.documentElement,"keydown",h))},O=()=>{C&&(C=!1,z(document.documentElement,"keydown",h))},{zIndex:x,isLastDialog:g}=I("dialog",{visible:u}),w=n((()=>(null==v?void 0:v.value)===document.body));let B=0;const j=()=>{B++,f.value&&(f.value=!1),p.value=!1,r("update:visible",!1)},$=o=>{var t;let n=!0;A(e.onBeforeCancel)&&(n=null!=(t=e.onBeforeCancel())&&t),n&&(r("cancel",o),j())},{setOverflowHidden:P,resetOverflow:S}=U(v);a((()=>{u.value&&(y.value=!0,P(),k())})),l((()=>{S(),O()})),s(u,(e=>{p.value!==e&&(p.value=e),e?(r("beforeOpen"),y.value=!0,P(),k()):(r("beforeClose"),O())}));return{prefixCls:d,style:n((()=>{var o;const t=((e,o)=>{for(var t in o||(o={}))Y.call(o,t)&&Q(e,t,o[t]);if(X)for(var t of X(o))G.call(o,t)&&Q(e,t,o[t]);return e})({[e.placement]:0},null!=(o=e.drawerStyle)?o:{});return["right","left"].includes(e.placement)?t.width=_(e.width)?`${e.width}px`:e.width:t.height=_(e.height)?`${e.height}px`:e.height,t})),t:c,mounted:y,computedVisible:u,mergedOkLoading:b,zIndex:x,handleOk:async o=>{const t=B,n=await new Promise((async o=>{var t;if(A(e.onBeforeOk)){let a=e.onBeforeOk(((e=!0)=>o(e)));if(!L(a)&&V(a)||(f.value=!0),L(a))try{a=null==(t=await a)||t}catch(n){a=!1}V(a)&&o(a)}else o(!0)}));t===B&&(n?(r("ok",o),j()):f.value&&(f.value=!1))},handleCancel:$,handleOpen:()=>{u.value&&r("open")},handleClose:()=>{u.value||(y.value=!1,S(),r("close"))},handleMask:o=>{e.maskClosable&&$(o)},isFixed:w,teleportContainer:m}}}),[["render",function(e,o,t,n,a,l){const s=r("icon-close"),B=r("icon-hover"),j=r("arco-button"),$=r("client-only");return i(),d($,null,{default:c((()=>[(i(),d(p,{to:e.teleportContainer,disabled:!e.renderToBody},[!e.unmountOnClose||e.computedVisible||e.mounted?u((i(),f("div",b({key:0,class:`${e.prefixCls}-container`,style:e.isFixed?{zIndex:e.zIndex}:{zIndex:"inherit",position:"absolute"}},e.$attrs),[m(v,{name:"fade-drawer",appear:""},{default:c((()=>[e.mask?u((i(),f("div",{key:0,class:y(`${e.prefixCls}-mask`),onClick:o[0]||(o[0]=(...o)=>e.handleMask&&e.handleMask(...o))},null,2)),[[C,e.computedVisible]]):h("v-if",!0)])),_:1}),m(v,{name:`slide-${e.placement}-drawer`,appear:"",onAfterEnter:e.handleOpen,onAfterLeave:e.handleClose},{default:c((()=>[u(k("div",{class:y(e.prefixCls),style:O(e.style)},[e.header?(i(),f("div",{key:0,class:y(`${e.prefixCls}-header`)},[x(e.$slots,"header",{},(()=>[e.$slots.title||e.title?(i(),f("div",{key:0,class:y(`${e.prefixCls}-title`)},[x(e.$slots,"title",{},(()=>[g(w(e.title),1)]))],2)):h("v-if",!0),e.closable?(i(),f("div",{key:1,tabindex:"-1",role:"button","aria-label":"Close",class:y(`${e.prefixCls}-close-btn`),onClick:o[1]||(o[1]=(...o)=>e.handleCancel&&e.handleCancel(...o))},[m(B,null,{default:c((()=>[m(s)])),_:1})],2)):h("v-if",!0)]))],2)):h("v-if",!0),k("div",{class:y(`${e.prefixCls}-body`)},[x(e.$slots,"default")],2),e.footer?(i(),f("div",{key:1,class:y(`${e.prefixCls}-footer`)},[x(e.$slots,"footer",{},(()=>[e.hideCancel?h("v-if",!0):(i(),d(j,b({key:0},e.cancelButtonProps,{onClick:e.handleCancel}),{default:c((()=>[g(w(e.cancelText||e.t("drawer.cancelText")),1)])),_:1},16,["onClick"])),m(j,b({type:"primary",loading:e.mergedOkLoading},e.okButtonProps,{onClick:e.handleOk}),{default:c((()=>[g(w(e.okText||e.t("drawer.okText")),1)])),_:1},16,["loading","onClick"])]))],2)):h("v-if",!0)],6),[[C,e.computedVisible]])])),_:3},8,["name","onAfterEnter","onAfterLeave"])],16)),[[C,e.computedVisible||e.mounted]]):h("v-if",!0)],8,["to","disabled"]))])),_:3})}]]),te=Object.defineProperty,ne=Object.getOwnPropertySymbols,ae=Object.prototype.hasOwnProperty,le=Object.prototype.propertyIsEnumerable,se=(e,o,t)=>o in e?te(e,o,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[o]=t,re=(e,o)=>{for(var t in o||(o={}))ae.call(o,t)&&se(e,t,o[t]);if(ne)for(var t of ne(o))le.call(o,t)&&se(e,t,o[t]);return e};const ie=(e,o)=>{let t=M("drawer");const n=m(oe,re(re(re({},{visible:!0,renderToBody:!1,unmountOnClose:!0,onOk:()=>{n.component&&(n.component.props.visible=!1),A(e.onOk)&&e.onOk()},onCancel:()=>{n.component&&(n.component.props.visible=!1),A(e.onCancel)&&e.onCancel()},onClose:async()=>{await j(),t&&(B(null,t),document.body.removeChild(t)),t=null,A(e.onClose)&&e.onClose()}}),K(e,["content","title","footer","visible","unmountOnClose","onOk","onCancel","onClose"])),{header:"boolean"==typeof e.header?e.header:void 0,footer:"boolean"==typeof e.footer?e.footer:void 0}),{default:H(e.content),header:"boolean"!=typeof e.header?H(e.header):void 0,title:H(e.title),footer:"boolean"!=typeof e.footer?H(e.footer):void 0});return(null!=o?o:de._context)&&(n.appContext=null!=o?o:de._context),B(n,t),document.body.appendChild(t),{close:()=>{n.component&&(n.component.props.visible=!1)},update:e=>{n.component&&Object.entries(e).forEach((([e,o])=>{n.component.props[e]=o}))}}},de=Object.assign(oe,{open:ie,install:(e,o)=>{F(e,o);const t=D(o);e.component(t+oe.name,oe);const n={open:(o,t=e._context)=>ie(o,t)};e.config.globalProperties.$drawer=n},_context:null});export{de as D};

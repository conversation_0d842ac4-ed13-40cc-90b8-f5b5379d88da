import"./index-DOhy6BH_.js";import{C as e}from"./index-CdWxsKz_.js";import"./index-DQjhgQFu.js";import{S as t}from"./index-DGtjsHgS.js";import{L as o}from"./index-Cuq5XRs0.js";import{e as a,I as s}from"./index-DDFSMqsG.js";import"./index-DD6vSYIM.js";import{T as n,a as i}from"./index-DdMaxvYa.js";import{_ as r}from"./index-HLwNT5T9.js";/* empty css              */import{d as l,r as m,o as d,k as p,q as c,A as u,z as f,f as j,j as x,u as h,J as y,y as b,p as _,I as g,af as k}from"./vue-D-10XvVk.js";import{r as V,a as C}from"./apiConfig-CUzVnK3l.js";import{$ as T,a0 as v,N as R}from"./index-D-8JbLQk.js";import{I as U}from"./index-DfEXMvnc.js";import"./pick-Ccd8Sfcm.js";import"./resize-observer-Dtogi-DJ.js";import"./ResizeObserver.es-CzGuHLZU.js";import"./use-children-components-v8i8lsOx.js";import"./index-CuYx5vtf.js";import"./render-function-CAXdZVZM.js";import"./apiUpload-DpATemHF.js";import"./_plugin-vue_export-helper-BCo6x5W8.js";const w={class:"page-container"},$={class:"h-full flex flex-col gap-[18px]"},z=l({__name:"index",setup(l){const z=m([]);return d((()=>{(async()=>{try{const{data:e}=await V({});z.value=e.filter((e=>"customerSetting"===e.type)).map((e=>{const{content:t,...o}=e;return{...o,content:"platformCommissionRate"===o.subType||"userCommissionRate"===o.subType?T(Number(t),100):t}}))}catch(e){}})()})),(l,m)=>{const d=j("icon-question-circle"),V=a,T=t,I=n,N=s,q=U,P=r,S=o,A=i,J=e;return x(),p("div",w,[c("div",$,[u(J,{title:"基础配置",bordered:!1},{default:f((()=>[u(A,{size:"large","row-key":"id",pagination:!1,data:h(z),bordered:{cell:!0}},{columns:f((()=>[u(I,{align:"center",title:"名称",width:150},{cell:f((({record:e})=>[u(T,null,{default:f((()=>[c("span",null,y(e.name),1),e.remark?(x(),b(V,{key:0,content:e.remark},{default:f((()=>[u(d)])),_:2},1032,["content"])):_("",!0)])),_:2},1024)])),_:1}),u(I,{align:"center",title:"内容",width:300},{cell:f((({record:e})=>["contactNumber"===e.subType?(x(),b(N,{key:0,modelValue:e.content,"onUpdate:modelValue":t=>e.content=t,placeholder:l.$inputPlaceholder},null,8,["modelValue","onUpdate:modelValue","placeholder"])):"platformCommissionRate"===e.subType||"userCommissionRate"===e.subType?(x(),b(q,{key:1,modelValue:e.content,"onUpdate:modelValue":t=>e.content=t,"hide-button":"",placeholder:l.$inputPlaceholder},{suffix:f((()=>m[0]||(m[0]=[g("%")]))),_:2},1032,["modelValue","onUpdate:modelValue","placeholder"])):(x(),b(P,{key:2,modelValue:e.content,"onUpdate:modelValue":t=>e.content=t,accept:"image/*",title:"上传图片",class:"!justify-center"},null,8,["modelValue","onUpdate:modelValue"]))])),_:1}),u(I,{align:"center",title:"操作",width:200,fixed:"right"},{cell:f((({record:e})=>[u(S,{onClick:k((()=>{h(C)(e.id,{content:"platformCommissionRate"===e.subType||"userCommissionRate"===e.subType?h(v)(e.content,100):e.content}).then((()=>{h(R).success({title:"成功提示",content:`已修改${e.name}`})}))}),["stop"])},{default:f((()=>m[1]||(m[1]=[g(" 提交 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])])),_:1})])])}}});export{z as default};

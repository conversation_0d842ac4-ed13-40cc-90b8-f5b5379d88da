import{_ as e,g as l,x as a,o as t,p as n,k as o,s as r,f as u,i,ae as s,C as d,h as c,e as p,I as v,aF as m,aG as f,aH as h,c as y,B as g,V as b,aw as x,u as C,v as S,w,a5 as $,j as k}from"./index-D-8JbLQk.js";import{S as j,R as O,i as I,a as z,u as P,V as A,d as F}from"./index-Cuq5XRs0.js";import{d as R,c as B,j as E,k as K,m as N,I as L,J as T,l as M,O as D,f as W,y as V,z as H,a4 as q,A as _,r as G,w as J,p as U,F as Y,q as X,n as Q,v as Z,t as ee,B as le,L as ae,o as te,S as ne,H as oe,i as re,a as ue,a3 as ie,a5 as se,b as de,g as ce}from"./vue-D-10XvVk.js";import{S as pe}from"./index-CdWxsKz_.js";import{a as ve,e as me,R as fe,T as he,k as ye,s as ge,o as be}from"./index-DDFSMqsG.js";import{I as xe}from"./index-DfEXMvnc.js";import{u as Ce}from"./index-DOhy6BH_.js";import{S as Se,E as we}from"./index-DD6vSYIM.js";import{C as $e}from"./index-DQjhgQFu.js";import{B as ke}from"./index-DGtjsHgS.js";import{R as je}from"./resize-observer-Dtogi-DJ.js";import{u as Oe}from"./use-children-components-v8i8lsOx.js";var Ie=e(R({name:"Pager",props:{pageNumber:{type:Number},current:{type:Number},disabled:{type:Boolean,default:!1},style:{type:Object},activeStyle:{type:Object}},emits:["click"],setup(e,{emit:a}){const t=l("pagination-item"),n=B((()=>e.current===e.pageNumber)),o=B((()=>[t,{[`${t}-active`]:n.value}])),r=B((()=>n.value?e.activeStyle:e.style));return{prefixCls:t,cls:o,mergedStyle:r,handleClick:l=>{e.disabled||a("click",e.pageNumber,l)}}}}),[["render",function(e,l,a,t,n,o){return E(),K("li",{class:M(e.cls),style:D(e.mergedStyle),onClick:l[0]||(l[0]=(...l)=>e.handleClick&&e.handleClick(...l))},[N(e.$slots,"default",{page:e.pageNumber},(()=>[L(T(e.pageNumber),1)]))],6)}]]);const ze=(e,{min:l,max:a})=>e<l?l:e>a?a:e;var Pe=e(R({name:"StepPager",components:{IconLeft:a,IconRight:t},props:{pages:{type:Number,required:!0},current:{type:Number,required:!0},type:{type:String,required:!0},disabled:{type:Boolean,default:!1},simple:{type:Boolean,default:!1}},emits:["click"],setup(e,{emit:a}){const t=l("pagination-item"),n="next"===e.type,o=B((()=>e.disabled?e.disabled:!e.pages||(!(!n||e.current!==e.pages)||!n&&e.current<=1))),r=B((()=>ze(e.current+(n?1:-1),{min:1,max:e.pages}))),u=B((()=>[t,`${t}-${e.type}`,{[`${t}-disabled`]:o.value}]));return{prefixCls:t,cls:u,isNext:n,handleClick:e=>{o.value||a("click",r.value)}}}}),[["render",function(e,l,a,t,n,o){const r=W("icon-right"),u=W("icon-left");return E(),V(q(e.simple?"span":"li"),{class:M(e.cls),onClick:e.handleClick},{default:H((()=>[N(e.$slots,"default",{type:e.isNext?"next":"previous"},(()=>[e.isNext?(E(),V(r,{key:0})):(E(),V(u,{key:1}))]))])),_:3},8,["class","onClick"])}]]);var Ae=e(R({name:"EllipsisPager",components:{IconMore:n},props:{current:{type:Number,required:!0},step:{type:Number,default:5},pages:{type:Number,required:!0}},emits:["click"],setup(e,{emit:a}){const t=l("pagination-item"),n=B((()=>ze(e.current+e.step,{min:1,max:e.pages}))),o=B((()=>[t,`${t}-ellipsis`]));return{prefixCls:t,cls:o,handleClick:e=>{a("click",n.value)}}}}),[["render",function(e,l,a,t,n,o){const r=W("icon-more");return E(),K("li",{class:M(e.cls),onClick:l[0]||(l[0]=(...l)=>e.handleClick&&e.handleClick(...l))},[N(e.$slots,"default",{},(()=>[_(r)]))],2)}]]);var Fe=e(R({name:"PageJumper",components:{InputNumber:xe},props:{current:{type:Number,required:!0},simple:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},pages:{type:Number,required:!0},size:{type:String},onChange:{type:Function}},emits:["change"],setup(e,{emit:a}){const t=l("pagination-jumper"),{t:n}=ve(),o=G(e.simple?e.current:void 0);J((()=>e.current),(l=>{e.simple&&l!==o.value&&(o.value=l)}));const r=B((()=>[t,{[`${t}-simple`]:e.simple}]));return{prefixCls:t,cls:r,t:n,inputValue:o,handleChange:l=>{a("change",o.value),Q((()=>{e.simple||(o.value=void 0)}))},handleFormatter:e=>{const l=parseInt(e.toString(),10);return Number.isNaN(l)?void 0:String(l)}}}}),[["render",function(e,l,a,t,n,o){const r=W("input-number");return E(),K("span",{class:M(e.cls)},[e.simple?U("v-if",!0):(E(),K("span",{key:0,class:M([`${e.prefixCls}-prepend`,`${e.prefixCls}-text-goto`])},[N(e.$slots,"jumper-prepend",{},(()=>[L(T(e.t("pagination.goto")),1)]))],2)),_(r,{modelValue:e.inputValue,"onUpdate:modelValue":l[0]||(l[0]=l=>e.inputValue=l),class:M(`${e.prefixCls}-input`),min:1,max:e.pages,size:e.size,disabled:e.disabled,"hide-button":"",formatter:e.handleFormatter,onChange:e.handleChange},null,8,["modelValue","class","max","size","disabled","formatter","onChange"]),e.$slots["jumper-append"]?(E(),K("span",{key:1,class:M(`${e.prefixCls}-append`)},[N(e.$slots,"jumper-append")],2)):U("v-if",!0),e.simple?(E(),K(Y,{key:2},[X("span",{class:M(`${e.prefixCls}-separator`)},"/",2),X("span",{class:M(`${e.prefixCls}-total-page`)},T(e.pages),3)],64)):U("v-if",!0)],2)}]]);var Re=e(R({name:"PageOptions",components:{ArcoSelect:j},props:{sizeOptions:{type:Array,required:!0},pageSize:Number,disabled:Boolean,size:{type:String},onChange:{type:Function},selectProps:{type:Object}},emits:["change"],setup(e,{emit:a}){const t=l("pagination-options"),{t:n}=ve();return{prefixCls:t,options:B((()=>e.sizeOptions.map((e=>({value:e,label:`${e} ${n("pagination.countPerPage")}`}))))),handleChange:e=>{a("change",e)}}}}),[["render",function(e,l,a,t,n,o){const r=W("arco-select");return E(),K("span",{class:M(e.prefixCls)},[_(r,Z({"model-value":e.pageSize,options:e.options,size:e.size,disabled:e.disabled},e.selectProps,{onChange:e.handleChange}),null,16,["model-value","options","size","disabled","onChange"])],2)}]]),Be=R({name:"Pagination",props:{total:{type:Number,required:!0},current:Number,defaultCurrent:{type:Number,default:1},pageSize:Number,defaultPageSize:{type:Number,default:10},disabled:{type:Boolean,default:!1},hideOnSinglePage:{type:Boolean,default:!1},simple:{type:Boolean,default:!1},showTotal:{type:Boolean,default:!1},showMore:{type:Boolean,default:!1},showJumper:{type:Boolean,default:!1},showPageSize:{type:Boolean,default:!1},pageSizeOptions:{type:Array,default:()=>[10,20,30,40,50]},pageSizeProps:{type:Object},size:{type:String},pageItemStyle:{type:Object},activePageItemStyle:{type:Object},baseSize:{type:Number,default:6},bufferSize:{type:Number,default:2},autoAdjust:{type:Boolean,default:!0}},emits:{"update:current":e=>!0,"update:pageSize":e=>!0,change:e=>!0,pageSizeChange:e=>!0},setup(e,{emit:a,slots:t}){const n=l("pagination"),{t:r}=ve(),{disabled:u,pageItemStyle:i,activePageItemStyle:s,size:d}=ee(e),{mergedSize:c}=Ce(d),p=G(e.defaultCurrent),v=G(e.defaultPageSize),m=B((()=>{var l;return null!=(l=e.current)?l:p.value})),f=B((()=>{var l;return null!=(l=e.pageSize)?l:v.value})),h=B((()=>Math.ceil(e.total/f.value))),y=l=>{l!==m.value&&o(l)&&!e.disabled&&(p.value=l,a("update:current",l),a("change",l))},g=e=>{v.value=e,a("update:pageSize",e),a("pageSizeChange",e)},b=le({current:m,pages:h,disabled:u,style:i,activeStyle:s,onClick:y}),x=(e,l={})=>"more"===e?_(Ae,Z(l,b),{default:t["page-item-ellipsis"]}):"previous"===e?_(Pe,Z({type:"previous"},l,b),{default:t["page-item-step"]}):"next"===e?_(Pe,Z({type:"next"},l,b),{default:t["page-item-step"]}):_(Ie,Z(l,b),{default:t["page-item"]}),C=B((()=>{const l=[];if(h.value<e.baseSize+2*e.bufferSize)for(let e=1;e<=h.value;e++)l.push(x("page",{key:e,pageNumber:e}));else{let a=1,t=h.value,n=!1,o=!1;m.value>2+e.bufferSize&&(n=!0,a=Math.min(m.value-e.bufferSize,h.value-2*e.bufferSize)),m.value<h.value-(e.bufferSize+1)&&(o=!0,t=Math.max(m.value+e.bufferSize,2*e.bufferSize+1)),n&&(l.push(x("page",{key:1,pageNumber:1})),l.push(x("more",{key:"left-ellipsis-pager",step:-(2*e.bufferSize+1)})));for(let e=a;e<=t;e++)l.push(x("page",{key:e,pageNumber:e}));o&&(l.push(x("more",{key:"right-ellipsis-pager",step:2*e.bufferSize+1})),l.push(x("page",{key:h.value,pageNumber:h.value})))}return l}));J(f,((l,t)=>{if(e.autoAdjust&&l!==t&&m.value>1){const e=t*(m.value-1)+1,n=Math.ceil(e/l);n!==m.value&&(p.value=n,a("update:current",n),a("change",n))}})),J(h,((l,t)=>{if(e.autoAdjust&&l!==t&&m.value>1&&m.value>l){const e=Math.max(l,1);p.value=e,a("update:current",e),a("change",e)}}));const S=B((()=>[n,`${n}-size-${c.value}`,{[`${n}-simple`]:e.simple,[`${n}-disabled`]:e.disabled}]));return()=>{var l,a;return e.hideOnSinglePage&&h.value<=1?null:_("div",{class:S.value},[e.showTotal&&_("span",{class:`${n}-total`},[null!=(a=null==(l=t.total)?void 0:l.call(t,{total:e.total}))?a:r("pagination.total",e.total)]),e.simple?_("span",{class:`${n}-simple`},[x("previous",{simple:!0}),_(Fe,{disabled:e.disabled,current:m.value,size:c.value,pages:h.value,simple:!0,onChange:y},null),x("next",{simple:!0})]):_("ul",{class:`${n}-list`},[x("previous",{simple:!0}),C.value,e.showMore&&x("more",{key:"more",step:2*e.bufferSize+1}),x("next",{simple:!0})]),e.showPageSize&&_(Re,{disabled:e.disabled,sizeOptions:e.pageSizeOptions,pageSize:f.value,size:c.value,onChange:g,selectProps:e.pageSizeProps},null),!e.simple&&e.showJumper&&_(Fe,{disabled:e.disabled,current:m.value,pages:h.value,size:c.value,onChange:y},{"jumper-prepend":t["jumper-prepend"],"jumper-append":t["jumper-append"]})])}}});const Ee=Object.assign(Be,{install:(e,l)=>{r(e,l);const a=u(l);e.component(a+Be.name,Be)}});var Ke=Object.defineProperty,Ne=Object.defineProperties,Le=Object.getOwnPropertyDescriptors,Te=Object.getOwnPropertySymbols,Me=Object.prototype.hasOwnProperty,De=Object.prototype.propertyIsEnumerable,We=(e,l,a)=>l in e?Ke(e,l,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[l]=a,Ve=(e,l)=>{for(var a in l||(l={}))Me.call(l,a)&&We(e,a,l[a]);if(Te)for(var a of Te(l))De.call(l,a)&&We(e,a,l[a]);return e};const He=e=>{let l=0;const a=e=>{if(s(e)&&e.length>0)for(const t of e)t.children?a(t.children):l+=1};return a(e),l},qe=e=>{let l=0;if(s(e)&&e.length>0){l=1;for(const a of e)if(a.children){const e=qe(a.children);e>0&&(l=Math.max(l,e+1))}}return l},_e=(e,l)=>{let{parent:a}=e;for(;a;)a.fixed===l&&("left"===l?a.isLastLeftFixed=!0:a.isFirstRightFixed=!0),a=a.parent},Ge=(e,l)=>{var a;const t=((e,l)=>{for(let a=0;a<e.length;a++)if(e[a].name===l)return a;return-1})(l,e.name);if(t<=0)return 0;let n=0;const o=l.slice(0,t);for(const r of o)n+=null!=(a=r.width)?a:0;return n},Je=e=>e.children&&e.children.length>0?Je(e.children[0]):e,Ue=(e,{dataColumns:l,operations:a})=>{var t,n,o;let r=0;if("left"===e.fixed){for(const e of a)r+=null!=(t=e.width)?t:40;const u=Je(e);for(const e of l){if(u.dataIndex===e.dataIndex)break;r+=null!=(o=null!=(n=e._resizeWidth)?n:e.width)?o:0}return r}const u=(e=>e.children&&e.children.length>0?Je(e.children[e.children.length-1]):e)(e);for(let i=l.length-1;i>0;i--){const e=l[i];if(u.dataIndex===e.dataIndex)break;"right"===e.fixed&&(r+=e.width)}return r},Ye=(e,l)=>l.fixed?[`${e}-col-fixed-left`,{[`${e}-col-fixed-left-last`]:l.isLastLeftFixed}]:[],Xe=(e,l)=>"left"===l.fixed?[`${e}-col-fixed-left`,{[`${e}-col-fixed-left-last`]:l.isLastLeftFixed}]:"right"===l.fixed?[`${e}-col-fixed-right`,{[`${e}-col-fixed-right-first`]:l.isFirstRightFixed}]:[],Qe=(e,{dataColumns:l,operations:a})=>{if(e.fixed){const t=`${Ue(e,{dataColumns:l,operations:a})}px`;return"left"===e.fixed?{left:t}:{right:t}}return{}},Ze=(e,l)=>e.fixed?{left:`${Ge(e,l)}px`}:{};function el(e){return e.map((e=>{const l=Ve({},e);return l.children&&(l.children=el(l.children)),l}))}function ll(e){return e.map((e=>{const l=e.raw;return e.children&&l.children&&(l.children=ll(e.children)),e.raw}))}const al=e=>{const l=[];if(e.children)for(const a of e.children)a.isLeaf?l.push(a.key):l.push(...al(a));return l},tl=(e,l,a=!1)=>a?e.filter((e=>!l.includes(e))):Array.from(new Set(e.concat(l)));var nl=e(R({name:"ColGroup",props:{dataColumns:{type:Array,required:!0},operations:{type:Array,required:!0},columnWidth:{type:Object}},setup:()=>({fixedWidth:(e,l)=>{if(e){return{width:`${e}px`,minWidth:`${Math.max(e,l||0)}px`,maxWidth:`${e}px`}}if(l)return{minWidth:`${l}px`}}})}),[["render",function(e,l,a,t,n,o){return E(),K("colgroup",null,[(E(!0),K(Y,null,ae(e.operations,(l=>(E(),K("col",{key:`arco-col-${l.name}`,class:M(`arco-table-${l.name}-col`),style:D(e.fixedWidth(l.width))},null,6)))),128)),(E(!0),K(Y,null,ae(e.dataColumns,(l=>(E(),K("col",{key:`arco-col-${l.dataIndex}`,style:D(e.fixedWidth(e.columnWidth&&l.dataIndex&&e.columnWidth[l.dataIndex]||l.width,l.minWidth))},null,4)))),128))])}]]),ol=R({name:"Thead",setup:(e,{slots:l})=>()=>{var e,a;return _(null!=(a=null==(e=l.thead)?void 0:e.call(l)[0])?a:"thead",null,{default:l.default})}}),rl=R({name:"Tbody",setup:(e,{slots:l})=>()=>{var e,a;return _(null!=(a=null==(e=l.tbody)?void 0:e.call(l)[0])?a:"tbody",null,{default:l.default})}}),ul=R({name:"Tr",props:{expand:{type:Boolean},empty:{type:Boolean},checked:{type:Boolean},rowIndex:Number,record:{type:Object,default:()=>({})}},setup(e,{slots:a}){const t=l("table"),n=B((()=>[`${t}-tr`,{[`${t}-tr-expand`]:e.expand,[`${t}-tr-empty`]:e.empty,[`${t}-tr-checked`]:e.checked}]));return()=>{var l,t,o;return _(null!=(o=null==(t=a.tr)?void 0:t.call(a,{rowIndex:e.rowIndex,record:null==(l=e.record)?void 0:l.raw})[0])?o:"tr",{class:n.value},{default:a.default})}}});const il=Symbol("ArcoTable"),sl=Symbol("ArcoTableColumn");function dl(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!oe(e)}var cl=R({name:"AutoTooltip",inheritAttrs:!1,props:{tooltipProps:{type:Object}},setup(e,{attrs:a,slots:t}){const n=l("auto-tooltip"),o=G(),r=G(),u=G(""),i=G(!1),s=()=>{if(o.value&&r.value){const e=r.value.offsetWidth>o.value.offsetWidth;e!==i.value&&(i.value=e)}},d=()=>{var e;(null==(e=r.value)?void 0:e.textContent)&&r.value.textContent!==u.value&&(u.value=r.value.textContent)},c=()=>{d(),s()};te((()=>{d(),s()})),ne((()=>{d(),s()}));const p=()=>_("span",Z({ref:o,class:n},a),[_(fe,{onResize:c},{default:()=>{var e;return[_("span",{ref:r,class:`${n}-content`},[null==(e=t.default)?void 0:e.call(t)])]}})]);return()=>{let l;if(i.value){let l;return _(me,Z({content:u.value,onResize:c},e.tooltipProps),dl(l=p())?l:{default:()=>[l]})}return _(fe,{onResize:c},dl(l=p())?l:{default:()=>[l]})}}}),pl=Object.defineProperty,vl=Object.getOwnPropertySymbols,ml=Object.prototype.hasOwnProperty,fl=Object.prototype.propertyIsEnumerable,hl=(e,l,a)=>l in e?pl(e,l,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[l]=a,yl=(e,l)=>{for(var a in l||(l={}))ml.call(l,a)&&hl(e,a,l[a]);if(vl)for(var a of vl(l))fl.call(l,a)&&hl(e,a,l[a]);return e};function gl(e){return"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!oe(e)}var bl=R({name:"Th",props:{column:{type:Object,default:()=>({})},operations:{type:Array,default:()=>[]},dataColumns:{type:Array,default:()=>[]},resizable:Boolean},setup(e,{slots:a}){const{column:t}=ee(e),n=l("table"),{t:o}=ve(),r=re(il,{}),u=B((()=>{var l;return(null==(l=e.column)?void 0:l.dataIndex)&&r.resizingColumn===e.column.dataIndex})),i=B((()=>{var l;if(c(null==(l=e.column)?void 0:l.tooltip))return e.column.tooltip})),d=B((()=>{var l;return(null==(l=e.column)?void 0:l.filterable)&&p(e.column.filterable.alignLeft)?e.column.filterable.alignLeft:r.filterIconAlignLeft})),{sortOrder:g,hasSorter:b,hasAscendBtn:x,hasDescendBtn:C,nextSortOrder:S,handleClickSorter:w}=(({column:e,tableCtx:l})=>{const a=B((()=>{var a;if(e.value.dataIndex&&e.value.dataIndex===(null==(a=l.sorter)?void 0:a.field))return l.sorter.direction})),t=B((()=>{var l,a,t;return null!=(t=null==(a=null==(l=e.value)?void 0:l.sortable)?void 0:a.sortDirections)?t:[]})),n=B((()=>t.value.length>0)),o=B((()=>t.value.includes("ascend"))),r=B((()=>t.value.includes("descend"))),u=B((()=>{var e,l;return a.value?a.value===t.value[0]&&null!=(l=t.value[1])?l:"":null!=(e=t.value[0])?e:""}));return{sortOrder:a,hasSorter:n,hasAscendBtn:o,hasDescendBtn:r,nextSortOrder:u,handleClickSorter:a=>{var t;e.value.dataIndex&&(null==(t=l.onSorterChange)||t.call(l,e.value.dataIndex,u.value,a))}}})({column:t,tableCtx:r}),{filterPopupVisible:$,isFilterActive:k,isMultipleFilter:j,columnFilterValue:I,handleFilterPopupVisibleChange:z,setFilterValue:P,handleCheckboxFilterChange:A,handleRadioFilterChange:F,handleFilterConfirm:R,handleFilterReset:E}=(({column:e,tableCtx:l})=>{const a=B((()=>{var a;return e.value.dataIndex&&(null==(a=l.filters)?void 0:a[e.value.dataIndex])?l.filters[e.value.dataIndex]:[]})),t=G(!1),n=B((()=>a.value.length>0)),o=B((()=>{var l;return Boolean(null==(l=e.value.filterable)?void 0:l.multiple)})),r=G(a.value);J(a,(e=>{s(e)&&String(e)!==String(r.value)&&(r.value=e)}));const u=e=>{t.value=e},i=e=>{r.value=e};return{filterPopupVisible:t,isFilterActive:n,isMultipleFilter:o,columnFilterValue:r,handleFilterPopupVisibleChange:u,setFilterValue:i,handleCheckboxFilterChange:e=>{i(e)},handleRadioFilterChange:e=>{i([e])},handleFilterConfirm:a=>{var t;e.value.dataIndex&&(null==(t=l.onFilterChange)||t.call(l,e.value.dataIndex,r.value,a)),u(!1)},handleFilterReset:a=>{var t;i([]),e.value.dataIndex&&(null==(t=l.onFilterChange)||t.call(l,e.value.dataIndex,r.value,a)),u(!1)}}})({column:t,tableCtx:r}),K=()=>{var l,a,t,u,i;let s,d;const{filterable:c}=e.column;return(null==(l=e.column.slots)?void 0:l["filter-content"])?null==(a=e.column.slots)?void 0:a["filter-content"]({filterValue:I.value,setFilterValue:P,handleFilterConfirm:R,handleFilterReset:E}):(null==c?void 0:c.slotName)?null==(u=null==(t=null==r?void 0:r.slots)?void 0:t[null==c?void 0:c.slotName])?void 0:u.call(t,{filterValue:I.value,setFilterValue:P,handleFilterConfirm:R,handleFilterReset:E}):(null==c?void 0:c.renderContent)?c.renderContent({filterValue:I.value,setFilterValue:P,handleFilterConfirm:R,handleFilterReset:E}):_("div",{class:`${n}-filters-content`},[_("ul",{class:`${n}-filters-list`},[null==(i=null==c?void 0:c.filters)?void 0:i.map(((e,l)=>{var a;return _("li",{class:`${n}-filters-item`,key:l},[j.value?_($e,{value:e.value,modelValue:I.value,uninjectGroupContext:!0,onChange:A},{default:()=>[e.text]}):_(O,{value:e.value,modelValue:null!=(a=I.value[0])?a:"",uninjectGroupContext:!0,onChange:F},{default:()=>[e.text]})])}))]),_("div",{class:`${n}-filters-bottom`},[_(ke,{size:"mini",onClick:E},gl(s=o("table.resetText"))?s:{default:()=>[s]}),_(ke,{type:"primary",size:"mini",onClick:R},gl(d=o("table.okText"))?d:{default:()=>[d]})])])},N=()=>{const{filterable:l}=e.column;return l?_(he,Z({popupVisible:$.value,trigger:"click",autoFitPosition:!0,popupOffset:d.value?4:0,onPopupVisibleChange:z},l.triggerProps),{default:()=>[_(v,{class:[`${n}-filters`,{[`${n}-filters-active`]:k.value,[`${n}-filters-open`]:$.value,[`${n}-filters-align-left`]:d.value}],disabled:!d.value,onClick:e=>e.stopPropagation()},{default:()=>{var a,t,n,o,r;return[null!=(r=null!=(o=null==(t=null==(a=e.column.slots)?void 0:a["filter-icon"])?void 0:t.call(a))?o:null==(n=l.icon)?void 0:n.call(l))?r:_(m,null,null)]}})],content:K}):null},L=B((()=>{var l,a;const t=[`${n}-cell`,`${n}-cell-align-${null!=(a=null==(l=e.column)?void 0:l.align)?a:e.column.children?"center":"left"}`];return b.value&&t.push(`${n}-cell-with-sorter`,{[`${n}-cell-next-ascend`]:"ascend"===S.value,[`${n}-cell-next-descend`]:"descend"===S.value}),d.value&&t.push(`${n}-cell-with-filter`),t})),T=()=>{var l,t,n,o,u,i;return a.default?a.default():(null==(l=e.column)?void 0:l.titleSlotName)&&(null==(t=r.slots)?void 0:t[e.column.titleSlotName])?null==(o=(n=r.slots)[e.column.titleSlotName])?void 0:o.call(n,{column:e.column}):(null==(i=null==(u=e.column)?void 0:u.slots)?void 0:i.title)?e.column.slots.title():y(e.column.title)?e.column.title():e.column.title},M=()=>{var l,a,t;let o;return _("span",{class:L.value,onClick:b.value?w:void 0},[(null==(l=e.column)?void 0:l.ellipsis)&&(null==(a=e.column)?void 0:a.tooltip)?_(cl,{class:`${n}-th-title`,tooltipProps:i.value},gl(o=T())?o:{default:()=>[o]}):_("span",{class:[`${n}-th-title`,{[`${n}-text-ellipsis`]:null==(t=e.column)?void 0:t.ellipsis}]},[T()]),b.value&&_("span",{class:`${n}-sorter`},[x.value&&_("div",{class:[`${n}-sorter-icon`,{[`${n}-sorter-icon-active`]:"ascend"===g.value}]},[_(f,null,null)]),C.value&&_("div",{class:[`${n}-sorter-icon`,{[`${n}-sorter-icon-active`]:"descend"===g.value}]},[_(h,null,null)])]),d.value&&N()])},D=B((()=>{var l,a;return yl(yl(yl({},Qe(e.column,{dataColumns:e.dataColumns,operations:e.operations})),null==(l=e.column)?void 0:l.cellStyle),null==(a=e.column)?void 0:a.headerCellStyle)})),W=B((()=>{var l,a;return[`${n}-th`,{[`${n}-col-sorted`]:Boolean(g.value),[`${n}-th-resizing`]:u.value},...Xe(n,e.column),null==(l=e.column)?void 0:l.cellClass,null==(a=e.column)?void 0:a.headerCellClass]})),V=l=>{var a,t,n;(null==(a=e.column)?void 0:a.dataIndex)&&(null==(n=r.onThMouseDown)||n.call(r,null==(t=e.column)?void 0:t.dataIndex,l))};return()=>{var l,t,o,r;const u=null!=(l=e.column.colSpan)?l:1,i=null!=(t=e.column.rowSpan)?t:1;return _(null!=(r=null==(o=a.th)?void 0:o.call(a,{column:e.column})[0])?r:"th",{class:W.value,style:D.value,colspan:u>1?u:void 0,rowspan:i>1?i:void 0},{default:()=>[M(),!d.value&&N(),e.resizable&&_("span",{class:`${n}-column-handle`,onMousedown:V},null)]})}}}),xl=Object.defineProperty,Cl=Object.getOwnPropertySymbols,Sl=Object.prototype.hasOwnProperty,wl=Object.prototype.propertyIsEnumerable,$l=(e,l,a)=>l in e?xl(e,l,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[l]=a,kl=(e,l)=>{for(var a in l||(l={}))Sl.call(l,a)&&$l(e,a,l[a]);if(Cl)for(var a of Cl(l))wl.call(l,a)&&$l(e,a,l[a]);return e};var jl=R({name:"Td",props:{rowIndex:Number,record:{type:Object,default:()=>({})},column:{type:Object,default:()=>({})},type:{type:String,default:"normal"},operations:{type:Array,default:()=>[]},dataColumns:{type:Array,default:()=>[]},colSpan:{type:Number,default:1},rowSpan:{type:Number,default:1},isFixedExpand:{type:Boolean,default:!1},containerWidth:{type:Number},showExpandBtn:{type:Boolean,default:!1},indentSize:{type:Number,default:0},renderExpandBtn:{type:Function},summary:{type:Boolean,default:!1}},setup(e,{slots:a}){const t=l("table"),n=B((()=>{var l;if(c(null==(l=e.column)?void 0:l.tooltip))return e.column.tooltip})),o=B((()=>{var l,a;return(null==(l=e.column)?void 0:l.dataIndex)&&(null==(a=p.sorter)?void 0:a.field)===e.column.dataIndex})),r=B((()=>{var l;return(null==(l=e.column)?void 0:l.dataIndex)&&p.resizingColumn===e.column.dataIndex})),u=()=>{var l,a,t,n,o,r;return e.summary?y(null==(l=e.column)?void 0:l.summaryCellClass)?e.column.summaryCellClass(null==(a=e.record)?void 0:a.raw):null==(t=e.column)?void 0:t.summaryCellClass:y(null==(n=e.column)?void 0:n.bodyCellClass)?e.column.bodyCellClass(null==(o=e.record)?void 0:o.raw):null==(r=e.column)?void 0:r.bodyCellClass},i=B((()=>{var l;return[`${t}-td`,{[`${t}-col-sorted`]:o.value,[`${t}-td-resizing`]:r.value},...Xe(t,e.column),null==(l=e.column)?void 0:l.cellClass,u()]})),s=B((()=>{var l;const a=Qe(e.column,{dataColumns:e.dataColumns,operations:e.operations}),t=(()=>{var l,a,t,n,o,r;return e.summary?y(null==(l=e.column)?void 0:l.summaryCellStyle)?e.column.summaryCellStyle(null==(a=e.record)?void 0:a.raw):null==(t=e.column)?void 0:t.summaryCellStyle:y(null==(n=e.column)?void 0:n.bodyCellStyle)?e.column.bodyCellStyle(null==(o=e.record)?void 0:o.raw):null==(r=e.column)?void 0:r.bodyCellStyle})();return kl(kl(kl({},a),null==(l=e.column)?void 0:l.cellStyle),t)})),d=B((()=>{if(e.isFixedExpand&&e.containerWidth)return{width:`${e.containerWidth}px`}})),p=re(il,{}),v=()=>{var l,t,n,o,r,u,i,s;if(a.default)return a.default();const d={record:null==(l=e.record)?void 0:l.raw,column:e.column,rowIndex:null!=(t=e.rowIndex)?t:-1};return a.cell?a.cell(d):(null==(n=e.column.slots)?void 0:n.cell)?e.column.slots.cell(d):e.column.render?e.column.render(d):e.column.slotName&&(null==(o=p.slots)?void 0:o[e.column.slotName])?null==(u=(r=p.slots)[e.column.slotName])?void 0:u.call(r,d):String(null!=(s=ye(null==(i=e.record)?void 0:i.raw,e.column.dataIndex))?s:"")},m=G(!1),f=l=>{var a,t;!y(p.loadMore)||(null==(a=e.record)?void 0:a.isLeaf)||(null==(t=e.record)?void 0:t.children)||(m.value=!0,new Promise((l=>{var a;null==(a=p.loadMore)||a.call(p,e.record.raw,l)})).then((l=>{var a;null==(a=p.addLazyLoadData)||a.call(p,l,e.record),m.value=!1}))),l.stopPropagation()},h=()=>{var l,a,o,r,u,i;let s;return _("span",{class:[`${t}-cell`,`${t}-cell-align-${null!=(a=null==(l=e.column)?void 0:l.align)?a:"left"}`,{[`${t}-cell-fixed-expand`]:e.isFixedExpand,[`${t}-cell-expand-icon`]:e.showExpandBtn}],style:d.value},[e.indentSize>0&&_("span",{style:{paddingLeft:`${e.indentSize}px`}},null),e.showExpandBtn&&_("span",{class:`${t}-cell-inline-icon`,onClick:f},[m.value?_(g,null,null):null==(o=e.renderExpandBtn)?void 0:o.call(e,e.record,!1)]),(null==(r=e.column)?void 0:r.ellipsis)&&(null==(u=e.column)?void 0:u.tooltip)?_(cl,{class:`${t}-td-content`,tooltipProps:n.value},(c=s=v(),"function"==typeof c||"[object Object]"===Object.prototype.toString.call(c)&&!oe(c)?s:{default:()=>[s]})):_("span",{class:[`${t}-td-content`,{[`${t}-text-ellipsis`]:null==(i=e.column)?void 0:i.ellipsis}]},[v()])]);var c};return()=>{var l,t,n,o;return _(null!=(o=null==(n=a.td)?void 0:n.call(a,{record:null==(l=e.record)?void 0:l.raw,column:e.column,rowIndex:null!=(t=e.rowIndex)?t:-1})[0])?o:"td",{class:i.value,style:s.value,rowspan:e.rowSpan>1?e.rowSpan:void 0,colspan:e.colSpan>1?e.colSpan:void 0},{default:()=>[h()]})}}}),Ol=R({name:"OperationTh",props:{operationColumn:{type:Object,required:!0},operations:{type:Array,required:!0},rowSpan:{type:Number,default:1},selectAll:{type:Boolean,default:!1}},setup(e){const a=l("table"),t=re(il,{}),n=B((()=>{var e,l,a,n;let o=!1,r=!1;const u=(null!=(l=null==(e=t.currentSelectedRowKeys)?void 0:e.filter((e=>{var l,a;return null==(a=null==(l=t.currentAllEnabledRowKeys)?void 0:l.includes(e))||a})))?l:[]).length,i=null!=(n=null==(a=t.currentAllEnabledRowKeys)?void 0:a.length)?n:0;return u>0&&(u>=i?o=!0:r=!0),{checked:o,indeterminate:r}})),o=B((()=>Ze(e.operationColumn,e.operations))),r=B((()=>[`${a}-th`,`${a}-operation`,{[`${a}-checkbox`]:e.selectAll},...Ye(a,e.operationColumn)]));return()=>_("th",{class:r.value,style:o.value,rowspan:e.rowSpan>1?e.rowSpan:void 0},[_("span",{class:`${a}-cell`},[e.selectAll?_($e,{modelValue:n.value.checked,indeterminate:n.value.indeterminate,uninjectGroupContext:!0,onChange:e=>{var l;null==(l=t.onSelectAll)||l.call(t,e)}},{default:y(e.operationColumn.title)?e.operationColumn.title():e.operationColumn.title}):e.operationColumn.title?y(e.operationColumn.title)?e.operationColumn.title():e.operationColumn.title:null])])}}),Il=R({name:"OperationTd",components:{Checkbox:$e,Radio:O,IconPlus:b,IconMinus:x},props:{operationColumn:{type:Object,required:!0},operations:{type:Array,required:!0},record:{type:Object,required:!0},hasExpand:{type:Boolean,default:!1},selectedRowKeys:{type:Array},renderExpandBtn:{type:Function},colSpan:{type:Number,default:1},rowSpan:{type:Number,default:1},summary:{type:Boolean,default:!1}},emits:["select"],setup(e,{emit:a,slots:t}){const n=l("table"),o=re(il,{}),r=B((()=>Ze(e.operationColumn,e.operations))),u=B((()=>[`${n}-td`,`${n}-operation`,{[`${n}-checkbox`]:"selection-checkbox"===e.operationColumn.name,[`${n}-radio`]:"selection-radio"===e.operationColumn.name,[`${n}-expand`]:"expand"===e.operationColumn.name,[`${n}-drag-handle`]:"drag-handle"===e.operationColumn.name},...Ye(n,e.operationColumn)])),i=B((()=>al(e.record))),s=B((()=>{var e;return((e,l)=>{let a=!1,t=!1;const n=l.filter((l=>e.includes(l)));return n.length>0&&(n.length>=l.length?a=!0:t=!0),{checked:a,indeterminate:t}})(null!=(e=o.currentSelectedRowKeys)?e:[],i.value)})),d=()=>{var l,a,n,r,u,i;if(e.summary)return null;if(e.operationColumn.render)return e.operationColumn.render(e.record.raw);if("selection-checkbox"===e.operationColumn.name){const t=e.record.key;return o.checkStrictly||e.record.isLeaf?_($e,{modelValue:null!=(a=null==(l=e.selectedRowKeys)?void 0:l.includes(t))&&a,disabled:Boolean(e.record.disabled),uninjectGroupContext:!0,onChange:l=>{var a;return null==(a=o.onSelect)?void 0:a.call(o,l,e.record)},onClick:e=>e.stopPropagation()},null):_($e,{modelValue:s.value.checked,indeterminate:s.value.indeterminate,disabled:Boolean(e.record.disabled),uninjectGroupContext:!0,onChange:l=>{var a;return null==(a=o.onSelectAllLeafs)?void 0:a.call(o,e.record,l)},onClick:e=>e.stopPropagation()},null)}if("selection-radio"===e.operationColumn.name){const l=e.record.key;return _(O,{modelValue:null!=(r=null==(n=e.selectedRowKeys)?void 0:n.includes(l))&&r,disabled:Boolean(e.record.disabled),uninjectGroupContext:!0,onChange:l=>{var a;return null==(a=o.onSelect)?void 0:a.call(o,l,e.record)},onClick:e=>e.stopPropagation()},null)}return"expand"===e.operationColumn.name?e.hasExpand&&e.renderExpandBtn?e.renderExpandBtn(e.record):null:"drag-handle"===e.operationColumn.name?null!=(i=null==(u=t["drag-handle-icon"])?void 0:u.call(t))?i:_(C,null,null):null};return()=>_("td",{class:u.value,style:r.value,rowspan:e.rowSpan>1?e.rowSpan:void 0,colspan:e.colSpan>1?e.colSpan:void 0},[_("span",{class:`${n}-cell`},[d()])])}});const zl=e=>{var l;const a={};for(const t of e)t.dataIndex&&(null==(l=t.filterable)?void 0:l.defaultFilteredValue)&&(a[t.dataIndex]=t.filterable.defaultFilteredValue);return a},Pl=e=>{var l;for(const a of e)if(a.dataIndex&&(null==(l=a.sortable)?void 0:l.defaultSortOrder))return{field:a.dataIndex,direction:a.sortable.defaultSortOrder}},Al=({spanMethod:e,data:l,columns:a})=>{const t=(l,o)=>{null==l||l.forEach(((r,u)=>{var i;r.hasSubtree&&(null==(i=r.children)?void 0:i.length)&&t(r.children||[],o),a.value.forEach(((t,i)=>{var s,d;const{rowspan:c=1,colspan:p=1}=null!=(d=null==(s=e.value)?void 0:s.call(e,{record:r.raw,column:t,rowIndex:u,columnIndex:i}))?d:{};(c>1||p>1)&&(o[`${u}-${i}-${r.key}`]=[c,p],Array.from({length:c}).forEach(((e,t)=>{var o;if(u+t<l.length){const{key:e}=null!=(o=l[u+t])?o:{};Array.from({length:p}).forEach(((l,o)=>{i+o<a.value.length&&`${u}-${i}-${r.key}`!=`${u+t}-${i+o}-${e}`&&(n.value[`${u+t}-${i+o}-${e}`]=[0,0])}))}})))}))}))};let n=G({});return{tableSpan:B((()=>{const a={};return n.value={},e.value&&t(l.value,a),a})),removedCells:B((()=>{const e=[];for(const l of Object.keys(n.value))e.push(l);return e}))}};var Fl=Object.defineProperty,Rl=Object.defineProperties,Bl=Object.getOwnPropertyDescriptors,El=Object.getOwnPropertySymbols,Kl=Object.prototype.hasOwnProperty,Nl=Object.prototype.propertyIsEnumerable,Ll=(e,l,a)=>l in e?Fl(e,l,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[l]=a,Tl=(e,l)=>{for(var a in l||(l={}))Kl.call(l,a)&&Ll(e,a,l[a]);if(El)for(var a of El(l))Nl.call(l,a)&&Ll(e,a,l[a]);return e},Ml=(e,l)=>Rl(e,Bl(l));const Dl={wrapper:!0,cell:!1,headerCell:!1,bodyCell:!1};var Wl=R({name:"Table",props:{columns:{type:Array,default:()=>[]},data:{type:Array,default:()=>[]},bordered:{type:[Boolean,Object],default:!0},hoverable:{type:Boolean,default:!0},stripe:{type:Boolean,default:!1},size:{type:String,default:()=>{var e,l;return null!=(l=null==(e=re(k,void 0))?void 0:e.size)?l:"large"}},tableLayoutFixed:{type:Boolean,default:!1},loading:{type:[Boolean,Object],default:!1},rowSelection:{type:Object},expandable:{type:Object},scroll:{type:Object},pagination:{type:[Boolean,Object],default:!0},pagePosition:{type:String,default:"br"},indentSize:{type:Number,default:16},rowKey:{type:String,default:"key"},showHeader:{type:Boolean,default:!0},virtualListProps:{type:Object},spanMethod:{type:Function},spanAll:{type:Boolean,default:!1},components:{type:Object},loadMore:{type:Function},filterIconAlignLeft:{type:Boolean,default:!1},hideExpandButtonOnEmpty:{type:Boolean,default:!1},rowClass:{type:[String,Array,Object,Function]},draggable:{type:Object},rowNumber:{type:[Boolean,Object]},columnResizable:{type:Boolean},summary:{type:[Boolean,Function]},summaryText:{type:String,default:"Summary"},summarySpanMethod:{type:Function},selectedKeys:{type:Array},defaultSelectedKeys:{type:Array},expandedKeys:{type:Array},defaultExpandedKeys:{type:Array},defaultExpandAllRows:{type:Boolean,default:!1},stickyHeader:{type:[Boolean,Number],default:!1},scrollbar:{type:[Object,Boolean],default:!0},showEmptyTree:{type:Boolean,default:!1}},emits:{"update:selectedKeys":e=>!0,"update:expandedKeys":e=>!0,expand:(e,l)=>!0,expandedChange:e=>!0,select:(e,l,a)=>!0,selectAll:e=>!0,selectionChange:e=>!0,sorterChange:(e,l)=>!0,filterChange:(e,l)=>!0,pageChange:e=>!0,pageSizeChange:e=>!0,change:(e,l,a)=>!0,cellMouseEnter:(e,l,a)=>!0,cellMouseLeave:(e,l,a)=>!0,cellClick:(e,l,a)=>!0,rowClick:(e,l)=>!0,headerClick:(e,l)=>!0,columnResize:(e,l)=>!0,rowDblclick:(e,l)=>!0,cellDblclick:(e,l,a)=>!0,rowContextmenu:(e,l)=>!0,cellContextmenu:(e,l,a)=>!0},setup(e,{emit:a,slots:t}){const{columns:n,rowKey:r,rowSelection:u,expandable:p,loadMore:v,filterIconAlignLeft:m,selectedKeys:f,defaultSelectedKeys:h,expandedKeys:g,defaultExpandedKeys:C,defaultExpandAllRows:j,spanMethod:O,draggable:R,summarySpanMethod:E,scrollbar:K,showEmptyTree:N}=ee(e),L=l("table"),T=re(k,void 0),M=B((()=>c(e.bordered)?Tl(Tl({},Dl),e.bordered):Ml(Tl({},Dl),{wrapper:e.bordered}))),{children:D,components:W}=Oe("TableColumn"),V=B((()=>{var e,l;return null==(l=null==(e=u.value)?void 0:e.checkStrictly)||l})),{displayScrollbar:H,scrollbarProps:q}=z(K),U=B((()=>{var l,a,t,n;return{x:Boolean((null==(l=e.scroll)?void 0:l.x)||(null==(a=e.scroll)?void 0:a.minWidth)),y:Boolean((null==(t=e.scroll)?void 0:t.y)||(null==(n=e.scroll)?void 0:n.maxHeight))}})),X=G(),Q=G({}),{componentRef:ae,elementRef:ne}=P("containerRef"),{componentRef:se,elementRef:de}=P("containerRef"),{componentRef:ce,elementRef:ve}=P("viewportRef"),{componentRef:me,elementRef:fe}=P("containerRef"),he=B((()=>xe.value?wa.value?ve.value:de.value:ne.value)),xe=B((()=>U.value.y||e.stickyHeader||wa.value||U.value.x&&0===la.value.length)),Ce=le(new Map),$e=G();J([W,Ce],(([e,l])=>{if(e.length>0){const a=[];e.forEach((e=>{const t=l.get(e);t&&a.push(t)})),$e.value=a}else $e.value=void 0}));const ke=new Map,Ie=G([]),ze=G([]),{resizingColumn:Pe,columnWidth:Ae,handleThMouseDown:Fe}=((e,l)=>{const a=G(""),t=le({}),n=()=>{a.value="",w(window,"mousemove",o),w(window,"mouseup",n),w(window,"contextmenu",n)},o=n=>{const o=e.value[a.value];if(o){const{clientX:e}=n,{x:r}=o.getBoundingClientRect();let u=Math.ceil(e-r);u<40&&(u=40),t[a.value]=u,l("columnResize",a.value,u)}};return{resizingColumn:a,columnWidth:t,handleThMouseDown:(e,l)=>{l.preventDefault(),a.value=e,S(window,"mousemove",o),S(window,"mouseup",n),S(window,"contextmenu",n)},handleThMouseUp:n}})(Q,a);J([n,$e,Ae],(([e,l])=>{var a;const t=((e,l,a)=>{const t=qe(e);l.clear();const n=[],o=[...Array(t)].map((()=>[]));let r,u;const c=(e,{level:p=0,parent:v,fixed:m}={})=>{var f,h;for(const y of e){const e=(h=Ve({},y),Ne(h,Le({parent:v})));if(s(e.children)){const l=He(e.children);l>1&&(e.colSpan=l),o[p].push(e),c(e.children,{level:p+1,parent:e,fixed:e.fixed})}else{const s=t-p;s>1&&(e.rowSpan=s),(m||e.fixed)&&(e.fixed=null!=(f=e.fixed)?f:m,"left"===e.fixed?r=n.length:i(u)&&(u=n.length)),(i(e.dataIndex)||d(e.dataIndex))&&(e.dataIndex=`__arco_data_index_${n.length}`),a[e.dataIndex]&&(e._resizeWidth=a[e.dataIndex]),l.set(e.dataIndex,e),n.push(e),o[p].push(e)}}};return c(e),i(r)||(n[r].isLastLeftFixed=!0,_e(n[r],"left")),i(u)||(n[u].isFirstRightFixed=!0,_e(n[u],"right")),{dataColumns:n,groupColumns:o}})(null!=(a=null!=l?l:e)?a:[],ke,Ae);Ie.value=t.dataColumns,ze.value=t.groupColumns}),{immediate:!0,deep:!0});const Re=B((()=>["tl","top","tr"].includes(e.pagePosition))),Be=G(!1),Ke=G(!1),Te=G(!1);ue((()=>{var l,a,t;let n=!1,o=!1,r=!1;((null==(l=e.rowSelection)?void 0:l.fixed)||(null==(a=e.expandable)?void 0:a.fixed)||(null==(t=e.draggable)?void 0:t.fixed))&&(n=!0);for(const e of Ie.value)"left"===e.fixed?(n=!0,r=!0):"right"===e.fixed&&(o=!0);n!==Be.value&&(Be.value=n),o!==Ke.value&&(Ke.value=o),r!==Te.value&&(Te.value=r)}));const Me=B((()=>{for(const e of Ie.value)if(e.ellipsis)return!0;return!1})),De=e=>{const l={type:e,page:Yl.value,pageSize:Xl.value,sorter:Ze.value,filters:Ue.value,dragTarget:"drag"===e?Nl.data:void 0};a("change",aa.value,l,Ul.value)},We=(e,l)=>{Je.value=Ml(Tl({},Ue.value),{[e]:l}),a("filterChange",e,l),De("filter")},Ge=(e,l)=>{Qe.value=l?{field:e,direction:l}:void 0,a("sorterChange",e,l),De("sorter")},{_filters:Je,computedFilters:Ue,resetFilters:Ye,clearFilters:Xe}=(({columns:e,onFilterChange:l})=>{const a=G(zl(e.value));J(e,(e=>{const l=zl(e);I(l,a.value)||(a.value=l)}));const t=B((()=>{var l,t;const n={};for(const o of e.value)if(o.dataIndex){const e=null!=(t=null==(l=o.filterable)?void 0:l.filteredValue)?t:a.value[o.dataIndex];e&&(n[o.dataIndex]=e)}return n}));return{_filters:a,computedFilters:t,resetFilters:t=>{var n;const o=t?[].concat(t):[],r={};for(const a of e.value)if(a.dataIndex&&a.filterable&&(0===o.length||o.includes(a.dataIndex))){const e=null!=(n=a.filterable.defaultFilteredValue)?n:[];r[a.dataIndex]=e,l(a.dataIndex,e)}a.value=r},clearFilters:t=>{const n=t?[].concat(t):[],o={};for(const a of e.value)if(a.dataIndex&&a.filterable&&(0===n.length||n.includes(a.dataIndex))){const e=[];o[a.dataIndex]=e,l(a.dataIndex,e)}a.value=o}}})({columns:Ie,onFilterChange:We}),{_sorter:Qe,computedSorter:Ze,resetSorters:sl,clearSorters:dl}=(({columns:e,onSorterChange:l})=>{const a=G(Pl(e.value));J(e,(e=>{const l=Pl(e);I(l,a.value)||(a.value=l)}));const t=B((()=>{var l;for(const t of e.value)if(t.dataIndex&&t.sortable){const e=$(t.sortable.sortOrder)?t.sortable.sortOrder:(null==(l=a.value)?void 0:l.field)===t.dataIndex?a.value.direction:"";if(e)return{field:t.dataIndex,direction:e}}}));return{_sorter:a,computedSorter:t,resetSorters:()=>{var t;let n;for(const a of e.value)a.dataIndex&&a.sortable&&(!n&&a.sortable.defaultSortOrder&&(n={field:a.dataIndex,direction:a.sortable.defaultSortOrder}),l(a.dataIndex,null!=(t=a.sortable.defaultSortOrder)?t:""));a.value=n},clearSorters:()=>{for(const a of e.value)a.dataIndex&&a.sortable&&l(a.dataIndex,"")}}})({columns:Ie,onSorterChange:Ge}),cl=new Set,pl=B((()=>{const l=[];cl.clear();const a=e=>{if(s(e)&&e.length>0)for(const t of e)l.push(t[r.value]),t.disabled&&cl.add(t[r.value]),t.children&&a(t.children)};return a(e.data),l})),vl=B((()=>{const e=[],l=a=>{for(const t of a)e.push(t.key),t.children&&l(t.children)};return l(la.value),e})),ml=B((()=>{const e=[],l=a=>{for(const t of a)t.disabled||e.push(t.key),t.children&&l(t.children)};return l(la.value),e})),{isRadio:fl,selectedRowKeys:hl,currentSelectedRowKeys:yl,handleSelect:gl,handleSelectAllLeafs:xl,handleSelectAll:Cl,select:Sl,selectAll:wl,clearSelected:$l}=(({selectedKeys:e,defaultSelectedKeys:l,rowSelection:a,currentAllRowKeys:t,currentAllEnabledRowKeys:n,emit:o})=>{var r,u,i;const s=B((()=>{var e;return"radio"===(null==(e=a.value)?void 0:e.type)})),d=G(null!=(i=null!=(u=l.value)?u:null==(r=a.value)?void 0:r.defaultSelectedRowKeys)?i:[]),c=B((()=>{var l,t,n;return null!=(n=null!=(t=e.value)?t:null==(l=a.value)?void 0:l.selectedRowKeys)?n:d.value})),p=B((()=>c.value.filter((e=>t.value.includes(e)))));return{isRadio:s,selectedRowKeys:c,currentSelectedRowKeys:p,handleSelectAll:e=>{const l=tl(c.value,n.value,!e);d.value=l,o("selectAll",e),o("selectionChange",l),o("update:selectedKeys",l)},handleSelect:(e,l)=>{const a=s.value?[l.key]:tl(c.value,[l.key],!e);d.value=a,o("select",a,l.key,l.raw),o("selectionChange",a),o("update:selectedKeys",a)},handleSelectAllLeafs:(e,l)=>{const a=tl(c.value,al(e),!l);d.value=a,o("select",a,e.key,e.raw),o("selectionChange",a),o("update:selectedKeys",a)},select:(e,l=!0)=>{const a=[].concat(e),t=s.value?a:tl(c.value,a,!l);d.value=t,o("selectionChange",t),o("update:selectedKeys",t)},selectAll:(e=!0)=>{const l=tl(c.value,n.value,!e);d.value=l,o("selectionChange",l),o("update:selectedKeys",l)},clearSelected:()=>{d.value=[],o("selectionChange",[]),o("update:selectedKeys",[])}}})({selectedKeys:f,defaultSelectedKeys:h,rowSelection:u,currentAllRowKeys:vl,currentAllEnabledRowKeys:ml,emit:a}),{expandedRowKeys:kl,handleExpand:Fl,expand:Rl,expandAll:Bl}=(({expandedKeys:e,defaultExpandedKeys:l,defaultExpandAllRows:a,expandable:t,allRowKeys:n,emit:o})=>{const r=G(l.value?l.value:(null==(u=t.value)?void 0:u.defaultExpandedRowKeys)?t.value.defaultExpandedRowKeys:a.value||(null==(i=t.value)?void 0:i.defaultExpandAllRows)?[...n.value]:[]);var u,i;const s=B((()=>{var l,a,n;return null!=(n=null!=(a=e.value)?a:null==(l=t.value)?void 0:l.expandedRowKeys)?n:r.value}));return{expandedRowKeys:s,handleExpand:(e,l)=>{const a=s.value.includes(e)?s.value.filter((l=>e!==l)):s.value.concat(e);r.value=a,o("expand",e,l),o("expandedChange",a),o("update:expandedKeys",a)},expand:(e,l=!0)=>{const a=[].concat(e),t=l?s.value.concat(a):s.value.filter((e=>!a.includes(e)));r.value=t,o("expandedChange",t),o("update:expandedKeys",t)},expandAll:(e=!0)=>{const l=e?[...n.value]:[];r.value=l,o("expandedChange",l),o("update:expandedKeys",l)}}})({expandedKeys:g,defaultExpandedKeys:C,defaultExpandAllRows:j,expandable:p,allRowKeys:pl,emit:a}),El=le({}),{dragType:Kl,dragState:Nl,handleDragStart:Ll,handleDragEnter:Wl,handleDragLeave:Vl,handleDragover:Hl,handleDragEnd:ql,handleDrop:_l}=(e=>{const l=B((()=>{if(e.value)return"handle"===e.value.type?"handle":"row"})),a=le({dragging:!1,sourceKey:"",sourcePath:[],targetPath:[],data:{}}),t=()=>{a.dragging=!1,a.sourceKey="",a.sourcePath=[],a.targetPath=[],a.data={}};return{dragType:l,dragState:a,handleDragStart:(e,l,t,n)=>{if(e.dataTransfer&&(e.dataTransfer.effectAllowed="move",e.target&&"TD"===e.target.tagName)){const{parentElement:l}=e.target;l&&"TR"===l.tagName&&e.dataTransfer.setDragImage(l,0,0)}a.dragging=!0,a.sourceKey=l,a.sourcePath=t,a.targetPath=[...t],a.data=n},handleDragEnter:(e,l)=>{e.dataTransfer&&(e.dataTransfer.dropEffect="move"),a.targetPath.toString()!==l.toString()&&(a.targetPath=l),e.preventDefault()},handleDragLeave:e=>{},handleDragover:e=>{e.dataTransfer&&(e.dataTransfer.dropEffect="move"),e.preventDefault()},handleDragEnd:e=>{var l;"none"===(null==(l=e.dataTransfer)?void 0:l.dropEffect)&&t()},handleDrop:e=>{t(),e.preventDefault()}}})(R),Gl=B((()=>{var l;const a=l=>{const t=[];for(const n of l){const l={raw:n,key:n[e.rowKey],disabled:n.disabled,expand:n.expand,isLeaf:n.isLeaf};n.children?(l.isLeaf=!1,l.children=a(n.children)):e.loadMore&&!n.isLeaf?(l.isLeaf=!1,El[l.key]&&(l.children=a(El[l.key]))):l.isLeaf=!0,l.hasSubtree=Boolean(l.children?!e.hideExpandButtonOnEmpty||l.children.length>0:e.loadMore&&!l.isLeaf),t.push(l)}return t};return a(null!=(l=e.data)?l:[])})),Jl=B((()=>{const e=l=>l.filter((l=>!!(e=>{var l,a;for(const t of Object.keys(Ue.value)){const n=Ue.value[t],o=ke.get(t);if(o&&(null==(l=o.filterable)?void 0:l.filter)&&n.length>0){const l=null==(a=o.filterable)?void 0:a.filter(n,e.raw);if(!l)return l}}return!0})(l)&&(l.children&&(l.children=e(l.children)),!0)));return Object.keys(Ue.value).length>0?e(Gl.value):Gl.value})),Ul=B((()=>{var e,l,a;const t=el(Jl.value);if(t.length>0){if(null==(e=Ze.value)?void 0:e.field){const e=ke.get(Ze.value.field);if(e&&!0!==(null==(l=e.sortable)?void 0:l.sorter)){const{field:l,direction:a}=Ze.value;t.sort(((t,n)=>{var o;const r=ye(t.raw,l),u=ye(n.raw,l);if((null==(o=e.sortable)?void 0:o.sorter)&&y(e.sortable.sorter))return e.sortable.sorter(t.raw,n.raw,{dataIndex:l,direction:a});const i=r>u?1:-1;return"descend"===a?-i:i}))}}const{sourcePath:n,targetPath:o}=Nl;if(Nl.dragging&&o.length&&o.toString()!==n.toString()&&n.length===o.length&&n.slice(0,-1).toString()===o.slice(0,-1).toString()){let e=t;for(let l=0;l<n.length;l++){const t=n[l];if(l>=n.length-1){const a=e[t],n=o[l];n>t?(e.splice(n+1,0,a),e.splice(t,1)):(e.splice(n,0,a),e.splice(t+1,1))}else e=null!=(a=e[t].children)?a:[]}}}return t})),{page:Yl,pageSize:Xl,handlePageChange:Ql,handlePageSizeChange:Zl}=((e,l)=>{var a,t;const n=G(c(e.pagination)&&null!=(a=e.pagination.defaultCurrent)?a:1),o=G(c(e.pagination)&&null!=(t=e.pagination.defaultPageSize)?t:10),r=B((()=>{var l;return c(e.pagination)&&null!=(l=e.pagination.pageSize)?l:o.value}));return{page:B((()=>{var l;return c(e.pagination)&&null!=(l=e.pagination.current)?l:n.value})),pageSize:r,handlePageChange:e=>{n.value=e,l("pageChange",e)},handlePageSizeChange:e=>{o.value=e,l("pageSizeChange",e)}}})(e,a),ea=B((()=>{var e,l;return null!=(l=null==(e=u.value)?void 0:e.onlyCurrent)&&l}));J(Yl,((e,l)=>{e!==l&&ea.value&&$l()}));const la=B((()=>e.pagination&&Ul.value.length>Xl.value?Ul.value.slice((Yl.value-1)*Xl.value,Yl.value*Xl.value):Ul.value)),aa=B((()=>ll(la.value))),ta=l=>l&&l.length>0?l.map((l=>({raw:l,key:l[e.rowKey]}))):[],na=B((()=>e.summary?y(e.summary)?ta(e.summary({columns:Ie.value,data:aa.value})):ta([Ie.value.reduce(((l,a,t)=>{if(a.dataIndex)if(0===t)ge(l,a.dataIndex,e.summaryText,{addPath:!0});else{let e=0,t=!1;la.value.forEach((l=>{if(a.dataIndex){const n=ye(l.raw,a.dataIndex);o(n)?e+=n:i(n)||d(n)||(t=!0)}})),ge(l,a.dataIndex,t?"":e,{addPath:!0})}return l}),{})]):[])),oa=G(0),ra=G(!0),ua=G(!0),ia=()=>{let e=!0,l=!0;const a=he.value;a&&(e=0===oa.value,l=Math.ceil(oa.value+a.offsetWidth)>=a.scrollWidth),e!==ra.value&&(ra.value=e),l!==ua.value&&(ua.value=l)},sa=e=>{e.target.scrollLeft!==oa.value&&(oa.value=e.target.scrollLeft),ia()},da=e=>{sa(e);const{scrollLeft:l}=e.target;fe.value&&(fe.value.scrollLeft=l),X.value&&(X.value.scrollLeft=l)},ca=(e,l)=>{a("rowClick",e.raw,l)},pa=(e,l,t)=>{a("cellClick",e.raw,l,t)},va=F(((e,l,t)=>{a("cellMouseEnter",e.raw,l,t)}),30),ma=F(((e,l,t)=>{a("cellMouseLeave",e.raw,l,t)}),30),fa=(e,l,t)=>{a("cellDblclick",e.raw,l,t)},ha=(e,l,t)=>{a("cellContextmenu",e.raw,l,t)},ya=B((()=>{var l,a;const t=[],n=Be.value||Ke.value;let o,r,u;"handle"===(null==(l=e.draggable)?void 0:l.type)&&(o={name:"drag-handle",title:e.draggable.title,width:e.draggable.width,fixed:e.draggable.fixed||n},t.push(o)),e.expandable&&(r={name:"expand",title:e.expandable.title,width:e.expandable.width,fixed:e.expandable.fixed||n},t.push(r)),e.rowSelection&&(u={name:"radio"===e.rowSelection.type?"selection-radio":"selection-checkbox",title:e.rowSelection.title,width:e.rowSelection.width,fixed:e.rowSelection.fixed||n},t.push(u)),!Te.value&&t.length>0&&t[t.length-1].fixed&&(t[t.length-1].isLastLeftFixed=!0);const i=null==(a=e.components)?void 0:a.operations;return y(i)?i({dragHandle:o,expand:r,selection:u}):t})),ga=B((()=>{var l,a,t,n;if(U.value.x){const r={width:o(null==(l=e.scroll)?void 0:l.x)?`${null==(a=e.scroll)?void 0:a.x}px`:null==(t=e.scroll)?void 0:t.x};return(null==(n=e.scroll)?void 0:n.minWidth)&&(r.minWidth=o(e.scroll.minWidth)?`${e.scroll.minWidth}px`:e.scroll.minWidth),r}})),ba=B((()=>{var l,a,t,n;if(U.value.x&&la.value.length>0){const r={width:o(null==(l=e.scroll)?void 0:l.x)?`${null==(a=e.scroll)?void 0:a.x}px`:null==(t=e.scroll)?void 0:t.x};return(null==(n=e.scroll)?void 0:n.minWidth)&&(r.minWidth=o(e.scroll.minWidth)?`${e.scroll.minWidth}px`:e.scroll.minWidth),r}}));ie(il,le({loadMore:v,addLazyLoadData:(e,l)=>{e&&(El[l.key]=e)},slots:t,sorter:Ze,filters:Ue,filterIconAlignLeft:m,resizingColumn:Pe,checkStrictly:V,currentAllEnabledRowKeys:ml,currentSelectedRowKeys:yl,addColumn:(e,l)=>{Ce.set(e,l)},removeColumn:e=>{Ce.delete(e)},onSelectAll:Cl,onSelect:gl,onSelectAllLeafs:xl,onSorterChange:Ge,onFilterChange:We,onThMouseDown:Fe}));const xa=B((()=>[L,`${L}-size-${e.size}`,{[`${L}-border`]:M.value.wrapper,[`${L}-border-cell`]:M.value.cell,[`${L}-border-header-cell`]:!M.value.cell&&M.value.headerCell,[`${L}-border-body-cell`]:!M.value.cell&&M.value.bodyCell,[`${L}-stripe`]:e.stripe,[`${L}-hover`]:e.hoverable,[`${L}-dragging`]:Nl.dragging,[`${L}-type-selection`]:Boolean(e.rowSelection),[`${L}-empty`]:e.data&&0===la.value.length,[`${L}-layout-fixed`]:e.tableLayoutFixed||U.value.x||xe.value||Me.value}])),Ca=B((()=>[`${L}-pagination`,{[`${L}-pagination-left`]:"tl"===e.pagePosition||"bl"===e.pagePosition,[`${L}-pagination-center`]:"top"===e.pagePosition||"bottom"===e.pagePosition,[`${L}-pagination-right`]:"tr"===e.pagePosition||"br"===e.pagePosition,[`${L}-pagination-top`]:Re.value}])),Sa=B((()=>{const e=(()=>{const e=[];return Be.value&&e.push(`${L}-has-fixed-col-left`),Ke.value&&e.push(`${L}-has-fixed-col-right`),e})();return U.value.x&&e.push(ra.value&&ua.value?`${L}-scroll-position-both`:ra.value?`${L}-scroll-position-left`:ua.value?`${L}-scroll-position-right`:`${L}-scroll-position-middle`),xe.value&&e.push(`${L}-scroll-y`),e})),wa=B((()=>Boolean(e.virtualListProps))),$a=G({}),ka=()=>{const e={};for(const l of Object.keys(Q.value))e[l]=Q.value[l].offsetWidth;$a.value=e},ja=G(!1),Oa=()=>!!de.value&&de.value.offsetWidth>de.value.clientWidth,Ia=()=>{const e=Oa();ja.value!==e&&(ja.value=e),ia(),ka()};te((()=>{ja.value=Oa(),ka()}));const za=B((()=>c(e.loading)?e.loading:{loading:e.loading})),Pa=()=>_(ul,{empty:!0},{default:()=>[_(jl,{colSpan:Ie.value.length+ya.value.length},{default:()=>{var e,l,a,n,o;return[null!=(o=null!=(n=null==(e=t.empty)?void 0:e.call(t))?n:null==(a=null==T?void 0:(l=T.slots).empty)?void 0:a.call(l,{component:"table"}))?o:_(we,null,null)]}})]}),Aa=B((()=>[].concat(ya.value,Ie.value))),Fa=B((()=>e.spanAll?Aa.value:Ie.value)),{tableSpan:Ra,removedCells:Ba}=Al({spanMethod:O,data:la,columns:Fa}),{tableSpan:Ea,removedCells:Ka}=Al({spanMethod:E,data:na,columns:Aa}),Na=e=>{if(wa.value&&e&&$a.value[e])return{width:`${$a.value[e]}px`}},La=()=>na.value&&na.value.length>0?_("tfoot",null,[na.value.map(((l,a)=>{return n=l,_(ul,{key:`table-summary-${o=a}`,class:[`${L}-tr-summary`,y(e.rowClass)?e.rowClass(n.raw,o):e.rowClass],onClick:e=>ca(n,e)},{default:()=>[ya.value.map(((e,l)=>{var a;const t=`${o}-${l}-${n.key}`,[r,u]=null!=(a=Ea.value[t])?a:[1,1];if(Ka.value.includes(t))return null;const i=Na(e.name);return _(Il,{style:i,operationColumn:e,operations:ya.value,record:n,rowSpan:r,colSpan:u,summary:!0},null)})),Ie.value.map(((e,l)=>{var a;const r=`${o}-${ya.value.length+l}-${n.key}`,[u,i]=null!=(a=Ea.value[r])?a:[1,1];if(Ka.value.includes(r))return null;const s=Na(e.dataIndex);return _(jl,{key:`td-${r}`,style:s,rowIndex:o,record:n,column:e,operations:ya.value,dataColumns:Ie.value,rowSpan:u,colSpan:i,summary:!0,onClick:l=>pa(n,e,l),onDblclick:l=>fa(n,e,l),onMouseenter:l=>va(n,e,l),onMouseleave:l=>ma(n,e,l),onContextmenu:l=>ha(n,e,l)},{td:t.td,cell:t["summary-cell"]})}))],tr:t.tr});var n,o}))]):null,Ta=(l,a=!0)=>{var n,o,r,u,i;const s=l.key,d=kl.value.includes(s);return _("button",{type:"button",class:`${L}-expand-btn`,onClick:e=>{Fl(s,l.raw),a&&e.stopPropagation()}},[null!=(i=null!=(u=null==(n=t["expand-icon"])?void 0:n.call(t,{expanded:d,record:l.raw}))?u:null==(r=null==(o=e.expandable)?void 0:o.icon)?void 0:r.call(o,d,l.raw))?i:_(d?x:b,null,null)])},Ma=(e,{indentSize:l,indexPath:a,allowDrag:t,expandContent:n})=>{var o,r;if(e.hasSubtree)return 0===(null==(o=e.children)?void 0:o.length)&&N.value?Pa():null==(r=e.children)?void 0:r.map(((e,n)=>Da(e,n,{indentSize:l,indexPath:a,allowDrag:t})));if(n){const l=he.value;return _(ul,{key:`${e.key}-expand`,expand:!0},{default:()=>{return[_(jl,{isFixedExpand:Be.value||Ke.value,containerWidth:null==l?void 0:l.clientWidth,colSpan:Ie.value.length+ya.value.length},(e=n,"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!oe(e)?n:{default:()=>[n]}))];var e}})}return null},Da=(l,n,{indentSize:o=0,indexPath:r,allowDrag:u=!0}={})=>{var i;const s=l.key,d=(null!=r?r:[]).concat(n),c=(l=>{var a;return l.expand?y(l.expand)?l.expand():l.expand:t["expand-row"]?t["expand-row"]({record:l.raw}):(null==(a=e.expandable)?void 0:a.expandedRowRender)?e.expandable.expandedRowRender(l.raw):void 0})(l),p=kl.value.includes(s),v=Nl.sourceKey===l.key,m=Kl.value?{draggable:u,onDragstart:e=>{u&&Ll(e,l.key,d,l.raw)},onDragend:e=>{u&&ql(e)}}:{},f=Kl.value?{onDragenter:e=>{u&&Wl(e,d)},onDragover:e=>{u&&Hl(e)},onDrop:e=>{u&&(De("drag"),_l(e))}}:{};return _(Y,null,[_(ul,Z({key:s,class:[{[`${L}-tr-draggable`]:"row"===Kl.value,[`${L}-tr-drag`]:v},y(e.rowClass)?e.rowClass(l.raw,n):e.rowClass],rowIndex:n,record:l,checked:e.rowSelection&&(null==(i=hl.value)?void 0:i.includes(s)),onClick:e=>ca(l,e),onDblclick:e=>((e,l)=>{a("rowDblclick",e.raw,l)})(l,e),onContextmenu:e=>((e,l)=>{a("rowContextmenu",e.raw,l)})(l,e)},"row"===Kl.value?m:{},f),{default:()=>[ya.value.map(((a,o)=>{var r;const u=`${n}-${o}-${l.key}`,[i,s]=e.spanAll&&null!=(r=Ra.value[u])?r:[1,1];if(e.spanAll&&Ba.value.includes(u))return null;const d=Na(a.name);return _(Il,Z({key:`operation-td-${o}`,style:d,operationColumn:a,operations:ya.value,record:l,hasExpand:Boolean(c),selectedRowKeys:yl.value,rowSpan:i,colSpan:s,renderExpandBtn:Ta},"handle"===Kl.value?m:{}),{"drag-handle-icon":t["drag-handle-icon"]})})),Ie.value.map(((a,r)=>{var u;const i=`${n}-${e.spanAll?ya.value.length+r:r}-${l.key}`,[s,d]=null!=(u=Ra.value[i])?u:[1,1];if(Ba.value.includes(i))return null;const c=0===r?{showExpandBtn:l.hasSubtree,indentSize:l.hasSubtree?o-20:o}:{},p=Na(a.dataIndex);return _(jl,Z({key:`td-${r}`,style:p,rowIndex:n,record:l,column:a,operations:ya.value,dataColumns:Ie.value,rowSpan:s,renderExpandBtn:Ta,colSpan:d},c,{onClick:e=>pa(l,a,e),onDblclick:e=>fa(l,a,e),onMouseenter:e=>va(l,a,e),onMouseleave:e=>ma(l,a,e),onContextmenu:e=>ha(l,a,e)}),{td:t.td})}))],tr:t.tr}),p&&Ma(l,{indentSize:o+e.indentSize,indexPath:d,allowDrag:u&&!v,expandContent:c})])},Wa=()=>{const e=la.value.some((e=>Boolean(e.hasSubtree)));return _(rl,null,{default:()=>[la.value.length>0?la.value.map(((l,a)=>Da(l,a,{indentSize:e?20:0}))):Pa()],tbody:t.tbody})},Va=()=>_(ol,null,{default:()=>[ze.value.map(((l,n)=>_(ul,{key:`header-row-${n}`},{default:()=>[0===n&&ya.value.map(((l,a)=>{var t;return _(Ol,{key:`operation-th-${a}`,ref:e=>{(null==e?void 0:e.$el)&&l.name&&(Q.value[l.name]=e.$el)},operationColumn:l,operations:ya.value,selectAll:Boolean("selection-checkbox"===l.name&&(null==(t=e.rowSelection)?void 0:t.showCheckedAll)),rowSpan:ze.value.length},null)})),l.map(((n,o)=>{const r=e.columnResizable&&Boolean(n.dataIndex)&&o<l.length-1;return _(bl,{key:`th-${o}`,ref:e=>{(null==e?void 0:e.$el)&&n.dataIndex&&(Q.value[n.dataIndex]=e.$el)},column:n,operations:ya.value,dataColumns:Ie.value,resizable:r,onClick:e=>((e,l)=>{a("headerClick",e,l)})(n,e)},{th:t.th})}))]})))],thead:t.thead}),Ha=()=>{var l,a;if(xe.value){const t=o(e.stickyHeader)?`${e.stickyHeader}px`:void 0,n=[null==(l=q.value)?void 0:l.outerClass];e.stickyHeader&&n.push(`${L}-header-sticky`);const r=Tl({top:t},null==(a=q.value)?void 0:a.outerStyle),u=H.value?Se:"div";return _(Y,null,[e.showHeader&&_(u,Z({ref:me,class:[`${L}-header`,{[`${L}-header-sticky`]:e.stickyHeader&&!H.value}],style:{overflowY:ja.value?"scroll":void 0,top:H.value?void 0:t}},K.value?Ml(Tl({hide:0!==la.value.length,disableVertical:!0},q.value),{outerClass:n,outerStyle:r}):void 0),{default:()=>[_("table",{class:`${L}-element`,style:ga.value,cellpadding:0,cellspacing:0},[_(nl,{dataColumns:Ie.value,operations:ya.value,columnWidth:Ae},null),Va()])]}),_(je,{onResize:Ia},{default:()=>{var l,a;return[wa.value&&la.value.length?_(A,Z({ref:e=>{(null==e?void 0:e.$el)&&(de.value=e.$el)},class:`${L}-body`,data:la.value,itemKey:"_key",component:{list:"table",content:"tbody"},listAttrs:{class:`${L}-element`,style:ba.value},paddingPosition:"list",height:"auto"},e.virtualListProps,{onScroll:da}),{item:({item:e,index:l})=>Da(e,l)}):_(u,Z({ref:se,class:`${L}-body`,style:{maxHeight:o(null==(l=e.scroll)?void 0:l.y)?`${null==(a=e.scroll)?void 0:a.y}px`:"100%"}},K.value?Tl({outerStyle:{display:"flex",minHeight:"0"}},q.value):void 0,{onScroll:da}),{default:()=>[_("table",{class:`${L}-element`,style:ba.value,cellpadding:0,cellspacing:0},[0!==la.value.length&&_(nl,{dataColumns:Ie.value,operations:ya.value,columnWidth:Ae},null),Wa()])]})]}}),na.value&&na.value.length>0&&_("div",{ref:X,class:`${L}-tfoot`,style:{overflowY:ja.value?"scroll":"hidden"}},[_("table",{class:`${L}-element`,style:ba.value,cellpadding:0,cellspacing:0},[_(nl,{dataColumns:Ie.value,operations:ya.value,columnWidth:Ae},null),La()])])])}return _(je,{onResize:()=>ia()},{default:()=>[_("table",{class:`${L}-element`,cellpadding:0,cellspacing:0,style:ba.value},[_(nl,{dataColumns:Ie.value,operations:ya.value,columnWidth:Ae},null),e.showHeader&&Va(),Wa(),na.value&&na.value.length>0&&La()])]})},qa=l=>{var a;const n=(null==(a=e.scroll)?void 0:a.maxHeight)?{maxHeight:e.scroll.maxHeight}:void 0,o=H.value?Se:"div";return _(Y,null,[_("div",{class:[`${L}-container`,Sa.value]},[_(o,Z({ref:ae,class:[`${L}-content`,{[`${L}-content-scroll-x`]:!xe.value}],style:n},K.value?Tl({outerStyle:{height:"100%"}},q.value):void 0,{onScroll:sa}),{default:()=>[l?_("table",{class:`${L}-element`,cellpadding:0,cellspacing:0},[l()]):Ha()]})]),t.footer&&_("div",{class:`${L}-footer`},[t.footer()])])},_a=()=>{var l,a;const n=c(e.pagination)?be(e.pagination,["current","pageSize","defaultCurrent","defaultPageSize"]):{};return _("div",{class:Ca.value},[null==(l=t["pagination-left"])?void 0:l.call(t),_(Ee,Z({total:Jl.value.length,current:Yl.value,pageSize:Xl.value,onChange:e=>{Ql(e),De("pagination")},onPageSizeChange:e=>{Zl(e),De("pagination")}},n),null),null==(a=t["pagination-right"])?void 0:a.call(t)])},Ga=B((()=>{var l,a;if($(null==(l=e.scroll)?void 0:l.y))return{height:null==(a=e.scroll)?void 0:a.y}}));return{render:()=>{var l;return t.default?_("div",{class:xa.value},[qa(t.default)]):(D.value=null==(l=t.columns)?void 0:l.call(t),_("div",{class:xa.value,style:Ga.value},[D.value,_(pe,za.value,{default:()=>[!1!==e.pagination&&(la.value.length>0||Ul.value.length>0)&&Re.value&&_a(),qa(),!1!==e.pagination&&(la.value.length>0||Ul.value.length>0)&&!Re.value&&_a()]})]))},selfExpand:Rl,selfExpandAll:Bl,selfSelect:Sl,selfSelectAll:wl,selfResetFilters:Ye,selfClearFilters:Xe,selfResetSorters:sl,selfClearSorters:dl}},methods:{selectAll(e){return this.selfSelectAll(e)},select(e,l){return this.selfSelect(e,l)},expandAll(e){return this.selfExpandAll(e)},expand(e,l){return this.selfExpand(e,l)},resetFilters(e){return this.selfResetFilters(e)},clearFilters(e){return this.selfClearFilters(e)},resetSorters(){return this.selfResetSorters()},clearSorters(){return this.selfClearSorters()}},render(){return this.render()}});const Vl=(e,l)=>{const a=se(e,l),t=G(a.value);return J(a,((e,l)=>{I(e,l)||(t.value=e)})),t};var Hl=R({name:"TableColumn",props:{dataIndex:String,title:String,width:Number,minWidth:Number,align:{type:String},fixed:{type:String},ellipsis:{type:Boolean,default:!1},sortable:{type:Object,default:void 0},filterable:{type:Object,default:void 0},cellClass:{type:[String,Array,Object]},headerCellClass:{type:[String,Array,Object]},bodyCellClass:{type:[String,Array,Object,Function]},summaryCellClass:{type:[String,Array,Object,Function]},cellStyle:{type:Object},headerCellStyle:{type:Object},bodyCellStyle:{type:[Object,Function]},summaryCellStyle:{type:[Object,Function]},index:{type:Number},tooltip:{type:[Boolean,Object],default:!1}},setup(e,{slots:l}){var a;const{dataIndex:t,title:n,width:o,align:r,fixed:u,ellipsis:i,index:s,minWidth:d}=ee(e),c=Vl(e,"sortable"),p=Vl(e,"filterable"),v=Vl(e,"cellClass"),m=Vl(e,"headerCellClass"),f=Vl(e,"bodyCellClass"),h=Vl(e,"summaryCellClass"),y=Vl(e,"cellStyle"),g=Vl(e,"headerCellStyle"),b=Vl(e,"bodyCellStyle"),x=Vl(e,"summaryCellStyle"),C=Vl(e,"tooltip"),S=ce(),w=re(il,{}),$=re(sl,void 0),{children:k,components:j}=Oe("TableColumn"),O=le(new Map);ie(sl,{addChild:(e,l)=>{O.set(e,l)},removeChild:e=>{O.delete(e)}});const I=G();J([j,O],(([e,l])=>{if(e.length>0){const a=[];e.forEach((e=>{const t=l.get(e);t&&a.push(t)})),I.value=a}else I.value=void 0}));const z=le({dataIndex:t,title:n,width:o,minWidth:d,align:r,fixed:u,ellipsis:i,sortable:c,filterable:p,cellClass:v,headerCellClass:m,bodyCellClass:f,summaryCellClass:h,cellStyle:y,headerCellStyle:g,bodyCellStyle:b,summaryCellStyle:x,index:s,tooltip:C,children:I,slots:l});return S&&($?$.addChild(S.uid,z):null==(a=w.addColumn)||a.call(w,S.uid,z)),de((()=>{var e;S&&($?$.removeChild(S.uid):null==(e=w.removeColumn)||e.call(w,S.uid))})),()=>{var e;return k.value=null==(e=l.default)?void 0:e.call(l),k.value}}});const ql=Object.assign(Wl,{Thead:ol,Tbody:rl,Tr:ul,Th:bl,Td:jl,Column:Hl,install:(e,l)=>{r(e,l);const a=u(l);e.component(a+Wl.name,Wl),e.component(a+ol.name,ol),e.component(a+rl.name,rl),e.component(a+ul.name,ul),e.component(a+bl.name,bl),e.component(a+jl.name,jl),e.component(a+Hl.name,Hl)}});export{Ee as P,Hl as T,ql as a};

import{h as e,_ as t,g as r,k as s,a5 as a}from"./index-D-8JbLQk.js";import{r as n,c as l,o,G as i,d as u,t as c,a3 as p,B as m,j as d,k as v,m as f,l as y,O as b,i as x,p as g}from"./vue-D-10XvVk.js";import{p as $}from"./pick-Ccd8Sfcm.js";var h=Object.defineProperty,j=Object.defineProperties,O=Object.getOwnPropertyDescriptors,w=Object.getOwnPropertySymbols,N=Object.prototype.hasOwnProperty,k=Object.prototype.propertyIsEnumerable,E=(e,t,r)=>t in e?h(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;const S=["xxl","xl","lg","md","sm","xs"],L={xs:"(max-width: 575px)",sm:"(min-width: 576px)",md:"(min-width: 768px)",lg:"(min-width: 992px)",xl:"(min-width: 1200px)",xxl:"(min-width: 1600px)"};let P=[],A=-1,q={};const B={matchHandlers:{},dispatch:(e,t)=>(q=e,!(P.length<1)&&(P.forEach((e=>{e.func(q,t)})),!0)),subscribe(e){0===P.length&&this.register();const t=(++A).toString();return P.push({token:t,func:e}),e(q,null),t},unsubscribe(e){P=P.filter((t=>t.token!==e)),0===P.length&&this.unregister()},unregister(){Object.keys(L).forEach((e=>{const t=L[e];if(!t)return;const r=this.matchHandlers[t];r&&r.mql&&r.listener&&(r.mql.removeEventListener?r.mql.removeEventListener("change",r.listener):r.mql.removeListener(r.listener))}))},register(){Object.keys(L).forEach((e=>{const t=L[e];if(!t)return;const r=({matches:t})=>{var r;this.dispatch((r=((e,t)=>{for(var r in t||(t={}))N.call(t,r)&&E(e,r,t[r]);if(w)for(var r of w(t))k.call(t,r)&&E(e,r,t[r]);return e})({},q),j(r,O({[e]:t}))),e)},s=window.matchMedia(t);s.addEventListener?s.addEventListener("change",r):s.addListener(r),this.matchHandlers[t]={mql:s,listener:r},r(s)}))}};function C(t){return e(t)}function I(e,t,r=!1){const s=n({xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0}),a=l((()=>{let a=t;if(C(e.value))for(let t=0;t<S.length;t++){const n=S[t];if((s.value[n]||"xs"===n&&r)&&void 0!==e.value[n]){a=e.value[n];break}}else a=e.value;return a}));let u="";return o((()=>{u=B.subscribe((t=>{C(e.value)&&(s.value=t)}))})),i((()=>{u&&B.unsubscribe(u)})),a}const R=Symbol("RowContextInjectionKey"),G=Symbol("GridContextInjectionKey"),H=Symbol("GridDataCollectorInjectionKey");var K=t(u({name:"Row",props:{gutter:{type:[Number,Object,Array],default:0},justify:{type:String,default:"start"},align:{type:String,default:"start"},div:{type:Boolean},wrap:{type:Boolean,default:!0}},setup(e){const{gutter:t,align:s,justify:a,div:n,wrap:o}=c(e),i=r("row"),u=l((()=>({[`${i}`]:!n.value,[`${i}-nowrap`]:!o.value,[`${i}-align-${s.value}`]:s.value,[`${i}-justify-${a.value}`]:a.value}))),d=l((()=>Array.isArray(t.value)?t.value[0]:t.value)),v=l((()=>Array.isArray(t.value)?t.value[1]:0)),f=I(d,0),y=I(v,0),b=l((()=>{const e={};if((f.value||y.value)&&!n.value){const t=-f.value/2,r=-y.value/2;t&&(e.marginLeft=`${t}px`,e.marginRight=`${t}px`),r&&(e.marginTop=`${r}px`,e.marginBottom=`${r}px`)}return e})),x=l((()=>[f.value,y.value]));return p(R,m({gutter:x,div:n})),{classNames:u,styles:b}}}),[["render",function(e,t,r,s,a,n){return d(),v("div",{class:y(e.classNames),style:b(e.styles)},[f(e.$slots,"default")],6)}]]);var D=Object.defineProperty,T=Object.getOwnPropertySymbols,M=Object.prototype.hasOwnProperty,_=Object.prototype.propertyIsEnumerable,z=(e,t,r)=>t in e?D(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,F=(e,t)=>{for(var r in t||(t={}))M.call(t,r)&&z(e,r,t[r]);if(T)for(var r of T(t))_.call(t,r)&&z(e,r,t[r]);return e};var J=t(u({name:"Col",props:{span:{type:Number,default:24},offset:{type:Number},order:{type:Number},xs:{type:[Number,Object]},sm:{type:[Number,Object]},md:{type:[Number,Object]},lg:{type:[Number,Object]},xl:{type:[Number,Object]},xxl:{type:[Number,Object]},flex:{type:[Number,String]}},setup(t){const n=r("col"),o=x(R,{}),i=l((()=>function(e){return a(e)&&(["initial","auto","none"].includes(e)||/^\d+$/.test(e))||s(e)?e:a(e)&&/^\d+(px|em|rem|%)$/.test(e)?`0 0 ${e}`:void 0}(t.flex))),u=l((()=>{const{div:r}=o,{span:a,offset:l,order:i,xs:u,sm:c,md:p,lg:m,xl:d,xxl:v}=t,f={[`${n}`]:!r,[`${n}-order-${i}`]:i,[`${n}-${a}`]:!(r||u||c||p||m||d||v),[`${n}-offset-${l}`]:l&&l>0},y={xs:u,sm:c,md:p,lg:m,xl:d,xxl:v};return Object.keys(y).forEach((t=>{const r=y[t];r&&s(r)?f[`${n}-${t}-${r}`]=!0:r&&e(r)&&(f[`${n}-${t}-${r.span}`]=r.span,f[`${n}-${t}-offset-${r.offset}`]=r.offset,f[`${n}-${t}-order-${r.order}`]=r.order)})),f})),c=l((()=>i.value?n:u.value)),p=l((()=>{const{gutter:e,div:t}=o,r={};if(Array.isArray(e)&&!t){const t=e[0]&&e[0]/2||0,s=e[1]&&e[1]/2||0;t&&(r.paddingLeft=`${t}px`,r.paddingRight=`${t}px`),s&&(r.paddingTop=`${s}px`,r.paddingBottom=`${s}px`)}return r})),m=l((()=>i.value?{flex:i.value}:{})),d=l((()=>$(t,S))),v=function(t){return l((()=>{const{val:r,key:a,xs:n,sm:l,md:o,lg:i,xl:u,xxl:c}=t.value;if(!(n||l||o||i||u||c))return r;const p={};return S.forEach((r=>{const n=t.value[r];s(n)?p[r]=n:e(n)&&s(n[a])&&(p[r]=n[a])})),p}))}(l((()=>F({val:t.span,key:"span"},d.value)))),f=I(v,24,!0);return{visible:l((()=>!!f.value)),classNames:c,styles:l((()=>F(F({},p.value),m.value)))}}}),[["render",function(e,t,r,s,a,n){return e.visible?(d(),v("div",{key:0,class:y(e.classNames),style:b(e.styles)},[f(e.$slots,"default")],6)):g("v-if",!0)}]]);export{J as C,G,K as R,H as a,B as r,I as u};

import"./index-DOhy6BH_.js";import"./index-BEo1tUsK.js";import{F as e,a as l}from"./index-DVDXfQhn.js";import{T as o}from"./index-DiBSSeoD.js";import{I as s,f as r}from"./index-DDFSMqsG.js";import{S as a}from"./index-Cuq5XRs0.js";import"./index-DD6vSYIM.js";import"./index-DQjhgQFu.js";import{u as d}from"./useCommon-BuUbRw8e.js";import{u as i}from"./index-SGXyeHZ_.js";import{d as n,o as m,K as t,j as u,k as p,A as c,z as f,q as h,u as j,y as $,p as x}from"./vue-D-10XvVk.js";import"./index-D-8JbLQk.js";import"./pick-Ccd8Sfcm.js";import"./index-DGtjsHgS.js";import"./resize-observer-Dtogi-DJ.js";import"./ResizeObserver.es-CzGuHLZU.js";import"./index-CdWxsKz_.js";import"./apiCommon-DcubqwY_.js";import"./index-DdMaxvYa.js";import"./index-DfEXMvnc.js";import"./use-children-components-v8i8lsOx.js";import"./index-DmW4RN1x.js";/* empty css              */import"./useLoading-D5mh7tTu.js";import"./usePagination-Dd_EW2BO.js";import"./dayjs.min-Daes5FZc.js";import"./index-O7pr3qsq.js";const w={class:"overflow-y-scroll no-scrollbar"},V=n({__name:"info",props:{id:{}},setup(n,{expose:V}){const P=n,{roleOptions:k,initRoleOptions:b}=d(),{detail:_,form:y}=i();m((()=>{b(),P.id&&_(P.id)}));const g=t("formRef");return V({formRef:g,form:y}),(d,i)=>{const n=a,m=e,t=s,V=r,P=o,b=l;return u(),p("div",w,[c(b,{ref_key:"formRef",ref:g,model:j(y),"auto-label-width":""},{default:f((()=>[i[6]||(i[6]=h("input",{type:"text",class:"fake-input"},null,-1)),i[7]||(i[7]=h("input",{type:"password",class:"fake-input"},null,-1)),c(m,{"show-colon":"",label:"所属角色",field:"roleId",rules:[{required:!0,message:`${d.$selectPlaceholder}所属角色`}]},{default:f((()=>[c(n,{modelValue:j(y).roleId,"onUpdate:modelValue":i[0]||(i[0]=e=>j(y).roleId=e),options:j(k),placeholder:`${d.$selectPlaceholder}所属角色`},null,8,["modelValue","options","placeholder"])])),_:1},8,["rules"]),c(m,{"show-colon":"",label:"用户名称",field:"nickname",rules:[{required:!0,message:`${d.$inputPlaceholder}用户名称`}]},{default:f((()=>[c(t,{modelValue:j(y).nickname,"onUpdate:modelValue":i[1]||(i[1]=e=>j(y).nickname=e),placeholder:`${d.$inputPlaceholder}用户名称`},null,8,["modelValue","placeholder"])])),_:1},8,["rules"]),c(m,{"show-colon":"",label:"登录账号",field:"username",rules:[{required:!0,message:`${d.$inputPlaceholder}登录账号`}]},{default:f((()=>[c(t,{modelValue:j(y).username,"onUpdate:modelValue":i[2]||(i[2]=e=>j(y).username=e),placeholder:`${d.$inputPlaceholder}登录账号`},null,8,["modelValue","placeholder"])])),_:1},8,["rules"]),d.id?x("",!0):(u(),$(m,{key:0,"show-colon":"",label:"登录密码",field:"password",rules:[{required:!0,message:`${d.$inputPlaceholder}登录密码`}]},{default:f((()=>[c(V,{modelValue:j(y).password,"onUpdate:modelValue":i[3]||(i[3]=e=>j(y).password=e),placeholder:`${d.$inputPlaceholder}登录密码`},null,8,["modelValue","placeholder"])])),_:1},8,["rules"])),d.id?x("",!0):(u(),$(m,{key:1,"show-colon":"",label:"确认密码",field:"confirmPassword",rules:[{required:!0,message:`${d.$inputPlaceholder}确认密码`}]},{default:f((()=>[c(V,{modelValue:j(y).confirmPassword,"onUpdate:modelValue":i[4]||(i[4]=e=>j(y).confirmPassword=e),placeholder:`${d.$inputPlaceholder}确认密码`},null,8,["modelValue","placeholder"])])),_:1},8,["rules"])),c(m,{"show-colon":"",label:"备注",field:"remark"},{default:f((()=>[c(P,{modelValue:j(y).remark,"onUpdate:modelValue":i[5]||(i[5]=e=>j(y).remark=e),"auto-size":{minRows:5},"max-length":50,"show-word-limit":"",placeholder:`${d.$inputPlaceholder}备注`},null,8,["modelValue","placeholder"])])),_:1})])),_:1},8,["model"])])}}});export{V as default};

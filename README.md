# mall-store 平台管理端(admin-platform)

## 项目简介

mall-store平台管理端是一个基于Vue 3、TypeScript和Vite构建的电商平台管理系统。该系统提供了完善的电商平台管理功能，包括商品管理、订单管理、用户管理、权限管理等。

## 环境要求

- Node.js版本: v16.20.2
- 包管理工具: yarn 或 npm

## 项目结构

```
src/
├── api/            # API接口定义
├── assets/         # 静态资源文件
├── components/     # 公共组件
├── config/         # 项目配置
├── hooks/          # Vue自定义hooks
├── layout/         # 布局组件
├── pages/          # 页面组件
├── router/         # 路由配置
├── store/          # 状态管理（Pinia）
├── types/          # TypeScript类型定义
├── typings/        # 类型声明文件
├── utils/          # 工具函数
├── App.vue         # 根组件
└── main.ts         # 入口文件
```

## 技术栈

- 前端框架: Vue 3
- 构建工具: Vite
- 语言: TypeScript
- UI组件库: Arco Design
- 状态管理: Pinia
- 路由: Vue Router 4
- HTTP客户端: Axios
- 数据可视化: ECharts
- CSS预处理器: LESS/SASS
- 实用工具库: Lodash, Day.js

## 开发命令

```bash
# 安装依赖
yarn install 或 npm install

# 开发环境运行
yarn dev 或 npm run dev

# 生产环境预览
yarn dev:prod 或 npm run dev:prod

# 无支付功能的生产环境预览
yarn dev:prod:noPay 或 npm run dev:prod:noPay

# 构建生产环境
yarn build:prod 或 npm run build:prod

# 构建无支付功能的生产环境
yarn build:prod:noPay 或 npm run build:prod:noPay

# 本地预览生产构建
yarn preview 或 npm run preview
```

## 主要功能模块

- 平台概览：系统数据统计与分析
- 商户管理：商户信息管理、审核、权限控制
- 商品管理：商品分类、商品上下架、商品属性管理
- 订单管理：订单处理、退款管理、物流跟踪
- 用户管理：用户信息、用户行为分析
- 系统设置：权限配置、系统参数配置
- 营销管理：优惠券、活动策划、促销方案

## 开发规范

- 组件命名采用PascalCase格式
- API请求统一在api目录下管理
- 使用TypeScript类型声明
- 遵循ESLint代码规范
- 使用Prettier进行代码格式化

## 推荐IDE设置

- VS Code + Volar + TypeScript Vue Plugin (Volar)

{"name": "mall-admin", "description": "mall-admin", "private": true, "author": "<EMAIL>", "version": "1.0.0", "license": "Apache-2.0", "type": "module", "scripts": {"dev": "vite --config ./config/vite.config.dev.ts", "dev:prod": "vite --mode production --config ./config/vite.config.dev.ts", "dev:prod:noPay": "vite --mode productionnopay --config ./config/vite.config.dev.ts", "build:prod": "vite build --config ./config/vite.config.prod.ts", "build:prod:noPay": "vite build --mode productionnopay --config ./config/vite.config.prod.ts", "build:prod:noPay_rky": "vite build --mode productionnopay_rky --config ./config/vite.config.prod.ts", "preview": "vite preview"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@better-scroll/core": "^2.5.0", "@vue-office/docx": "^1.6.2", "@vue-office/excel": "^1.7.11", "@vue-office/pdf": "^2.0.2", "@vueuse/core": "^11.0.3", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "ali-oss": "^6.20.0", "axios": "^1.7.7", "colorpicker-v3": "^2.10.2", "dayjs": "^1.11.13", "decimal.js": "^10.4.3", "downloadjs": "^1.4.7", "echarts": "^5.6.0", "exceljs": "^4.4.0", "lodash": "^4.17.21", "mitt": "^3.0.0", "pinia": "^2.2.2", "qiniu-js": "^3.4.1", "qrcode.vue": "^3.3.4", "socket.io-client": "^4.8.1", "ts-md5": "^1.3.1", "uuid": "^10.0.0", "vue": "^3.5.0", "vue-demi": "^0.14.10", "vue-echarts": "^7.0.3", "vue-router": "4"}, "devDependencies": {"@arco-design/web-vue": "^2.55.3", "@types/ali-oss": "^6.16.11", "@types/downloadjs": "^1.4.3", "@types/lodash": "^4.14.191", "@types/node": "^18.14.6", "@types/uuid": "^10.0.0", "@vitejs/plugin-vue": "^4.0.0", "autoprefixer": "^10.4.13", "less": "^4.1.3", "postcss": "^8.4.21", "prettier": "^2.8.4", "rollup-plugin-visualizer": "^5.9.0", "sass": "^1.58.3", "tailwindcss": "^3.2.7", "typescript": "^5.5.4", "unplugin-auto-import": "^0.15.1", "unplugin-vue-components": "^0.24.1", "vite": "^5.4.3", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.0", "vue-tsc": "^2.1.4"}}
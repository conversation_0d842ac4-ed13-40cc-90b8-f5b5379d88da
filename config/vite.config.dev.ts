import baseConfig from './vite.config.base'
import { mergeConfig } from 'vite'

export default ({ mode }) => {
  return mergeConfig(
    {
      server: {
        host: '0.0.0.0',
        port: 8997,
        open: false,
        proxy: {
          '/api': {
            target: 'http://127.0.0.1:8999',
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/api/, '')
          }
        },
        cors: true
      }
    },
    baseConfig({ mode })
  )
}

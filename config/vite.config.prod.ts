import baseConfig from './vite.config.base'
import { mergeConfig } from 'vite'
// 打包分析工具
import { visualizer } from 'rollup-plugin-visualizer'
// GZIP压缩
import viteCompression from 'vite-plugin-compression'

export default ({ mode }) => {
  return mergeConfig(
    {
      plugins: [
        // 生成打包分析
        visualizer({
          // 打包后自动打开分析页面
          open: false
        }),
        viteCompression({
          // filter：过滤器，对哪些类型的文件进行压缩，默认为 ‘/.(js|mjs|json|css|html)$/i’
          // verbose: true：是否在控制台输出压缩结果，默认为 true
          // threshold：启用压缩的文件大小限制，单位是字节，默认为 0
          // disable: false：是否禁用压缩，默认为 false
          // deleteOriginFile：压缩后是否删除原文件，默认为 false
          // algorithm：采用的压缩算法，默认是 gzip
          // ext：生成的压缩包后缀
          // threshold: 1024000,
          filter: /.(js|mjs|json|css)$/i
          // deleteOriginFile: true
        })
      ],
      // 打包配置
      build: {
        outDir: 'dist_prod',
        rollupOptions: {
          output: {
            // 分包
            manualChunks: {
              vue: ['vue', 'vue-router', 'pinia', '@vueuse/core'],
              chart: ['echarts', 'vue-echarts']
              // office: ['@vue-office/docx', '@vue-office/excel', '@vue-office/pdf']
            }
          }
        },
        // 打包体积预警大小
        chunkSizeWarningLimit: 2000,
        // css拆分
        cssCodeSplit: true,
        // 不生成sourcemap
        sourcemap: false,
        // 是否禁用最小化混淆，esbuild打包速度最快，terser打包体积最小
        minify: 'terser',
        // 图片小于该值将打包成Base64
        assetsInlineLimit: 4000
      }
    },
    baseConfig({ mode })
  )
}

const logger = ({ mode, title, env }: { mode: string; title: string; env: any }) => {
  console.log(
    [
      '                   _ooOoo_',
      '                  o8888888o',
      '                  88" . "88',
      '                  (| -_- |)',
      '                  O\\  =  /O',
      "               ____/`---'\\____",
      "             .'  \\\\|     |//  `.",
      '            /  \\\\|||  :  |||//  \\',
      '           /  _||||| -:- |||||-  \\',
      '           |   | \\\\\\  -  /// |   |',
      "           | \\_|  ''\\---/''  |   |",
      '           \\  .-\\__  `-`  ___/-. /',
      "         ___`. .'  /--.--\\  `. . __",
      '      ."" \'<  `.___\\_<|>_/___.\'  >\'"".',
      '     | | :  `- \\`.;`\\ _ /`;.`/ - ` : | |',
      '     \\  \\ `-.   \\_ __\\ /__ _/   .-` /  /',
      "======`-.____`-.___\\_____/___.-`____.-'======",
      "                   `=---='",
      '^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^',
      '             佛祖保佑    永无BUG'
    ].join('\r\n')
  )
  console.table([
    ['title', title],
    ['auth', env.npm_package_author_name],
    ['version', env.npm_package_version],
    ['packageName', env.npm_package_name],
    ['mode', mode]
  ])
}
export default logger

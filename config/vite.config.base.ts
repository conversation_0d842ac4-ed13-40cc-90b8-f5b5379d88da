import logger from './logger'
import { defineConfig, loadEnv } from 'vite'
import { resolve } from 'path'
import vue from '@vitejs/plugin-vue'
import { createHtmlPlugin } from 'vite-plugin-html'
//引入自动引入插件
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ArcoResolver } from 'unplugin-vue-components/resolvers'

// https://vitejs.dev/config/
export default ({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  const title = env.VITE_TITLE
  logger({ mode, title, env: process.env })
  return defineConfig({
    base: env.VITE_BASE_PATH,
    plugins: [
      vue(),
      createHtmlPlugin({
        // 是否压缩 html
        minify: true,
        // 在这里写entry后，你将不需要在`index.html`内添加 script 标签，原有标签需要删除
        // entry: 'src/main.ts',
        // 如果你想将 `index.html`存放在指定文件夹，可以修改它，否则不需要配置 @default index.html
        // template: 'public/index.html',
        // 需要注入 index.html ejs 模版的数据
        inject: {
          data: {
            title,
            injectScript: ''
          }
        }
      }),
      // 自动导入
      AutoImport({
        imports: ['vue'],
        dts: 'src/auto-import.d.ts',
        resolvers: [ArcoResolver()]
      }),
      Components({
        resolvers: [
          ArcoResolver({
            importStyle: 'less',
            sideEffect: true
          })
        ]
      })
    ],
    css: {
      preprocessorOptions: {
        scss: {
          // Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.
          silenceDeprecations: ['legacy-js-api']
        },
        less: {
          // 如果使用了按需加载的方式引入组件，请确保在按需加载插件中开启了 less 样式文件的导入。
          modifyVars: {
            'arcoblue-6': '#1890ff'
            // 'color-menu-light-bg': '#2B3C57',
            // 'color-text-2': '#fff'
            // hack: `true; @import (reference) "${resolve('src/assets/style/variables.less')}";`
          },
          javascriptEnabled: true
        }
      }
    },
    resolve: {
      alias: {
        '@': resolve(__dirname, '../src')
      },
      extensions: ['.ts', '.js']
    }
  })
}
